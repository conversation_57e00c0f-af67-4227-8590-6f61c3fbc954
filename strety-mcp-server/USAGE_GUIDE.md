# Strety MCP Server Usage Guide

This guide provides detailed examples and best practices for using the Strety MCP Server.

## Table of Contents
- [Authentication Setup](#authentication-setup)
- [Tool Reference](#tool-reference)
- [Common Use Cases](#common-use-cases)
- [Integration Examples](#integration-examples)
- [Troubleshooting](#troubleshooting)

## Authentication Setup

### Getting OAuth2 Credentials

1. **Log into your Strety account**
2. **Navigate to API settings** (usually under Settings > Integrations or Developer)
3. **Create a new OAuth2 application**
4. **Note down your Client ID and Client Secret**
5. **Configure redirect URI** (for web applications)

### Environment Configuration

Create a `.env` file with your credentials:

```env
# Server Configuration
PORT=3000
LOG_LEVEL=info

# OAuth2 Configuration (Recommended)
OAUTH_CLIENT_ID_OAUTH2=your_client_id_here
OAUTH_CLIENT_SECRET_OAUTH2=your_client_secret_here
OAUTH_SCOPES_OAUTH2=read,write

# Alternative: Pre-acquired Token
# OAUTH_TOKEN_OAUTH2=your_existing_token_here

# Alternative: Bearer Token
# BEARER_TOKEN_BEARERAUTH=your_bearer_token_here
```

## Tool Reference

### Goals Management

#### listGoals
Retrieve a paginated list of goals with optional filtering.

**Parameters:**
- `page[size]` (optional): Number of results per page (1-100, default: 20)
- `page[number]` (optional): Page number (default: 1)
- `filter[created_after]` (optional): ISO 8601 datetime
- `filter[updated_after]` (optional): ISO 8601 datetime
- `filter[ids][]` (optional): Array of goal UUIDs
- `filter[assignee_id]` (optional): Person UUID
- `filter[parent_id]` (optional): Parent goal UUID
- `include` (optional): "latest_check_ins"
- `limit_check_ins` (optional): Number of check-ins to include (1-5)

**Example:**
```json
{
  "tool": "listGoals",
  "arguments": {
    "page[size]": 10,
    "filter[assignee_id]": "13a8deaf-77d7-47c5-b0e6-2d6f3bfb79a2",
    "include": "latest_check_ins",
    "limit_check_ins": 3
  }
}
```

#### getGoal
Retrieve a single goal by UUID.

**Parameters:**
- `id` (required): Goal UUID
- `include` (optional): "latest_check_ins"
- `limit_check_ins` (optional): Number of check-ins to include (1-5)

**Example:**
```json
{
  "tool": "getGoal",
  "arguments": {
    "id": "13a8deaf-77d7-47c5-b0e6-2d6f3bfb79a2",
    "include": "latest_check_ins"
  }
}
```

### Metrics Management

#### listMetrics
Retrieve metrics with filtering options.

**Parameters:**
- `page[size]`, `page[number]`: Pagination
- `filter[assignee_id]`: Person UUID
- `filter[checkin_frequency]`: "weekly", "monthly", "quarterly", "annual"
- `include`: "latest_check_ins"

**Example:**
```json
{
  "tool": "listMetrics",
  "arguments": {
    "filter[checkin_frequency]": "weekly",
    "filter[assignee_id]": "13a8deaf-77d7-47c5-b0e6-2d6f3bfb79a2"
  }
}
```

#### createMetricCheckIn
Create a new check-in for a metric.

**Parameters:**
- `metric_id` (required): Metric UUID
- `requestBody` (required): JSON:API formatted request body

**Example:**
```json
{
  "tool": "createMetricCheckIn",
  "arguments": {
    "metric_id": "d911deaf-77d7-47c5-b0e6-2d6f3bfb79a3",
    "requestBody": "{\"data\":{\"type\":\"metric_check_in\",\"attributes\":{\"value\":85.5,\"context\":\"Exceeded target this week due to successful marketing campaign\",\"iso_week\":40,\"iso_week_year\":2025}}}"
  }
}
```

#### updateMetricCheckIn
Update an existing metric check-in.

**Parameters:**
- `metric_id` (required): Metric UUID
- `id` (required): Check-in UUID
- `If-Match` (required): ETag value for version control
- `requestBody` (required): JSON:API formatted update data

**Example:**
```json
{
  "tool": "updateMetricCheckIn",
  "arguments": {
    "metric_id": "d911deaf-77d7-47c5-b0e6-2d6f3bfb79a3",
    "id": "7ac3deaf-77d7-47c5-b0e6-2d6f3bfb79a4",
    "If-Match": "\"1234567890\"",
    "requestBody": "{\"data\":{\"type\":\"metric_check_in\",\"id\":\"7ac3deaf-77d7-47c5-b0e6-2d6f3bfb79a4\",\"attributes\":{\"value\":92.3,\"context\":\"Updated with final numbers\"}}}"
  }
}
```

### Issues Management

#### listIssues
Retrieve issues with filtering.

**Parameters:**
- `filter[issue_type]`: "long_term", "short_term", "parking_lot"
- `filter[owner_id]`: Person UUID

**Example:**
```json
{
  "tool": "listIssues",
  "arguments": {
    "filter[issue_type]": "short_term",
    "page[size]": 20
  }
}
```

### People Management

#### listPeople
Retrieve organization members.

**Example:**
```json
{
  "tool": "listPeople",
  "arguments": {
    "page[size]": 50
  }
}
```

## Common Use Cases

### 1. Weekly Goal Review
```javascript
// Get all goals assigned to a specific person with recent check-ins
const goals = await callTool("listGoals", {
  "filter[assignee_id]": "person-uuid",
  "include": "latest_check_ins",
  "limit_check_ins": 5
});

// Get detailed view of a specific goal
const goalDetail = await callTool("getGoal", {
  "id": "goal-uuid",
  "include": "latest_check_ins"
});
```

### 2. Metric Tracking Workflow
```javascript
// List weekly metrics for a team member
const metrics = await callTool("listMetrics", {
  "filter[checkin_frequency]": "weekly",
  "filter[assignee_id]": "person-uuid"
});

// Create a new check-in
const checkIn = await callTool("createMetricCheckIn", {
  "metric_id": "metric-uuid",
  "requestBody": JSON.stringify({
    "data": {
      "type": "metric_check_in",
      "attributes": {
        "value": 95.2,
        "context": "Strong performance this week",
        "iso_week": 41,
        "iso_week_year": 2025
      }
    }
  })
});
```

### 3. Issue Management
```javascript
// Get all short-term issues
const issues = await callTool("listIssues", {
  "filter[issue_type]": "short_term"
});

// Get specific issue details
const issue = await callTool("getIssue", {
  "id": "issue-uuid"
});
```

## Integration Examples

### Using with MCP Client Libraries

#### Python Example
```python
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def main():
    server_params = StdioServerParameters(
        command="node",
        args=["build/index.js"],
        cwd="/path/to/strety-mcp-server"
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the client
            await session.initialize()
            
            # List available tools
            tools = await session.list_tools()
            print(f"Available tools: {[tool.name for tool in tools.tools]}")
            
            # Call a tool
            result = await session.call_tool("listGoals", {"page[size]": 5})
            print(f"Goals: {result.content}")

if __name__ == "__main__":
    asyncio.run(main())
```

#### JavaScript/Node.js Example
```javascript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

async function main() {
  const serverProcess = spawn('node', ['build/index.js'], {
    cwd: '/path/to/strety-mcp-server',
    stdio: ['pipe', 'pipe', 'inherit']
  });

  const transport = new StdioClientTransport({
    stdin: serverProcess.stdin,
    stdout: serverProcess.stdout
  });

  const client = new Client({
    name: "strety-client",
    version: "1.0.0"
  }, {
    capabilities: {}
  });

  await client.connect(transport);

  // List tools
  const tools = await client.listTools();
  console.log('Available tools:', tools.tools.map(t => t.name));

  // Call a tool
  const result = await client.callTool({
    name: "listGoals",
    arguments: { "page[size]": 5 }
  });
  
  console.log('Result:', result.content);
}

main().catch(console.error);
```

## Troubleshooting

### Common Issues

#### Authentication Errors (401/403)
```
Error: 401 Unauthorized
```
**Solutions:**
1. Verify your OAuth credentials in `.env`
2. Check if your token has expired
3. Ensure correct scopes are configured

#### Invalid Request Format (422)
```
Error: 422 Unprocessable Entity
```
**Solutions:**
1. Validate JSON:API format for request bodies
2. Check required fields are present
3. Verify UUID formats

#### Rate Limiting (429)
```
Error: 429 Too Many Requests
```
**Solutions:**
1. Implement request throttling
2. Add delays between requests
3. Use pagination to reduce request frequency

### Debug Mode

Enable detailed logging:
```env
LOG_LEVEL=debug
```

### Health Check

Test server connectivity:
```bash
curl http://localhost:3000/health
```

Expected response:
```json
{"status":"OK","server":"strety-mcp-server","version":"1.0.0"}
```

### Testing Individual Tools

Use the web interface at http://localhost:3000 to:
1. Browse available tools
2. Test tool calls interactively
3. View request/response details
4. Debug parameter formatting

## Best Practices

1. **Use pagination** for large datasets
2. **Include relationships** when you need related data
3. **Handle errors gracefully** with proper error checking
4. **Cache frequently accessed data** to reduce API calls
5. **Use appropriate scopes** (read vs write) for security
6. **Monitor rate limits** to avoid throttling
7. **Validate UUIDs** before making requests
8. **Use ISO 8601 format** for all datetime parameters
