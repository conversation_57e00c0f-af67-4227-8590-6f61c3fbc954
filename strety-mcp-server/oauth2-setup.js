#!/usr/bin/env node

/**
 * OAuth2 Setup Helper for Strety MCP Server
 * 
 * This script helps you set up OAuth2 authentication for the Strety MCP server
 * by guiding you through the authorization code flow.
 */

import dotenv from 'dotenv';
import axios from 'axios';
import https from 'https';
import { createServer as createHttpsServer } from 'https';
import { URL } from 'url';

dotenv.config();

const CLIENT_ID = process.env.OAUTH_CLIENT_ID_OAUTH2;
const CLIENT_SECRET = process.env.OAUTH_CLIENT_SECRET_OAUTH2;
const REDIRECT_URI = 'https://localhost:3001/oauth/callback';
const SCOPES = 'read write';

function createSelfSignedCert() {
  // Simple self-signed certificate for localhost
  const cert = `-----BEGIN CERTIFICATE-----
MIICpDCCAYwCCQC8w+VhNZU4jDANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAls
b2NhbGhvc3QwHhcNMjQwMTAxMDAwMDAwWhcNMjUwMTAxMDAwMDAwWjAUMRIwEAYD
VQQDDAlsb2NhbGhvc3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC7
VJTUt9Us8cKBwko6c8+uQV/3uVSjHR/xUCsXBFAOV9lXvpCjRqREiCwlkRPiKalr
WiWqhkiG9w0BAQsFAAOCAQEAuiKQfwAFdxvRiuiQuC+oHkLsPiXpXQHiEGmlXlKj
-----END CERTIFICATE-----`;

  const key = `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wko6c8+uQV/3uVSjHR/xUCsXBFAOV9lXvpCjRqREiCwlkRPiKalrWiWqhkiG9w0B
AQsFAAOCAQEAuiKQfwAFdxvRiuiQuC+oHkLsPiXpXQHiEGmlXlKj
-----END PRIVATE KEY-----`;

  return { cert, key };
}

async function setupOAuth2() {
  console.log('🔐 Strety MCP Server OAuth2 Setup\n');
  
  if (!CLIENT_ID || !CLIENT_SECRET) {
    console.error('❌ Missing OAuth2 credentials!');
    console.error('Please set OAUTH_CLIENT_ID_OAUTH2 and OAUTH_CLIENT_SECRET_OAUTH2 in your .env file');
    process.exit(1);
  }
  
  console.log('✅ OAuth2 credentials found');
  console.log(`Client ID: ${CLIENT_ID.substring(0, 10)}...`);
  console.log(`Redirect URI: ${REDIRECT_URI}\n`);
  
  // Create authorization URL
  const authUrl = new URL('https://2.strety.com/api/v1/oauth/authorize');
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('client_id', CLIENT_ID);
  authUrl.searchParams.set('redirect_uri', REDIRECT_URI);
  authUrl.searchParams.set('scope', SCOPES);
  authUrl.searchParams.set('state', 'mcp-setup-' + Date.now());
  
  console.log('📋 Step 1: Authorization');
  console.log('Open this URL in your browser to authorize the application:');
  console.log('\n' + authUrl.toString() + '\n');
  
  // Create self-signed certificate for HTTPS
  const cert = createSelfSignedCert();

  // Start temporary HTTPS server to catch the callback
  const server = createHttpsServer(cert, (req, res) => {
    const url = new URL(req.url, `http://${req.headers.host}`);
    
    if (url.pathname === '/oauth/callback') {
      const code = url.searchParams.get('code');
      const error = url.searchParams.get('error');
      
      if (error) {
        res.writeHead(400, { 'Content-Type': 'text/html' });
        res.end(`<h1>Authorization Failed</h1><p>Error: ${error}</p>`);
        console.error(`❌ Authorization failed: ${error}`);
        server.close();
        process.exit(1);
      }
      
      if (code) {
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(`
          <h1>Authorization Successful!</h1>
          <p>You can close this window and return to the terminal.</p>
          <p>Authorization code received: <code>${code.substring(0, 20)}...</code></p>
        `);
        
        console.log('✅ Authorization code received!');
        exchangeCodeForTokens(code);
        server.close();
      }
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not found');
    }
  });
  
  server.listen(3001, () => {
    console.log('🔄 Waiting for authorization callback on https://localhost:3001/oauth/callback');
    console.log('⚠️  You may see a security warning - click "Advanced" and "Proceed to localhost"');
    console.log('After authorizing in your browser, return here for the next steps...\n');
  });
  
  // Timeout after 5 minutes
  setTimeout(() => {
    console.log('\n⏰ Timeout waiting for authorization. Please try again.');
    server.close();
    process.exit(1);
  }, 5 * 60 * 1000);
}

async function exchangeCodeForTokens(code) {
  console.log('\n📋 Step 2: Token Exchange');
  console.log('Exchanging authorization code for access and refresh tokens...');
  
  try {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'authorization_code');
    formData.append('code', code);
    formData.append('redirect_uri', REDIRECT_URI);
    formData.append('client_id', CLIENT_ID);
    formData.append('client_secret', CLIENT_SECRET);
    
    const response = await axios({
      method: 'POST',
      url: 'https://2.strety.com/api/v1/oauth/token',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      data: formData.toString(),
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
    
    const { access_token, refresh_token, expires_in, token_type } = response.data;
    
    if (access_token) {
      console.log('✅ Tokens acquired successfully!\n');
      
      console.log('📋 Step 3: Update your .env file');
      console.log('Add these lines to your .env file:\n');
      
      console.log('# OAuth2 tokens (acquired via authorization code flow)');
      console.log(`OAUTH_TOKEN_OAUTH2=${access_token}`);
      if (refresh_token) {
        console.log(`OAUTH_REFRESH_TOKEN_OAUTH2=${refresh_token}`);
      }
      console.log('');
      
      console.log('📊 Token Information:');
      console.log(`Token Type: ${token_type || 'Bearer'}`);
      console.log(`Expires In: ${expires_in || 'Unknown'} seconds`);
      console.log(`Refresh Token: ${refresh_token ? 'Yes' : 'No'}`);
      
      if (refresh_token) {
        console.log('\n✅ Refresh token available - automatic token renewal enabled!');
        console.log('The MCP server will automatically refresh tokens when they expire.');
      } else {
        console.log('\n⚠️  No refresh token provided - you may need to re-authorize when the token expires.');
      }
      
      console.log('\n🎉 Setup complete! You can now start the MCP server:');
      console.log('npm start');
      
    } else {
      console.error('❌ No access token in response');
      console.error('Response:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Token exchange failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response:`, error.response.data);
    } else {
      console.error(error.message);
    }
    
    console.log('\n💡 Troubleshooting:');
    console.log('1. Check that your OAuth2 app redirect URI is set to: ' + REDIRECT_URI);
    console.log('2. Verify your client ID and secret are correct');
    console.log('3. Make sure you authorized the correct scopes: ' + SCOPES);
  }
  
  process.exit(0);
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user');
  process.exit(0);
});

setupOAuth2().catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
