{"name": "strety-mcp-server", "version": "1.0.0", "description": "✅ WORKING - Production-ready MCP server for Strety API with OAuth2 authentication and Raycast integration", "private": true, "type": "module", "main": "build/index.js", "files": ["build", "src"], "scripts": {"start": "node build/index.js", "build": "tsc && chmod 755 build/index.js", "typecheck": "tsc --noEmit", "prestart": "npm run build", "start:web": "node build/index.js --transport=web", "oauth2:simple": "node oauth2-simple-setup.js", "oauth2:setup": "node oauth2-setup.js", "oauth2:manual": "node oauth2-manual-setup.js"}, "engines": {"node": ">=20.0.0"}, "dependencies": {"@hono/node-server": "^1.14.1", "@modelcontextprotocol/sdk": "^1.10.0", "axios": "^1.9.0", "dotenv": "^16.4.5", "eventsource": "^4.0.0", "hono": "^4.7.7", "json-schema-to-zod": "^2.6.1", "node-fetch": "^3.3.2", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.15.2", "@types/uuid": "^10.0.0", "typescript": "^5.8.3"}}