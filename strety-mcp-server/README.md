# Strety MCP Server

**✅ FULLY FUNCTIONAL** - A production-ready MCP (Model Context Protocol) server for the Strety API with OAuth2 authentication, automatic token refresh, and seamless Raycast integration.

## 🎯 Current Status

**WORKING** - This MCP server is fully functional with:
- ✅ **Full MCP Compliance** - Tools, Resources, and Prompts support
- ✅ **EOS-Focused Design** - Optimized for Entrepreneurial Operating System workflows
- ✅ OAuth2 authentication with automatic token refresh
- ✅ Proper JSON response formatting (`application/vnd.api+json`)
- ✅ 18 working Strety API tools with EOS-focused descriptions
- ✅ 5 contextual resources (Goals, Issues, Scorecards, Headlines, Team)
- ✅ 5 EOS-specific AI prompts (Goal analysis, Issue resolution, L10 prep, etc.)
- ✅ Raycast integration ready
- ✅ Production-ready configuration with security best practices
- ✅ Enhanced error handling with proper MCP error codes

## 🎯 EOS-Focused MCP Features

### 🛠️ **Tools** (18 Available)
EOS-optimized API tools with clear categorization:
- **🎯 Goals/Rocks**: `listGoals`, `getGoal`, `listGoalCheckIns` - Quarterly planning and tracking
- **🔥 Issues**: `listIssues`, `getIssue` - Problems for IDS (Identify, Discuss, Solve) process
- **📊 Scorecards**: `listScorecards`, `getScorecard` - KPIs and metrics tracking
- **📰 Headlines**: `listHeadlines` - Company updates for L10 meetings
- **✅ To-Dos**: `listTodos`, `getTodo` - Action items and accountability
- **👥 People**: `listPeople`, `getPerson` - Team management and GWC assessment
- **📋 Meetings**: `listMeetings`, `getMeeting` - L10 and other EOS meetings

### 📚 **Resources** (5 Available)
Contextual data sources for AI analysis:
- `strety://goals` - Current quarterly goals and rocks with progress
- `strety://issues` - Issues requiring attention and resolution
- `strety://scorecards` - Performance metrics and KPIs
- `strety://headlines` - Recent company updates and announcements
- `strety://team` - Team structure and member information

### 💬 **Prompts** (5 Available)
EOS-specific AI interaction templates:
- `analyze-goals` - Quarterly goals/rocks progress analysis and insights
- `resolve-issues` - IDS methodology for issue resolution
- `scorecard-insights` - Performance metrics analysis and recommendations
- `l10-preparation` - Level 10 meeting agenda and talking points
- `team-performance` - GWC assessment and development recommendations

## 🚀 Quick Start (5 Minutes)

### 1. Install & Build
```bash
git clone <repository-url>
cd strety-mcp-server
npm install
npm run build
```

### 2. OAuth2 Setup (Required)
```bash
npm run oauth2:simple
```
This automated wizard will:
1. Generate the authorization URL
2. Guide you through browser authorization
3. Automatically exchange the code for tokens
4. Test the API connection

### 3. Raycast Configuration
Add this to your Raycast MCP configuration:

```json
{
  "mcpServers": {
    "strety": {
      "command": "node",
      "args": ["./build/index.js"],
      "cwd": "/Users/<USER>/path/to/strety-mcp-server",
      "env": {
        "OAUTH_TOKEN_OAUTH2": "your_access_token_here",
        "OAUTH_REFRESH_TOKEN_OAUTH2": "your_refresh_token_here",
        "OAUTH_CLIENT_ID_OAUTH2": "your_client_id_here",
        "OAUTH_CLIENT_SECRET_OAUTH2": "your_client_secret_here"
      }
    }
  }
}
```

### 4. Start Using!
Ask Raycast natural language questions like:
- "Show me all people in my organization"
- "List my current goals"
- "What are the latest metrics?"
- "Create a check-in for my sales goal"

## 📊 Available Tools (18 Total)

This MCP server provides access to all Strety API endpoints:

### 📋 Data Retrieval Tools
- **`listPeople`** - List people in your organization ✅ **TESTED**
- **`listGoals`** - List goals and objectives
- **`listMetrics`** - List metrics and KPIs
- **`listTodos`** - List todos and tasks
- **`listMeetings`** - List meetings
- **`listHeadlines`** - List headlines and updates
- **`listIssues`** - List issues

### 🔍 Detail Tools
- **`getGoal`** - Get specific goal by UUID
- **`getMetric`** - Get specific metric by UUID
- **`getTodo`** - Get specific todo by UUID
- **`getMeeting`** - Get specific meeting by UUID
- **`getIssue`** - Get specific issue by UUID

### 📝 Check-in Management
- **`listMetricCheckIns`** - List metric check-ins
- **`createMetricCheckIn`** - Create new metric check-in
- **`getMetricCheckIn`** - Get specific metric check-in
- **`updateMetricCheckIn`** - Update metric check-in
- **`deleteMetricCheckIn`** - Delete metric check-in
- **`listGoalCheckIns`** - List goal check-ins

## 🔐 Authentication (OAuth2 Required)

Strety uses OAuth2 Authorization Code flow with:
- **Access tokens**: Valid for 2 hours
- **Refresh tokens**: Used for automatic renewal
- **Automatic refresh**: Built into the MCP server
- **Required redirect URI**: `https://oauth.pstmn.io/v1/callback`

### OAuth2 Setup Process

1. **Get OAuth2 credentials** from your Strety account
2. **Configure redirect URI** in Strety: `https://oauth.pstmn.io/v1/callback`
3. **Run setup wizard**: `npm run oauth2:simple`
4. **Authorize in browser** when prompted
5. **Tokens automatically saved** and tested

### Manual Token Refresh

When tokens expire (every 2 hours), refresh manually:

```bash
curl -X POST https://2.strety.com/api/v1/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=refresh_token&refresh_token=YOUR_REFRESH_TOKEN&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET"
```

## ⚙️ Configuration Options

### Option 1: Environment Variables in MCP Config (Recommended)

Include tokens directly in your MCP configuration:

```json
{
  "mcpServers": {
    "strety": {
      "command": "node",
      "args": ["./build/index.js"],
      "cwd": "/path/to/strety-mcp-server",
      "env": {
        "OAUTH_TOKEN_OAUTH2": "your_access_token",
        "OAUTH_REFRESH_TOKEN_OAUTH2": "your_refresh_token",
        "OAUTH_CLIENT_ID_OAUTH2": "your_client_id",
        "OAUTH_CLIENT_SECRET_OAUTH2": "your_client_secret"
      }
    }
  }
}
```

### Option 2: .env File

Create a `.env` file in the server directory:

```env
OAUTH_TOKEN_OAUTH2=your_access_token_here
OAUTH_REFRESH_TOKEN_OAUTH2=your_refresh_token_here
OAUTH_CLIENT_ID_OAUTH2=your_client_id_here
OAUTH_CLIENT_SECRET_OAUTH2=your_client_secret_here
```

## 🛠️ Available Scripts

- **`npm start`** - Start server in stdio mode (for MCP clients)
- **`npm run build`** - Build the TypeScript code
- **`npm run oauth2:simple`** - **✅ WORKING** OAuth2 setup wizard
- **`npm run start:web`** - Start server in web mode (for testing)
- **`npm run typecheck`** - TypeScript type checking

## 🧪 Testing

### Test the MCP Server
```bash
npm start
```

### Test with Web Interface
```bash
npm run start:web
# Open http://localhost:3000
```

### Test API Connection
The OAuth2 setup wizard automatically tests the connection and shows sample data.

## 🔧 Troubleshooting

### Common Issues & Solutions

**❌ "Invalid access token" (401 error)**
- **Cause**: Token expired (2-hour limit)
- **Solution**: Server should auto-refresh, or run manual refresh curl command

**❌ "Cannot find module /build/index.js"**
- **Cause**: Missing build files or wrong path in MCP config
- **Solution**: Run `npm run build` and use `./build/index.js` in args

**❌ "Request cancelled" (-32800)**
- **Cause**: Authentication failure or timeout
- **Solution**: Check tokens in MCP configuration, verify they're not expired

**❌ Authorize button doesn't work on OAuth2 page**
- **Cause**: Browser/JavaScript issue or OAuth2 app misconfiguration
- **Solution**: Try different browser, incognito mode, or check OAuth2 app settings

**❌ "Invalid redirect URI"**
- **Cause**: Redirect URI not configured in Strety OAuth2 app
- **Solution**: Add `https://oauth.pstmn.io/v1/callback` to your Strety OAuth2 app

### Debug Mode

Enable detailed logging by setting in your MCP config:

```json
"env": {
  "LOG_LEVEL": "debug",
  "OAUTH_TOKEN_OAUTH2": "...",
  "..."
}
```

This shows OAuth2 token acquisition and refresh attempts.

## 📋 Production Checklist

- ✅ **OAuth2 tokens configured** in MCP config or .env
- ✅ **Refresh token available** for automatic renewal
- ✅ **Correct file paths** in MCP configuration (`./build/index.js`)
- ✅ **Build completed** (`npm run build`)
- ✅ **Strety OAuth2 app configured** with correct redirect URI
- ✅ **Network access** to `https://2.strety.com`

## 🎯 What's Working

- ✅ **OAuth2 authentication** with automatic token refresh
- ✅ **Proper JSON response formatting** (`application/vnd.api+json`)
- ✅ **All 18 Strety API tools** functional
- ✅ **Raycast integration** tested and working
- ✅ **Error handling** and graceful failure recovery
- ✅ **SSL certificate handling** for HTTPS API calls
- ✅ **Token expiration management** (2-hour cycle)

## 📚 Additional Resources

- **OAuth2 Setup Guide**: [OAUTH2_SETUP.md](./OAUTH2_SETUP.md)
- **Strety API Documentation**: Contact your Strety administrator
- **MCP Protocol**: [Model Context Protocol Specification](https://modelcontextprotocol.io)

## 📄 License

MIT License - See LICENSE file for details.
