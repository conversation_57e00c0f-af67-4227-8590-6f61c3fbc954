{"name": "eventsource", "version": "4.0.0", "description": "WhatWG/W3C compliant EventSource client for Node.js and browsers", "sideEffects": false, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"deno": "./dist/index.js", "bun": "./dist/index.js", "source": "./src/index.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "pkg-utils build && pkg-utils --strict", "build:watch": "pkg-utils watch", "clean": "rimraf dist coverage", "lint": "eslint . && tsc --noEmit", "posttest": "npm run lint", "prebuild": "npm run clean", "prepare": "npm run build", "test": "npm run test:node && npm run test:browser", "test:browser": "tsx test/browser/client.browser.test.ts", "test:bun": "bun run test/bun/client.bun.test.ts", "test:deno": "deno run --allow-net --allow-read --allow-env --unstable-sloppy-imports test/deno/client.deno.test.ts", "test:node": "tsx test/node/client.node.test.ts"}, "files": ["!dist/stats.html", "dist", "src"], "repository": {"type": "git", "url": "git://**************/EventSource/eventsource.git"}, "keywords": ["sse", "eventsource", "server-sent-events"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "engines": {"node": ">=20.0.0"}, "browserslist": ["node >= 20", "chrome >= 71", "safari >= 14.1", "firefox >= 105", "edge >= 79"], "dependencies": {"eventsource-parser": "^3.0.1"}, "devDependencies": {"@sanity/pkg-utils": "^7.2.2", "@sanity/semantic-release-preset": "^5.0.0", "@tsconfig/strictest": "^2.0.5", "@types/sinon": "^17.0.4", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "esbuild": "^0.25.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-sanity": "^7.1.4", "eventsource-encoder": "^1.0.1", "playwright": "^1.52.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.14.0", "semantic-release": "^24.2.3", "sinon": "^20.0.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "undici": "^7.8.0"}, "overrides": {"cross-spawn": "7.0.6"}, "bugs": {"url": "https://github.com/EventSource/eventsource/issues"}, "homepage": "https://github.com/EventSource/eventsource#readme", "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}, "eslintConfig": {"parserOptions": {"ecmaVersion": 9, "sourceType": "module", "ecmaFeatures": {"modules": true}}, "extends": ["sanity", "sanity/typescript", "prettier"], "ignorePatterns": ["lib/**/"], "globals": {"globalThis": false}, "rules": {"no-undef": "off", "no-empty": "off"}}, "publishConfig": {"provenance": true}}