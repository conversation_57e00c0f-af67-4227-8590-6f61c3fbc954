{"version": 3, "file": "polyfill.es2018.min.js", "sources": ["../src/utils.ts", "../src/lib/helpers/miscellaneous.ts", "../src/lib/helpers/webidl.ts", "../src/lib/simple-queue.ts", "../src/lib/abstract-ops/internal-methods.ts", "../src/lib/readable-stream/generic-reader.ts", "../src/stub/number-isfinite.ts", "../src/stub/math-trunc.ts", "../src/lib/validators/basic.ts", "../src/lib/validators/readable-stream.ts", "../src/lib/readable-stream/default-reader.ts", "../src/target/es2018/stub/async-iterator-prototype.ts", "../src/lib/readable-stream/async-iterator.ts", "../src/stub/number-isnan.ts", "../src/lib/abstract-ops/ecmascript.ts", "../src/lib/abstract-ops/miscellaneous.ts", "../src/lib/abstract-ops/queue-with-sizes.ts", "../src/lib/helpers/array-buffer-view.ts", "../src/lib/readable-stream/byte-stream-controller.ts", "../src/lib/readable-stream/byob-reader.ts", "../src/lib/validators/reader-options.ts", "../src/lib/abstract-ops/queuing-strategy.ts", "../src/lib/validators/queuing-strategy.ts", "../src/lib/validators/underlying-sink.ts", "../src/lib/validators/writable-stream.ts", "../src/lib/abort-signal.ts", "../src/lib/writable-stream.ts", "../src/globals.ts", "../src/stub/dom-exception.ts", "../src/lib/readable-stream/pipe.ts", "../src/lib/readable-stream/default-controller.ts", "../src/lib/readable-stream/tee.ts", "../src/lib/readable-stream/from.ts", "../src/lib/readable-stream/readable-stream-like.ts", "../src/lib/validators/underlying-source.ts", "../src/lib/validators/pipe-options.ts", "../src/lib/readable-stream.ts", "../src/lib/validators/readable-writable-pair.ts", "../src/lib/validators/iterator-options.ts", "../src/lib/validators/queuing-strategy-init.ts", "../src/lib/byte-length-queuing-strategy.ts", "../src/lib/count-queuing-strategy.ts", "../src/lib/validators/transformer.ts", "../src/lib/transform-stream.ts", "../src/polyfill.ts"], "sourcesContent": ["export function noop(): undefined {\n  return undefined;\n}\n", "import { noop } from '../../utils';\nimport { AssertionError } from '../../stub/assert';\n\nexport function typeIsObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport const rethrowAssertionErrorRejection: (e: any) => void =\n  DEBUG ? e => {\n    // Used throughout the reference implementation, as `.catch(rethrowAssertionErrorRejection)`, to ensure any errors\n    // get shown. There are places in the spec where we do promise transformations and purposefully ignore or don't\n    // expect any errors, but assertion errors are always problematic.\n    if (e && e instanceof AssertionError) {\n      setTimeout(() => {\n        throw e;\n      }, 0);\n    }\n  } : noop;\n\nexport function setFunctionName(fn: Function, name: string): void {\n  try {\n    Object.defineProperty(fn, 'name', {\n      value: name,\n      configurable: true\n    });\n  } catch {\n    // This property is non-configurable in older browsers, so ignore if this throws.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/name#browser_compatibility\n  }\n}\n", "import { rethrowAssertionErrorRejection } from './miscellaneous';\nimport assert from '../../stub/assert';\n\nconst originalPromise = Promise;\nconst originalPromiseThen = Promise.prototype.then;\nconst originalPromiseReject = Promise.reject.bind(originalPromise);\n\n// https://webidl.spec.whatwg.org/#a-new-promise\nexport function newPromise<T>(executor: (\n  resolve: (value: T | PromiseLike<T>) => void,\n  reject: (reason?: any) => void\n) => void): Promise<T> {\n  return new originalPromise(executor);\n}\n\n// https://webidl.spec.whatwg.org/#a-promise-resolved-with\nexport function promiseResolvedWith<T>(value: T | PromiseLike<T>): Promise<T> {\n  return newPromise(resolve => resolve(value));\n}\n\n// https://webidl.spec.whatwg.org/#a-promise-rejected-with\nexport function promiseRejectedWith<T = never>(reason: any): Promise<T> {\n  return originalPromiseReject(reason);\n}\n\nexport function PerformPromiseThen<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  onRejected?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  // There doesn't appear to be any way to correctly emulate the behaviour from JavaScript, so this is just an\n  // approximation.\n  return originalPromiseThen.call(promise, onFulfilled, onRejected) as Promise<TResult1 | TResult2>;\n}\n\n// Bluebird logs a warning when a promise is created within a fulfillment handler, but then isn't returned\n// from that handler. To prevent this, return null instead of void from all handlers.\n// http://bluebirdjs.com/docs/warning-explanations.html#warning-a-promise-was-created-in-a-handler-but-was-not-returned-from-it\nexport function uponPromise<T>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => null | PromiseLike<null>,\n  onRejected?: (reason: any) => null | PromiseLike<null>): void {\n  PerformPromiseThen(\n    PerformPromiseThen(promise, onFulfilled, onRejected),\n    undefined,\n    rethrowAssertionErrorRejection\n  );\n}\n\nexport function uponFulfillment<T>(promise: Promise<T>, onFulfilled: (value: T) => null | PromiseLike<null>): void {\n  uponPromise(promise, onFulfilled);\n}\n\nexport function uponRejection(promise: Promise<unknown>, onRejected: (reason: any) => null | PromiseLike<null>): void {\n  uponPromise(promise, undefined, onRejected);\n}\n\nexport function transformPromiseWith<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  fulfillmentHandler?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  rejectionHandler?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);\n}\n\nexport function setPromiseIsHandledToTrue(promise: Promise<unknown>): void {\n  PerformPromiseThen(promise, undefined, rethrowAssertionErrorRejection);\n}\n\nlet _queueMicrotask: (callback: () => void) => void = callback => {\n  if (typeof queueMicrotask === 'function') {\n    _queueMicrotask = queueMicrotask;\n  } else {\n    const resolvedPromise = promiseResolvedWith(undefined);\n    _queueMicrotask = cb => PerformPromiseThen(resolvedPromise, cb);\n  }\n  return _queueMicrotask(callback);\n};\n\nexport { _queueMicrotask as queueMicrotask };\n\nexport function reflectCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R, V: T, args: A): R {\n  if (typeof F !== 'function') {\n    throw new TypeError('Argument is not a function');\n  }\n  return Function.prototype.apply.call(F, V, args);\n}\n\nexport function promiseCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R | PromiseLike<R>,\n                                                   V: T,\n                                                   args: A): Promise<R> {\n  assert(typeof F === 'function');\n  assert(V !== undefined);\n  assert(Array.isArray(args));\n  try {\n    return promiseResolvedWith(reflectCall(F, V, args));\n  } catch (value) {\n    return promiseRejectedWith(value);\n  }\n}\n", "import assert from '../stub/assert';\n\n// Original from Chromium\n// https://chromium.googlesource.com/chromium/src/+/0aee4434a4dba42a42abaea9bfbc0cd196a63bc1/third_party/blink/renderer/core/streams/SimpleQueue.js\n\nconst QUEUE_MAX_ARRAY_SIZE = 16384;\n\ninterface Node<T> {\n  _elements: T[];\n  _next: Node<T> | undefined;\n}\n\n/**\n * Simple queue structure.\n *\n * Avoids scalability issues with using a packed array directly by using\n * multiple arrays in a linked list and keeping the array size bounded.\n */\nexport class SimpleQueue<T> {\n  private _front: Node<T>;\n  private _back: Node<T>;\n  private _cursor = 0;\n  private _size = 0;\n\n  constructor() {\n    // _front and _back are always defined.\n    this._front = {\n      _elements: [],\n      _next: undefined\n    };\n    this._back = this._front;\n    // The cursor is used to avoid calling Array.shift().\n    // It contains the index of the front element of the array inside the\n    // front-most node. It is always in the range [0, QUEUE_MAX_ARRAY_SIZE).\n    this._cursor = 0;\n    // When there is only one node, size === elements.length - cursor.\n    this._size = 0;\n  }\n\n  get length(): number {\n    return this._size;\n  }\n\n  // For exception safety, this method is structured in order:\n  // 1. Read state\n  // 2. Calculate required state mutations\n  // 3. Perform state mutations\n  push(element: T): void {\n    const oldBack = this._back;\n    let newBack = oldBack;\n    assert(oldBack._next === undefined);\n    if (oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1) {\n      newBack = {\n        _elements: [],\n        _next: undefined\n      };\n    }\n\n    // push() is the mutation most likely to throw an exception, so it\n    // goes first.\n    oldBack._elements.push(element);\n    if (newBack !== oldBack) {\n      this._back = newBack;\n      oldBack._next = newBack;\n    }\n    ++this._size;\n  }\n\n  // Like push(), shift() follows the read -> calculate -> mutate pattern for\n  // exception safety.\n  shift(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const oldFront = this._front;\n    let newFront = oldFront;\n    const oldCursor = this._cursor;\n    let newCursor = oldCursor + 1;\n\n    const elements = oldFront._elements;\n    const element = elements[oldCursor];\n\n    if (newCursor === QUEUE_MAX_ARRAY_SIZE) {\n      assert(elements.length === QUEUE_MAX_ARRAY_SIZE);\n      assert(oldFront._next !== undefined);\n      newFront = oldFront._next!;\n      newCursor = 0;\n    }\n\n    // No mutations before this point.\n    --this._size;\n    this._cursor = newCursor;\n    if (oldFront !== newFront) {\n      this._front = newFront;\n    }\n\n    // Permit shifted element to be garbage collected.\n    elements[oldCursor] = undefined!;\n\n    return element;\n  }\n\n  // The tricky thing about forEach() is that it can be called\n  // re-entrantly. The queue may be mutated inside the callback. It is easy to\n  // see that push() within the callback has no negative effects since the end\n  // of the queue is checked for on every iteration. If shift() is called\n  // repeatedly within the callback then the next iteration may return an\n  // element that has been removed. In this case the callback will be called\n  // with undefined values until we either \"catch up\" with elements that still\n  // exist or reach the back of the queue.\n  forEach(callback: (element: T) => void): void {\n    let i = this._cursor;\n    let node = this._front;\n    let elements = node._elements;\n    while (i !== elements.length || node._next !== undefined) {\n      if (i === elements.length) {\n        assert(node._next !== undefined);\n        assert(i === QUEUE_MAX_ARRAY_SIZE);\n        node = node._next!;\n        elements = node._elements;\n        i = 0;\n        if (elements.length === 0) {\n          break;\n        }\n      }\n      callback(elements[i]);\n      ++i;\n    }\n  }\n\n  // Return the element that would be returned if shift() was called now,\n  // without modifying the queue.\n  peek(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const front = this._front;\n    const cursor = this._cursor;\n    return front._elements[cursor];\n  }\n}\n", "export const AbortSteps = Symbol('[[AbortSteps]]');\nexport const ErrorSteps = Symbol('[[ErrorSteps]]');\nexport const CancelSteps = Symbol('[[CancelSteps]]');\nexport const PullSteps = Symbol('[[PullSteps]]');\nexport const ReleaseSteps = Symbol('[[ReleaseSteps]]');\n", "import assert from '../../stub/assert';\nimport { ReadableStream, ReadableStreamCancel, type ReadableStreamReader } from '../readable-stream';\nimport { newPromise, setPromiseIsHandledToTrue } from '../helpers/webidl';\nimport { ReleaseSteps } from '../abstract-ops/internal-methods';\n\nexport function ReadableStreamReaderGenericInitialize<R>(reader: ReadableStreamReader<R>, stream: ReadableStream<R>) {\n  reader._ownerReadableStream = stream;\n  stream._reader = reader;\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseInitialize(reader);\n  } else if (stream._state === 'closed') {\n    defaultReaderClosedPromiseInitializeAsResolved(reader);\n  } else {\n    assert(stream._state === 'errored');\n\n    defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);\n  }\n}\n\n// A client of ReadableStreamDefaultReader and ReadableStreamBYO<PERSON>eader may use these functions directly to bypass state\n// check.\n\nexport function ReadableStreamReaderGenericCancel(reader: ReadableStreamReader<any>, reason: any): Promise<undefined> {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  return ReadableStreamCancel(stream, reason);\n}\n\nexport function ReadableStreamReaderGenericRelease(reader: ReadableStreamReader<any>) {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  assert(stream._reader === reader);\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseReject(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  } else {\n    defaultReaderClosedPromiseResetToRejected(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  }\n\n  stream._readableStreamController[ReleaseSteps]();\n\n  stream._reader = undefined;\n  reader._ownerReadableStream = undefined!;\n}\n\n// Helper functions for the readers.\n\nexport function readerLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released reader');\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nexport function defaultReaderClosedPromiseInitialize(reader: ReadableStreamReader<any>) {\n  reader._closedPromise = newPromise((resolve, reject) => {\n    reader._closedPromise_resolve = resolve;\n    reader._closedPromise_reject = reject;\n  });\n}\n\nexport function defaultReaderClosedPromiseInitializeAsRejected(reader: ReadableStreamReader<any>, reason: any) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseReject(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseInitializeAsResolved(reader: ReadableStreamReader<any>) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseResolve(reader);\n}\n\nexport function defaultReaderClosedPromiseReject(reader: ReadableStreamReader<any>, reason: any) {\n  if (reader._closedPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(reader._closedPromise);\n  reader._closedPromise_reject(reason);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n\nexport function defaultReaderClosedPromiseResetToRejected(reader: ReadableStreamReader<any>, reason: any) {\n  assert(reader._closedPromise_resolve === undefined);\n  assert(reader._closedPromise_reject === undefined);\n\n  defaultReaderClosedPromiseInitializeAsRejected(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseResolve(reader: ReadableStreamReader<any>) {\n  if (reader._closedPromise_resolve === undefined) {\n    return;\n  }\n\n  reader._closedPromise_resolve(undefined);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isFinite#Polyfill\nconst NumberIsFinite: typeof Number.isFinite = Number.isFinite || function (x) {\n  return typeof x === 'number' && isFinite(x);\n};\n\nexport default NumberIsFinite;\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc#Polyfill\nconst MathTrunc: typeof Math.trunc = Math.trunc || function (v) {\n  return v < 0 ? Math.ceil(v) : Math.floor(v);\n};\n\nexport default MathTrunc;\n", "import NumberIsFinite from '../../stub/number-isfinite';\nimport MathTrunc from '../../stub/math-trunc';\n\n// https://heycam.github.io/webidl/#idl-dictionaries\nexport function isDictionary(x: any): x is object | null {\n  return typeof x === 'object' || typeof x === 'function';\n}\n\nexport function assertDictionary(obj: unknown,\n                                 context: string): asserts obj is object | null | undefined {\n  if (obj !== undefined && !isDictionary(obj)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport type AnyFunction = (...args: any[]) => any;\n\n// https://heycam.github.io/webidl/#idl-callback-functions\nexport function assertFunction(x: unknown, context: string): asserts x is AnyFunction {\n  if (typeof x !== 'function') {\n    throw new TypeError(`${context} is not a function.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-object\nexport function isObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport function assertObject(x: unknown,\n                             context: string): asserts x is object {\n  if (!isObject(x)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport function assertRequiredArgument<T>(x: T | undefined,\n                                          position: number,\n                                          context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`Parameter ${position} is required in '${context}'.`);\n  }\n}\n\nexport function assertRequiredField<T>(x: T | undefined,\n                                       field: string,\n                                       context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`${field} is required in '${context}'.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-unrestricted-double\nexport function convertUnrestrictedDouble(value: unknown): number {\n  return Number(value);\n}\n\nfunction censorNegativeZero(x: number): number {\n  return x === 0 ? 0 : x;\n}\n\nfunction integerPart(x: number): number {\n  return censorNegativeZero(MathTrunc(x));\n}\n\n// https://heycam.github.io/webidl/#idl-unsigned-long-long\nexport function convertUnsignedLongLongWithEnforceRange(value: unknown, context: string): number {\n  const lowerBound = 0;\n  const upperBound = Number.MAX_SAFE_INTEGER;\n\n  let x = Number(value);\n  x = censorNegativeZero(x);\n\n  if (!NumberIsFinite(x)) {\n    throw new TypeError(`${context} is not a finite number`);\n  }\n\n  x = integerPart(x);\n\n  if (x < lowerBound || x > upperBound) {\n    throw new TypeError(`${context} is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`);\n  }\n\n  if (!NumberIsFinite(x) || x === 0) {\n    return 0;\n  }\n\n  // TODO Use BigInt if supported?\n  // let xBigInt = BigInt(integerPart(x));\n  // xBigInt = BigInt.asUintN(64, xBigInt);\n  // return Number(xBigInt);\n\n  return x;\n}\n", "import { IsReadableStream, ReadableStream } from '../readable-stream';\n\nexport function assertReadableStream(x: unknown, context: string): asserts x is ReadableStream {\n  if (!IsReadableStream(x)) {\n    throw new TypeError(`${context} is not a ReadableStream.`);\n  }\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableStream } from '../readable-stream';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { PullSteps } from '../abstract-ops/internal-methods';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\n\n/**\n * A result returned by {@link ReadableStreamDefaultReader.read}.\n *\n * @public\n */\nexport type ReadableStreamDefaultReadResult<T> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value?: undefined;\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamDefaultReader<R>(stream: ReadableStream): ReadableStreamDefaultReader<R> {\n  return new ReadableStreamDefaultReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadRequest<R>(stream: ReadableStream<R>,\n                                                readRequest: ReadRequest<R>): void {\n  assert(IsReadableStreamDefaultReader(stream._reader));\n  assert(stream._state === 'readable');\n\n  (stream._reader! as ReadableStreamDefaultReader<R>)._readRequests.push(readRequest);\n}\n\nexport function ReadableStreamFulfillReadRequest<R>(stream: ReadableStream<R>, chunk: R | undefined, done: boolean) {\n  const reader = stream._reader as ReadableStreamDefaultReader<R>;\n\n  assert(reader._readRequests.length > 0);\n\n  const readRequest = reader._readRequests.shift()!;\n  if (done) {\n    readRequest._closeSteps();\n  } else {\n    readRequest._chunkSteps(chunk!);\n  }\n}\n\nexport function ReadableStreamGetNumReadRequests<R>(stream: ReadableStream<R>): number {\n  return (stream._reader as ReadableStreamDefaultReader<R>)._readRequests.length;\n}\n\nexport function ReadableStreamHasDefaultReader(stream: ReadableStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamDefaultReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadRequest<R> {\n  _chunkSteps(chunk: R): void;\n\n  _closeSteps(): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A default reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamDefaultReader<R = any> {\n  /** @internal */\n  _ownerReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readRequests: SimpleQueue<ReadRequest<R>>;\n\n  constructor(stream: ReadableStream<R>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamDefaultReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed,\n   * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('read'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: () => resolvePromise({ value: undefined, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamDefaultReaderRead(this, readRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamDefaultReader(this)) {\n      throw defaultReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    ReadableStreamDefaultReaderRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamDefaultReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamDefaultReader.prototype.releaseLock, 'releaseLock');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamDefaultReader<R = any>(x: any): x is ReadableStreamDefaultReader<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultReader;\n}\n\nexport function ReadableStreamDefaultReaderRead<R>(reader: ReadableStreamDefaultReader<R>,\n                                                   readRequest: ReadRequest<R>): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    readRequest._closeSteps();\n  } else if (stream._state === 'errored') {\n    readRequest._errorSteps(stream._storedError);\n  } else {\n    assert(stream._state === 'readable');\n    stream._readableStreamController[PullSteps](readRequest as ReadRequest<any>);\n  }\n}\n\nexport function ReadableStreamDefaultReaderRelease(reader: ReadableStreamDefaultReader) {\n  ReadableStreamReaderGenericRelease(reader);\n  const e = new TypeError('Reader was released');\n  ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n}\n\nexport function ReadableStreamDefaultReaderErrorReadRequests(reader: ReadableStreamDefaultReader, e: any) {\n  const readRequests = reader._readRequests;\n  reader._readRequests = new SimpleQueue();\n  readRequests.forEach(readRequest => {\n    readRequest._errorSteps(e);\n  });\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nfunction defaultReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultReader.prototype.${name} can only be used on a ReadableStreamDefaultReader`);\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\n/* eslint-disable @typescript-eslint/no-empty-function */\nexport const AsyncIteratorPrototype: AsyncIterable<any> =\n  Object.getPrototypeOf(Object.getPrototypeOf(async function* (): AsyncIterableIterator<any> {}).prototype);\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { ReadableStream } from '../readable-stream';\nimport {\n  AcquireReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  type ReadableStreamDefaultReadResult,\n  type ReadRequest\n} from './default-reader';\nimport { ReadableStreamReaderGenericCancel, ReadableStreamReaderGenericRelease } from './generic-reader';\nimport assert from '../../stub/assert';\nimport { AsyncIteratorPrototype } from '@@target/stub/async-iterator-prototype';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  queueMicrotask,\n  transformPromiseWith\n} from '../helpers/webidl';\n\n/**\n * An async iterator returned by {@link ReadableStream.values}.\n *\n * @public\n */\nexport interface ReadableStreamAsyncIterator<R> extends AsyncIterableIterator<R> {\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nexport class ReadableStreamAsyncIteratorImpl<R> {\n  private readonly _reader: ReadableStreamDefaultReader<R>;\n  private readonly _preventCancel: boolean;\n  private _ongoingPromise: Promise<ReadableStreamDefaultReadResult<R>> | undefined = undefined;\n  private _isFinished = false;\n\n  constructor(reader: ReadableStreamDefaultReader<R>, preventCancel: boolean) {\n    this._reader = reader;\n    this._preventCancel = preventCancel;\n  }\n\n  next(): Promise<ReadableStreamDefaultReadResult<R>> {\n    const nextSteps = () => this._nextSteps();\n    this._ongoingPromise = this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) :\n      nextSteps();\n    return this._ongoingPromise;\n  }\n\n  return(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    const returnSteps = () => this._returnSteps(value);\n    return this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) :\n      returnSteps();\n  }\n\n  private _nextSteps(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value: undefined, done: true });\n    }\n\n    const reader = this._reader;\n    assert(reader._ownerReadableStream !== undefined);\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        this._ongoingPromise = undefined;\n        // This needs to be delayed by one microtask, otherwise we stop pulling too early which breaks a test.\n        // FIXME Is this a bug in the specification, or in the test?\n        queueMicrotask(() => resolvePromise({ value: chunk, done: false }));\n      },\n      _closeSteps: () => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        resolvePromise({ value: undefined, done: true });\n      },\n      _errorSteps: reason => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        rejectPromise(reason);\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n    return promise;\n  }\n\n  private _returnSteps(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value, done: true });\n    }\n    this._isFinished = true;\n\n    const reader = this._reader;\n    assert(reader._ownerReadableStream !== undefined);\n    assert(reader._readRequests.length === 0);\n\n    if (!this._preventCancel) {\n      const result = ReadableStreamReaderGenericCancel(reader, value);\n      ReadableStreamReaderGenericRelease(reader);\n      return transformPromiseWith(result, () => ({ value, done: true }));\n    }\n\n    ReadableStreamReaderGenericRelease(reader);\n    return promiseResolvedWith({ value, done: true });\n  }\n}\n\ninterface ReadableStreamAsyncIteratorInstance<R> extends ReadableStreamAsyncIterator<R> {\n  /** @interal */\n  _asyncIteratorImpl: ReadableStreamAsyncIteratorImpl<R>;\n\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nconst ReadableStreamAsyncIteratorPrototype: ReadableStreamAsyncIteratorInstance<any> = {\n  next(this: ReadableStreamAsyncIteratorInstance<any>): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('next'));\n    }\n    return this._asyncIteratorImpl.next();\n  },\n\n  return(this: ReadableStreamAsyncIteratorInstance<any>, value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('return'));\n    }\n    return this._asyncIteratorImpl.return(value);\n  }\n} as any;\nObject.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamAsyncIterator<R>(stream: ReadableStream<R>,\n                                                      preventCancel: boolean): ReadableStreamAsyncIterator<R> {\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n  const impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel);\n  const iterator: ReadableStreamAsyncIteratorInstance<R> = Object.create(ReadableStreamAsyncIteratorPrototype);\n  iterator._asyncIteratorImpl = impl;\n  return iterator;\n}\n\nfunction IsReadableStreamAsyncIterator<R = any>(x: any): x is ReadableStreamAsyncIterator<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_asyncIteratorImpl')) {\n    return false;\n  }\n\n  try {\n    // noinspection SuspiciousTypeOfGuard\n    return (x as ReadableStreamAsyncIteratorInstance<any>)._asyncIteratorImpl instanceof\n      ReadableStreamAsyncIteratorImpl;\n  } catch {\n    return false;\n  }\n}\n\n// Helper functions for the ReadableStream.\n\nfunction streamAsyncIteratorBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStreamAsyncIterator.${name} can only be used on a ReadableSteamAsyncIterator`);\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN#Polyfill\nconst NumberIsNaN: typeof Number.isNaN = Number.isNaN || function (x) {\n  // eslint-disable-next-line no-self-compare\n  return x !== x;\n};\n\nexport default NumberIsNaN;\n", "import { reflectCall } from 'lib/helpers/webidl';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport assert from '../../stub/assert';\n\ndeclare global {\n  interface ArrayBuffer {\n    readonly detached: boolean;\n\n    transfer(): ArrayBuffer;\n  }\n\n  function structuredClone<T>(value: T, options: { transfer: ArrayBuffer[] }): T;\n}\n\nexport function CreateArrayFromList<T extends any[]>(elements: T): T {\n  // We use arrays to represent lists, so this is basically a no-op.\n  // Do a slice though just in case we happen to depend on the unique-ness.\n  return elements.slice() as T;\n}\n\nexport function CopyDataBlockBytes(dest: ArrayBuffer,\n                                   destOffset: number,\n                                   src: ArrayBuffer,\n                                   srcOffset: number,\n                                   n: number) {\n  new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);\n}\n\nexport let TransferArrayBuffer = (O: ArrayBuffer): ArrayBuffer => {\n  if (typeof O.transfer === 'function') {\n    TransferArrayBuffer = buffer => buffer.transfer();\n  } else if (typeof structuredClone === 'function') {\n    TransferArrayBuffer = buffer => structuredClone(buffer, { transfer: [buffer] });\n  } else {\n    // Not implemented correctly\n    TransferArrayBuffer = buffer => buffer;\n  }\n  return TransferArrayBuffer(O);\n};\n\nexport function CanTransferArrayBuffer(O: ArrayBuffer): boolean {\n  return !IsDetachedBuffer(O);\n}\n\nexport let IsDetachedBuffer = (O: ArrayBuffer): boolean => {\n  if (typeof O.detached === 'boolean') {\n    IsDetachedBuffer = buffer => buffer.detached;\n  } else {\n    // Not implemented correctly\n    IsDetachedBuffer = buffer => buffer.byteLength === 0;\n  }\n  return IsDetachedBuffer(O);\n};\n\nexport function ArrayBufferSlice(buffer: ArrayBuffer, begin: number, end: number): ArrayBuffer {\n  // ArrayBuffer.prototype.slice is not available on IE10\n  // https://www.caniuse.com/mdn-javascript_builtins_arraybuffer_slice\n  if (buffer.slice) {\n    return buffer.slice(begin, end);\n  }\n  const length = end - begin;\n  const slice = new ArrayBuffer(length);\n  CopyDataBlockBytes(slice, 0, buffer, begin, length);\n  return slice;\n}\n\nexport type MethodName<T> = {\n  [P in keyof T]: T[P] extends Function | undefined ? P : never;\n}[keyof T];\n\nexport function GetMethod<T, K extends MethodName<T>>(receiver: T, prop: K): T[K] | undefined {\n  const func = receiver[prop];\n  if (func === undefined || func === null) {\n    return undefined;\n  }\n  if (typeof func !== 'function') {\n    throw new TypeError(`${String(prop)} is not a function`);\n  }\n  return func;\n}\n\nexport interface SyncIteratorRecord<T> {\n  iterator: Iterator<T>,\n  nextMethod: Iterator<T>['next'],\n  done: boolean;\n}\n\nexport interface AsyncIteratorRecord<T> {\n  iterator: AsyncIterator<T>,\n  nextMethod: AsyncIterator<T>['next'],\n  done: boolean;\n}\n\nexport type SyncOrAsyncIteratorRecord<T> = SyncIteratorRecord<T> | AsyncIteratorRecord<T>;\n\nexport function CreateAsyncFromSyncIterator<T>(syncIteratorRecord: SyncIteratorRecord<T>): AsyncIteratorRecord<T> {\n  // Instead of re-implementing CreateAsyncFromSyncIterator and %AsyncFromSyncIteratorPrototype%,\n  // we use yield* inside an async generator function to achieve the same result.\n\n  // Wrap the sync iterator inside a sync iterable, so we can use it with yield*.\n  const syncIterable = {\n    [Symbol.iterator]: () => syncIteratorRecord.iterator\n  };\n  // Create an async generator function and immediately invoke it.\n  const asyncIterator = (async function* () {\n    return yield* syncIterable;\n  }());\n  // Return as an async iterator record.\n  const nextMethod = asyncIterator.next;\n  return { iterator: asyncIterator, nextMethod, done: false };\n}\n\n// Aligns with core-js/modules/es.symbol.async-iterator.js\nexport const SymbolAsyncIterator: (typeof Symbol)['asyncIterator'] =\n  Symbol.asyncIterator ??\n  Symbol.for?.('Symbol.asyncIterator') ??\n  '@@asyncIterator';\n\nexport type SyncOrAsyncIterable<T> = Iterable<T> | AsyncIterable<T>;\nexport type SyncOrAsyncIteratorMethod<T> = () => (Iterator<T> | AsyncIterator<T>);\n\nfunction GetIterator<T>(\n  obj: SyncOrAsyncIterable<T>,\n  hint: 'async',\n  method?: SyncOrAsyncIteratorMethod<T>\n): AsyncIteratorRecord<T>;\nfunction GetIterator<T>(\n  obj: Iterable<T>,\n  hint: 'sync',\n  method?: SyncOrAsyncIteratorMethod<T>\n): SyncIteratorRecord<T>;\nfunction GetIterator<T>(\n  obj: SyncOrAsyncIterable<T>,\n  hint = 'sync',\n  method?: SyncOrAsyncIteratorMethod<T>\n): SyncOrAsyncIteratorRecord<T> {\n  assert(hint === 'sync' || hint === 'async');\n  if (method === undefined) {\n    if (hint === 'async') {\n      method = GetMethod(obj as AsyncIterable<T>, SymbolAsyncIterator);\n      if (method === undefined) {\n        const syncMethod = GetMethod(obj as Iterable<T>, Symbol.iterator);\n        const syncIteratorRecord = GetIterator(obj as Iterable<T>, 'sync', syncMethod);\n        return CreateAsyncFromSyncIterator(syncIteratorRecord);\n      }\n    } else {\n      method = GetMethod(obj as Iterable<T>, Symbol.iterator);\n    }\n  }\n  if (method === undefined) {\n    throw new TypeError('The object is not iterable');\n  }\n  const iterator = reflectCall(method, obj, []);\n  if (!typeIsObject(iterator)) {\n    throw new TypeError('The iterator method must return an object');\n  }\n  const nextMethod = iterator.next;\n  return { iterator, nextMethod, done: false } as SyncOrAsyncIteratorRecord<T>;\n}\n\nexport { GetIterator };\n\nexport function IteratorNext<T>(iteratorRecord: AsyncIteratorRecord<T>): Promise<IteratorResult<T>> {\n  const result = reflectCall(iteratorRecord.nextMethod, iteratorRecord.iterator, []);\n  if (!typeIsObject(result)) {\n    throw new TypeError('The iterator.next() method must return an object');\n  }\n  return result;\n}\n\nexport function IteratorComplete<TReturn>(\n  iterResult: IteratorResult<unknown, TReturn>\n): iterResult is IteratorReturnResult<TReturn> {\n  assert(typeIsObject(iterResult));\n  return Boolean(iterResult.done);\n}\n\nexport function IteratorValue<T>(iterResult: IteratorYieldResult<T>): T {\n  assert(typeIsObject(iterResult));\n  return iterResult.value;\n}\n", "import NumberIsNaN from '../../stub/number-isnan';\nimport { ArrayBufferSlice } from './ecmascript';\nimport type { NonShared } from '../helpers/array-buffer-view';\n\nexport function IsNonNegativeNumber(v: number): boolean {\n  if (typeof v !== 'number') {\n    return false;\n  }\n\n  if (NumberIsNaN(v)) {\n    return false;\n  }\n\n  if (v < 0) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function CloneAsUint8Array(O: NonShared<ArrayBufferView>): NonShared<Uint8Array> {\n  const buffer = ArrayBufferSlice(O.buffer, O.byteOffset, O.byteOffset + O.byteLength);\n  return new Uint8Array(buffer) as NonShared<Uint8Array>;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsNonNegativeNumber } from './miscellaneous';\n\nexport interface QueueContainer<T> {\n  _queue: SimpleQueue<T>;\n  _queueTotalSize: number;\n}\n\nexport interface QueuePair<T> {\n  value: T;\n  size: number;\n}\n\nexport function DequeueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.shift()!;\n  container._queueTotalSize -= pair.size;\n  if (container._queueTotalSize < 0) {\n    container._queueTotalSize = 0;\n  }\n\n  return pair.value;\n}\n\nexport function EnqueueValueWithSize<T>(container: QueueContainer<QueuePair<T>>, value: T, size: number) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  if (!IsNonNegativeNumber(size) || size === Infinity) {\n    throw new RangeError('Size must be a finite, non-NaN, non-negative number.');\n  }\n\n  container._queue.push({ value, size });\n  container._queueTotalSize += size;\n}\n\nexport function PeekQueueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.peek();\n  return pair.value;\n}\n\nexport function ResetQueue<T>(container: QueueContainer<T>) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  container._queue = new SimpleQueue<T>();\n  container._queueTotalSize = 0;\n}\n", "export type TypedArray =\n  | Int8Array\n  | Uint8Array\n  | Uint8ClampedArray\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Float32Array\n  | Float64Array;\n\nexport type NonShared<T extends ArrayBufferView> = T & {\n  buffer: ArrayBuffer;\n}\n\nexport interface ArrayBufferViewConstructor<T extends ArrayBufferView = ArrayBufferView> {\n  new(buffer: ArrayBuffer, byteOffset: number, length?: number): T;\n\n  readonly prototype: T;\n}\n\nexport interface TypedArrayConstructor<T extends TypedArray = TypedArray> extends ArrayBufferViewConstructor<T> {\n  readonly BYTES_PER_ELEMENT: number;\n}\n\nexport type DataViewConstructor = ArrayBufferViewConstructor<DataView>;\n\nfunction isDataViewConstructor(ctor: Function): ctor is DataViewConstructor {\n  return ctor === DataView;\n}\n\nexport function isDataView(view: ArrayBufferView): view is DataView {\n  return isDataViewConstructor(view.constructor);\n}\n\nexport function arrayBufferViewElementSize<T extends ArrayBufferView>(ctor: ArrayBufferViewConstructor<T>): number {\n  if (isDataViewConstructor(ctor)) {\n    return 1;\n  }\n  return (ctor as unknown as TypedArrayConstructor).BYTES_PER_ELEMENT;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  IsReadableStreamDefaultReader,\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadableStreamHasDefaultReader,\n  type ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamAddReadIntoRequest,\n  ReadableStreamFulfillReadIntoRequest,\n  ReadableStreamGetNumReadIntoRequests,\n  ReadableStreamHasBYOBReader,\n  type ReadIntoRequest\n} from './byob-reader';\nimport NumberIsInteger from '../../stub/number-isinteger';\nimport {\n  IsReadableStreamLocked,\n  type ReadableByteStream,\n  ReadableStreamClose,\n  ReadableStreamError\n} from '../readable-stream';\nimport type { ValidatedUnderlyingByteSource } from './underlying-source';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport {\n  ArrayBufferSlice,\n  CanTransferArrayBuffer,\n  CopyDataBlockBytes,\n  IsDetachedBuffer,\n  TransferArrayBuffer\n} from '../abstract-ops/ecmascript';\nimport { CancelSteps, PullSteps, ReleaseSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\nimport { assertRequiredArgument, convertUnsignedLongLongWithEnforceRange } from '../validators/basic';\nimport {\n  type ArrayBufferViewConstructor,\n  arrayBufferViewElementSize,\n  type NonShared,\n  type TypedArrayConstructor\n} from '../helpers/array-buffer-view';\n\n/**\n * A pull-into request in a {@link ReadableByteStreamController}.\n *\n * @public\n */\nexport class ReadableStreamBYOBRequest {\n  /** @internal */\n  _associatedReadableByteStreamController!: ReadableByteStreamController;\n  /** @internal */\n  _view!: NonShared<ArrayBufferView> | null;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.\n   */\n  get view(): ArrayBufferView | null {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('view');\n    }\n\n    return this._view;\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that `bytesWritten` bytes were written into\n   * {@link ReadableStreamBYOBRequest.view | view}, causing the result be surfaced to the consumer.\n   *\n   * After this method is called, {@link ReadableStreamBYOBRequest.view | view} will be transferred and no longer\n   * modifiable.\n   */\n  respond(bytesWritten: number): void;\n  respond(bytesWritten: number | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respond');\n    }\n    assertRequiredArgument(bytesWritten, 1, 'respond');\n    bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, 'First parameter');\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(this._view!.buffer)) {\n      throw new TypeError(`The BYOB request's buffer has been detached and so cannot be used as a response`);\n    }\n\n    assert(this._view!.byteLength > 0);\n    assert(this._view!.buffer.byteLength > 0);\n\n    ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that instead of writing into\n   * {@link ReadableStreamBYOBRequest.view | view}, the underlying byte source is providing a new `ArrayBufferView`,\n   * which will be given to the consumer of the readable byte stream.\n   *\n   * After this method is called, `view` will be transferred and no longer modifiable.\n   */\n  respondWithNewView(view: ArrayBufferView): void;\n  respondWithNewView(view: NonShared<ArrayBufferView>): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respondWithNewView');\n    }\n    assertRequiredArgument(view, 1, 'respondWithNewView');\n\n    if (!ArrayBuffer.isView(view)) {\n      throw new TypeError('You can only respond with array buffer views');\n    }\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(view.buffer)) {\n      throw new TypeError('The given view\\'s buffer has been detached and so cannot be used as a response');\n    }\n\n    ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n  respond: { enumerable: true },\n  respondWithNewView: { enumerable: true },\n  view: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respond, 'respond');\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respondWithNewView, 'respondWithNewView');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBRequest.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBRequest',\n    configurable: true\n  });\n}\n\ninterface ByteQueueElement {\n  buffer: ArrayBuffer;\n  byteOffset: number;\n  byteLength: number;\n}\n\ntype PullIntoDescriptor<T extends NonShared<ArrayBufferView> = NonShared<ArrayBufferView>> =\n  DefaultPullIntoDescriptor\n  | BYOBPullIntoDescriptor<T>;\n\ninterface DefaultPullIntoDescriptor {\n  buffer: ArrayBuffer;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  minimumFill: number;\n  elementSize: number;\n  viewConstructor: TypedArrayConstructor<Uint8Array>;\n  readerType: 'default' | 'none';\n}\n\ninterface BYOBPullIntoDescriptor<T extends NonShared<ArrayBufferView> = NonShared<ArrayBufferView>> {\n  buffer: ArrayBuffer;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  minimumFill: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<T>;\n  readerType: 'byob' | 'none';\n}\n\n/**\n * Allows control of a {@link ReadableStream | readable byte stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableByteStreamController {\n  /** @internal */\n  _controlledReadableByteStream!: ReadableByteStream;\n  /** @internal */\n  _queue!: SimpleQueue<ByteQueueElement>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n  /** @internal */\n  _autoAllocateChunkSize: number | undefined;\n  /** @internal */\n  _byobRequest: ReadableStreamBYOBRequest | null;\n  /** @internal */\n  _pendingPullIntos!: SimpleQueue<PullIntoDescriptor>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the current BYOB pull request, or `null` if there isn't one.\n   */\n  get byobRequest(): ReadableStreamBYOBRequest | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('byobRequest');\n    }\n\n    return ReadableByteStreamControllerGetBYOBRequest(this);\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableByteStreamControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('close');\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('The stream has already been closed; do not close it again!');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be closed`);\n    }\n\n    ReadableByteStreamControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk chunk in the controlled readable stream.\n   * The chunk has to be an `ArrayBufferView` instance, or else a `TypeError` will be thrown.\n   */\n  enqueue(chunk: ArrayBufferView): void;\n  enqueue(chunk: NonShared<ArrayBufferView>): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('enqueue');\n    }\n\n    assertRequiredArgument(chunk, 1, 'enqueue');\n    if (!ArrayBuffer.isView(chunk)) {\n      throw new TypeError('chunk must be an array buffer view');\n    }\n    if (chunk.byteLength === 0) {\n      throw new TypeError('chunk must have non-zero byteLength');\n    }\n    if (chunk.buffer.byteLength === 0) {\n      throw new TypeError(`chunk's buffer must have non-zero byteLength`);\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('stream is closed or draining');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be enqueued to`);\n    }\n\n    ReadableByteStreamControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('error');\n    }\n\n    ReadableByteStreamControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ReadableByteStreamControllerClearPendingPullIntos(this);\n\n    ResetQueue(this);\n\n    const result = this._cancelAlgorithm(reason);\n    ReadableByteStreamControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<NonShared<Uint8Array>>): void {\n    const stream = this._controlledReadableByteStream;\n    assert(ReadableStreamHasDefaultReader(stream));\n\n    if (this._queueTotalSize > 0) {\n      assert(ReadableStreamGetNumReadRequests(stream) === 0);\n\n      ReadableByteStreamControllerFillReadRequestFromQueue(this, readRequest);\n      return;\n    }\n\n    const autoAllocateChunkSize = this._autoAllocateChunkSize;\n    if (autoAllocateChunkSize !== undefined) {\n      let buffer: ArrayBuffer;\n      try {\n        buffer = new ArrayBuffer(autoAllocateChunkSize);\n      } catch (bufferE) {\n        readRequest._errorSteps(bufferE);\n        return;\n      }\n\n      const pullIntoDescriptor: DefaultPullIntoDescriptor = {\n        buffer,\n        bufferByteLength: autoAllocateChunkSize,\n        byteOffset: 0,\n        byteLength: autoAllocateChunkSize,\n        bytesFilled: 0,\n        minimumFill: 1,\n        elementSize: 1,\n        viewConstructor: Uint8Array,\n        readerType: 'default'\n      };\n\n      this._pendingPullIntos.push(pullIntoDescriptor);\n    }\n\n    ReadableStreamAddReadRequest(stream, readRequest);\n    ReadableByteStreamControllerCallPullIfNeeded(this);\n  }\n\n  /** @internal */\n  [ReleaseSteps](): void {\n    if (this._pendingPullIntos.length > 0) {\n      const firstPullInto = this._pendingPullIntos.peek();\n      firstPullInto.readerType = 'none';\n\n      this._pendingPullIntos = new SimpleQueue();\n      this._pendingPullIntos.push(firstPullInto);\n    }\n  }\n}\n\nObject.defineProperties(ReadableByteStreamController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  byobRequest: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableByteStreamController.prototype.close, 'close');\nsetFunctionName(ReadableByteStreamController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableByteStreamController.prototype.error, 'error');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableByteStreamController.prototype, Symbol.toStringTag, {\n    value: 'ReadableByteStreamController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableByteStreamController.\n\nexport function IsReadableByteStreamController(x: any): x is ReadableByteStreamController {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableByteStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableByteStreamController;\n}\n\nfunction IsReadableStreamBYOBRequest(x: any): x is ReadableStreamBYOBRequest {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_associatedReadableByteStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBRequest;\n}\n\nfunction ReadableByteStreamControllerCallPullIfNeeded(controller: ReadableByteStreamController): void {\n  const shouldPull = ReadableByteStreamControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  // TODO: Test controller argument\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n      }\n\n      return null;\n    },\n    e => {\n      ReadableByteStreamControllerError(controller, e);\n      return null;\n    }\n  );\n}\n\nfunction ReadableByteStreamControllerClearPendingPullIntos(controller: ReadableByteStreamController) {\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  controller._pendingPullIntos = new SimpleQueue();\n}\n\nfunction ReadableByteStreamControllerCommitPullIntoDescriptor<T extends NonShared<ArrayBufferView>>(\n  stream: ReadableByteStream,\n  pullIntoDescriptor: PullIntoDescriptor<T>\n) {\n  assert(stream._state !== 'errored');\n  assert(pullIntoDescriptor.readerType !== 'none');\n\n  let done = false;\n  if (stream._state === 'closed') {\n    assert(pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize === 0);\n    done = true;\n  }\n\n  const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n  if (pullIntoDescriptor.readerType === 'default') {\n    ReadableStreamFulfillReadRequest(stream, filledView as unknown as NonShared<Uint8Array>, done);\n  } else {\n    assert(pullIntoDescriptor.readerType === 'byob');\n    ReadableStreamFulfillReadIntoRequest(stream, filledView, done);\n  }\n}\n\nfunction ReadableByteStreamControllerConvertPullIntoDescriptor<T extends NonShared<ArrayBufferView>>(\n  pullIntoDescriptor: PullIntoDescriptor<T>\n): T {\n  const bytesFilled = pullIntoDescriptor.bytesFilled;\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  assert(bytesFilled <= pullIntoDescriptor.byteLength);\n  assert(bytesFilled % elementSize === 0);\n\n  return new pullIntoDescriptor.viewConstructor(\n    pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize) as T;\n}\n\nfunction ReadableByteStreamControllerEnqueueChunkToQueue(controller: ReadableByteStreamController,\n                                                         buffer: ArrayBuffer,\n                                                         byteOffset: number,\n                                                         byteLength: number) {\n  controller._queue.push({ buffer, byteOffset, byteLength });\n  controller._queueTotalSize += byteLength;\n}\n\nfunction ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller: ReadableByteStreamController,\n                                                               buffer: ArrayBuffer,\n                                                               byteOffset: number,\n                                                               byteLength: number) {\n  let clonedChunk;\n  try {\n    clonedChunk = ArrayBufferSlice(buffer, byteOffset, byteOffset + byteLength);\n  } catch (cloneE) {\n    ReadableByteStreamControllerError(controller, cloneE);\n    throw cloneE;\n  }\n  ReadableByteStreamControllerEnqueueChunkToQueue(controller, clonedChunk, 0, byteLength);\n}\n\nfunction ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller: ReadableByteStreamController,\n                                                                    firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.readerType === 'none');\n  if (firstDescriptor.bytesFilled > 0) {\n    ReadableByteStreamControllerEnqueueClonedChunkToQueue(\n      controller,\n      firstDescriptor.buffer,\n      firstDescriptor.byteOffset,\n      firstDescriptor.bytesFilled\n    );\n  }\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n}\n\nfunction ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller: ReadableByteStreamController,\n                                                                     pullIntoDescriptor: PullIntoDescriptor) {\n  const maxBytesToCopy = Math.min(controller._queueTotalSize,\n                                  pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled);\n  const maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy;\n\n  let totalBytesToCopyRemaining = maxBytesToCopy;\n  let ready = false;\n  assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill);\n  const remainderBytes = maxBytesFilled % pullIntoDescriptor.elementSize;\n  const maxAlignedBytes = maxBytesFilled - remainderBytes;\n  // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n  // of the queue, so the underlying source can keep filling it.\n  if (maxAlignedBytes >= pullIntoDescriptor.minimumFill) {\n    totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled;\n    ready = true;\n  }\n\n  const queue = controller._queue;\n\n  while (totalBytesToCopyRemaining > 0) {\n    const headOfQueue = queue.peek();\n\n    const bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength);\n\n    const destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy);\n\n    if (headOfQueue.byteLength === bytesToCopy) {\n      queue.shift();\n    } else {\n      headOfQueue.byteOffset += bytesToCopy;\n      headOfQueue.byteLength -= bytesToCopy;\n    }\n    controller._queueTotalSize -= bytesToCopy;\n\n    ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor);\n\n    totalBytesToCopyRemaining -= bytesToCopy;\n  }\n\n  if (!ready) {\n    assert(controller._queueTotalSize === 0);\n    assert(pullIntoDescriptor.bytesFilled > 0);\n    assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill);\n  }\n\n  return ready;\n}\n\nfunction ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller: ReadableByteStreamController,\n                                                                size: number,\n                                                                pullIntoDescriptor: PullIntoDescriptor) {\n  assert(controller._pendingPullIntos.length === 0 || controller._pendingPullIntos.peek() === pullIntoDescriptor);\n  assert(controller._byobRequest === null);\n  pullIntoDescriptor.bytesFilled += size;\n}\n\nfunction ReadableByteStreamControllerHandleQueueDrain(controller: ReadableByteStreamController) {\n  assert(controller._controlledReadableByteStream._state === 'readable');\n\n  if (controller._queueTotalSize === 0 && controller._closeRequested) {\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamClose(controller._controlledReadableByteStream);\n  } else {\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n  }\n}\n\nfunction ReadableByteStreamControllerInvalidateBYOBRequest(controller: ReadableByteStreamController) {\n  if (controller._byobRequest === null) {\n    return;\n  }\n\n  controller._byobRequest._associatedReadableByteStreamController = undefined!;\n  controller._byobRequest._view = null!;\n  controller._byobRequest = null;\n}\n\nfunction ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller: ReadableByteStreamController) {\n  assert(!controller._closeRequested);\n\n  while (controller._pendingPullIntos.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n\n    const pullIntoDescriptor = controller._pendingPullIntos.peek();\n    assert(pullIntoDescriptor.readerType !== 'none');\n\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n      ReadableByteStreamControllerCommitPullIntoDescriptor(\n        controller._controlledReadableByteStream,\n        pullIntoDescriptor\n      );\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller: ReadableByteStreamController) {\n  const reader = controller._controlledReadableByteStream._reader;\n  assert(IsReadableStreamDefaultReader(reader));\n  while (reader._readRequests.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n    const readRequest = reader._readRequests.shift();\n    ReadableByteStreamControllerFillReadRequestFromQueue(controller, readRequest);\n  }\n}\n\nexport function ReadableByteStreamControllerPullInto<T extends NonShared<ArrayBufferView>>(\n  controller: ReadableByteStreamController,\n  view: T,\n  min: number,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = controller._controlledReadableByteStream;\n\n  const ctor = view.constructor as ArrayBufferViewConstructor<T>;\n  const elementSize = arrayBufferViewElementSize(ctor);\n\n  const { byteOffset, byteLength } = view;\n\n  const minimumFill = min * elementSize;\n  assert(minimumFill >= elementSize && minimumFill <= byteLength);\n  assert(minimumFill % elementSize === 0);\n\n  let buffer: ArrayBuffer;\n  try {\n    buffer = TransferArrayBuffer(view.buffer);\n  } catch (e) {\n    readIntoRequest._errorSteps(e);\n    return;\n  }\n\n  const pullIntoDescriptor: BYOBPullIntoDescriptor<T> = {\n    buffer,\n    bufferByteLength: buffer.byteLength,\n    byteOffset,\n    byteLength,\n    bytesFilled: 0,\n    minimumFill,\n    elementSize,\n    viewConstructor: ctor,\n    readerType: 'byob'\n  };\n\n  if (controller._pendingPullIntos.length > 0) {\n    controller._pendingPullIntos.push(pullIntoDescriptor);\n\n    // No ReadableByteStreamControllerCallPullIfNeeded() call since:\n    // - No change happens on desiredSize\n    // - The source has already been notified of that there's at least 1 pending read(view)\n\n    ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n    return;\n  }\n\n  if (stream._state === 'closed') {\n    const emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);\n    readIntoRequest._closeSteps(emptyView);\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n\n      ReadableByteStreamControllerHandleQueueDrain(controller);\n\n      readIntoRequest._chunkSteps(filledView);\n      return;\n    }\n\n    if (controller._closeRequested) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      readIntoRequest._errorSteps(e);\n      return;\n    }\n  }\n\n  controller._pendingPullIntos.push(pullIntoDescriptor);\n\n  ReadableStreamAddReadIntoRequest<T>(stream, readIntoRequest);\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInClosedState(controller: ReadableByteStreamController,\n                                                          firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.bytesFilled % firstDescriptor.elementSize === 0);\n\n  if (firstDescriptor.readerType === 'none') {\n    ReadableByteStreamControllerShiftPendingPullInto(controller);\n  }\n\n  const stream = controller._controlledReadableByteStream;\n  if (ReadableStreamHasBYOBReader(stream)) {\n    while (ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n      const pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);\n      ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerRespondInReadableState(controller: ReadableByteStreamController,\n                                                            bytesWritten: number,\n                                                            pullIntoDescriptor: PullIntoDescriptor) {\n  assert(pullIntoDescriptor.bytesFilled + bytesWritten <= pullIntoDescriptor.byteLength);\n\n  ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor);\n\n  if (pullIntoDescriptor.readerType === 'none') {\n    ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, pullIntoDescriptor);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n    return;\n  }\n\n  if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill) {\n    // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n    // of the queue, so the underlying source can keep filling it.\n    return;\n  }\n\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n  const remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;\n  if (remainderSize > 0) {\n    const end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    ReadableByteStreamControllerEnqueueClonedChunkToQueue(\n      controller,\n      pullIntoDescriptor.buffer,\n      end - remainderSize,\n      remainderSize\n    );\n  }\n\n  pullIntoDescriptor.bytesFilled -= remainderSize;\n  ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n\n  ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInternal(controller: ReadableByteStreamController, bytesWritten: number) {\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  assert(CanTransferArrayBuffer(firstDescriptor.buffer));\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n\n  const state = controller._controlledReadableByteStream._state;\n  if (state === 'closed') {\n    assert(bytesWritten === 0);\n    ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor);\n  } else {\n    assert(state === 'readable');\n    assert(bytesWritten > 0);\n    ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerShiftPendingPullInto(\n  controller: ReadableByteStreamController\n): PullIntoDescriptor {\n  assert(controller._byobRequest === null);\n  const descriptor = controller._pendingPullIntos.shift()!;\n  return descriptor;\n}\n\nfunction ReadableByteStreamControllerShouldCallPull(controller: ReadableByteStreamController): boolean {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return false;\n  }\n\n  if (controller._closeRequested) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  if (ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableByteStreamControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableByteStreamControllerClearAlgorithms(controller: ReadableByteStreamController) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\n// A client of ReadableByteStreamController may use these functions directly to bypass state check.\n\nexport function ReadableByteStreamControllerClose(controller: ReadableByteStreamController) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    controller._closeRequested = true;\n\n    return;\n  }\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (firstPendingPullInto.bytesFilled % firstPendingPullInto.elementSize !== 0) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      throw e;\n    }\n  }\n\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamClose(stream);\n}\n\nexport function ReadableByteStreamControllerEnqueue(\n  controller: ReadableByteStreamController,\n  chunk: NonShared<ArrayBufferView>\n) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  const { buffer, byteOffset, byteLength } = chunk;\n  if (IsDetachedBuffer(buffer)) {\n    throw new TypeError('chunk\\'s buffer is detached and so cannot be enqueued');\n  }\n  const transferredBuffer = TransferArrayBuffer(buffer);\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (IsDetachedBuffer(firstPendingPullInto.buffer)) {\n      throw new TypeError(\n        'The BYOB request\\'s buffer has been detached and so cannot be filled with an enqueued chunk'\n      );\n    }\n    ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n    firstPendingPullInto.buffer = TransferArrayBuffer(firstPendingPullInto.buffer);\n    if (firstPendingPullInto.readerType === 'none') {\n      ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, firstPendingPullInto);\n    }\n  }\n\n  if (ReadableStreamHasDefaultReader(stream)) {\n    ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller);\n    if (ReadableStreamGetNumReadRequests(stream) === 0) {\n      assert(controller._pendingPullIntos.length === 0);\n      ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    } else {\n      assert(controller._queue.length === 0);\n      if (controller._pendingPullIntos.length > 0) {\n        assert(controller._pendingPullIntos.peek().readerType === 'default');\n        ReadableByteStreamControllerShiftPendingPullInto(controller);\n      }\n      const transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);\n      ReadableStreamFulfillReadRequest(stream, transferredView as NonShared<Uint8Array>, false);\n    }\n  } else if (ReadableStreamHasBYOBReader(stream)) {\n    // TODO: Ideally in this branch detaching should happen only if the buffer is not consumed fully.\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n  } else {\n    assert(!IsReadableStreamLocked(stream));\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableByteStreamControllerError(controller: ReadableByteStreamController, e: any) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ReadableByteStreamControllerClearPendingPullIntos(controller);\n\n  ResetQueue(controller);\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableByteStreamControllerFillReadRequestFromQueue(\n  controller: ReadableByteStreamController,\n  readRequest: ReadRequest<NonShared<Uint8Array>>\n) {\n  assert(controller._queueTotalSize > 0);\n\n  const entry = controller._queue.shift();\n  controller._queueTotalSize -= entry.byteLength;\n\n  ReadableByteStreamControllerHandleQueueDrain(controller);\n\n  const view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);\n  readRequest._chunkSteps(view as NonShared<Uint8Array>);\n}\n\nexport function ReadableByteStreamControllerGetBYOBRequest(\n  controller: ReadableByteStreamController\n): ReadableStreamBYOBRequest | null {\n  if (controller._byobRequest === null && controller._pendingPullIntos.length > 0) {\n    const firstDescriptor = controller._pendingPullIntos.peek();\n    const view = new Uint8Array(firstDescriptor.buffer,\n                                firstDescriptor.byteOffset + firstDescriptor.bytesFilled,\n                                firstDescriptor.byteLength - firstDescriptor.bytesFilled);\n\n    const byobRequest: ReadableStreamBYOBRequest = Object.create(ReadableStreamBYOBRequest.prototype);\n    SetUpReadableStreamBYOBRequest(byobRequest, controller, view as NonShared<Uint8Array>);\n    controller._byobRequest = byobRequest;\n  }\n  return controller._byobRequest;\n}\n\nfunction ReadableByteStreamControllerGetDesiredSize(controller: ReadableByteStreamController): number | null {\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nexport function ReadableByteStreamControllerRespond(controller: ReadableByteStreamController, bytesWritten: number) {\n  assert(controller._pendingPullIntos.length > 0);\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (bytesWritten !== 0) {\n      throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (bytesWritten === 0) {\n      throw new TypeError('bytesWritten must be greater than 0 when calling respond() on a readable stream');\n    }\n    if (firstDescriptor.bytesFilled + bytesWritten > firstDescriptor.byteLength) {\n      throw new RangeError('bytesWritten out of range');\n    }\n  }\n\n  firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer);\n\n  ReadableByteStreamControllerRespondInternal(controller, bytesWritten);\n}\n\nexport function ReadableByteStreamControllerRespondWithNewView(controller: ReadableByteStreamController,\n                                                               view: NonShared<ArrayBufferView>) {\n  assert(controller._pendingPullIntos.length > 0);\n  assert(!IsDetachedBuffer(view.buffer));\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (view.byteLength !== 0) {\n      throw new TypeError('The view\\'s length must be 0 when calling respondWithNewView() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (view.byteLength === 0) {\n      throw new TypeError(\n        'The view\\'s length must be greater than 0 when calling respondWithNewView() on a readable stream'\n      );\n    }\n  }\n\n  if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset) {\n    throw new RangeError('The region specified by view does not match byobRequest');\n  }\n  if (firstDescriptor.bufferByteLength !== view.buffer.byteLength) {\n    throw new RangeError('The buffer of view has different capacity than byobRequest');\n  }\n  if (firstDescriptor.bytesFilled + view.byteLength > firstDescriptor.byteLength) {\n    throw new RangeError('The region specified by view is larger than byobRequest');\n  }\n\n  const viewByteLength = view.byteLength;\n  firstDescriptor.buffer = TransferArrayBuffer(view.buffer);\n  ReadableByteStreamControllerRespondInternal(controller, viewByteLength);\n}\n\nexport function SetUpReadableByteStreamController(stream: ReadableByteStream,\n                                                  controller: ReadableByteStreamController,\n                                                  startAlgorithm: () => void | PromiseLike<void>,\n                                                  pullAlgorithm: () => Promise<void>,\n                                                  cancelAlgorithm: (reason: any) => Promise<void>,\n                                                  highWaterMark: number,\n                                                  autoAllocateChunkSize: number | undefined) {\n  assert(stream._readableStreamController === undefined);\n  if (autoAllocateChunkSize !== undefined) {\n    assert(NumberIsInteger(autoAllocateChunkSize));\n    assert(autoAllocateChunkSize > 0);\n  }\n\n  controller._controlledReadableByteStream = stream;\n\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._byobRequest = null;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._closeRequested = false;\n  controller._started = false;\n\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._autoAllocateChunkSize = autoAllocateChunkSize;\n\n  controller._pendingPullIntos = new SimpleQueue();\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableByteStreamControllerCallPullIfNeeded(controller);\n      return null;\n    },\n    r => {\n      ReadableByteStreamControllerError(controller, r);\n      return null;\n    }\n  );\n}\n\nexport function SetUpReadableByteStreamControllerFromUnderlyingSource(\n  stream: ReadableByteStream,\n  underlyingByteSource: ValidatedUnderlyingByteSource,\n  highWaterMark: number\n) {\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let pullAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingByteSource.start !== undefined) {\n    startAlgorithm = () => underlyingByteSource.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingByteSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingByteSource.pull!(controller);\n  } else {\n    pullAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingByteSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingByteSource.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  const autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;\n  if (autoAllocateChunkSize === 0) {\n    throw new TypeError('autoAllocateChunkSize must be greater than 0');\n  }\n\n  SetUpReadableByteStreamController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize\n  );\n}\n\nfunction SetUpReadableStreamBYOBRequest(request: ReadableStreamBYOBRequest,\n                                        controller: ReadableByteStreamController,\n                                        view: NonShared<ArrayBufferView>) {\n  assert(IsReadableByteStreamController(controller));\n  assert(typeof view === 'object');\n  assert(ArrayBuffer.isView(view));\n  assert(!IsDetachedBuffer(view.buffer));\n  request._associatedReadableByteStreamController = controller;\n  request._view = view;\n}\n\n// Helper functions for the ReadableStreamBYOBRequest.\n\nfunction byobRequestBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBRequest.prototype.${name} can only be used on a ReadableStreamBYOBRequest`);\n}\n\n// Helper functions for the ReadableByteStreamController.\n\nfunction byteStreamControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableByteStreamController.prototype.${name} can only be used on a ReadableByteStreamController`);\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, type ReadableByteStream, type ReadableStream } from '../readable-stream';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamController,\n  ReadableByteStreamControllerPullInto\n} from './byte-stream-controller';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\nimport { IsDetachedBuffer } from '../abstract-ops/ecmascript';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ValidatedReadableStreamBYOBReaderReadOptions\n} from './reader-options';\nimport { convertByobReadOptions } from '../validators/reader-options';\nimport { isDataView, type NonShared, type TypedArray } from '../helpers/array-buffer-view';\n\n/**\n * A result returned by {@link ReadableStreamBYOBReader.read}.\n *\n * @public\n */\nexport type ReadableStreamBYOBReadResult<T extends ArrayBufferView> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value: T | undefined;\n};\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamBYOBReader(stream: ReadableByteStream): ReadableStreamBYOBReader {\n  return new ReadableStreamBYOBReader(stream as ReadableStream<Uint8Array>);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadIntoRequest<T extends NonShared<ArrayBufferView>>(\n  stream: ReadableByteStream,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  assert(IsReadableStreamBYOBReader(stream._reader));\n  assert(stream._state === 'readable' || stream._state === 'closed');\n\n  (stream._reader! as ReadableStreamBYOBReader)._readIntoRequests.push(readIntoRequest);\n}\n\nexport function ReadableStreamFulfillReadIntoRequest(stream: ReadableByteStream,\n                                                     chunk: ArrayBufferView,\n                                                     done: boolean) {\n  const reader = stream._reader as ReadableStreamBYOBReader;\n\n  assert(reader._readIntoRequests.length > 0);\n\n  const readIntoRequest = reader._readIntoRequests.shift()!;\n  if (done) {\n    readIntoRequest._closeSteps(chunk);\n  } else {\n    readIntoRequest._chunkSteps(chunk);\n  }\n}\n\nexport function ReadableStreamGetNumReadIntoRequests(stream: ReadableByteStream): number {\n  return (stream._reader as ReadableStreamBYOBReader)._readIntoRequests.length;\n}\n\nexport function ReadableStreamHasBYOBReader(stream: ReadableByteStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamBYOBReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadIntoRequest<T extends NonShared<ArrayBufferView>> {\n  _chunkSteps(chunk: T): void;\n\n  _closeSteps(chunk: T | undefined): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A BYOB reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamBYOBReader {\n  /** @internal */\n  _ownerReadableStream!: ReadableByteStream;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readIntoRequests: SimpleQueue<ReadIntoRequest<any>>;\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamBYOBReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    if (!IsReadableByteStreamController(stream._readableStreamController)) {\n      throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte ' +\n        'source');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readIntoRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Attempts to reads bytes into view, and returns a promise resolved with the result.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read<T extends ArrayBufferView>(\n    view: T,\n    options?: ReadableStreamBYOBReaderReadOptions\n  ): Promise<ReadableStreamBYOBReadResult<T>>;\n  read<T extends NonShared<ArrayBufferView>>(\n    view: T,\n    rawOptions: ReadableStreamBYOBReaderReadOptions | null | undefined = {}\n  ): Promise<ReadableStreamBYOBReadResult<T>> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('read'));\n    }\n\n    if (!ArrayBuffer.isView(view)) {\n      return promiseRejectedWith(new TypeError('view must be an array buffer view'));\n    }\n    if (view.byteLength === 0) {\n      return promiseRejectedWith(new TypeError('view must have non-zero byteLength'));\n    }\n    if (view.buffer.byteLength === 0) {\n      return promiseRejectedWith(new TypeError(`view's buffer must have non-zero byteLength`));\n    }\n    if (IsDetachedBuffer(view.buffer)) {\n      return promiseRejectedWith(new TypeError('view\\'s buffer has been detached'));\n    }\n\n    let options: ValidatedReadableStreamBYOBReaderReadOptions;\n    try {\n      options = convertByobReadOptions(rawOptions, 'options');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const min = options.min;\n    if (min === 0) {\n      return promiseRejectedWith(new TypeError('options.min must be greater than 0'));\n    }\n    if (!isDataView(view)) {\n      if (min > (view as unknown as TypedArray).length) {\n        return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s length'));\n      }\n    } else if (min > view.byteLength) {\n      return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s byteLength'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamBYOBReadResult<T>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamBYOBReadResult<T>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readIntoRequest: ReadIntoRequest<T> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: chunk => resolvePromise({ value: chunk, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamBYOBReaderRead(this, view, min, readIntoRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamBYOBReader(this)) {\n      throw byobReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    ReadableStreamBYOBReaderRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamBYOBReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamBYOBReader.prototype.releaseLock, 'releaseLock');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamBYOBReader(x: any): x is ReadableStreamBYOBReader {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readIntoRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBReader;\n}\n\nexport function ReadableStreamBYOBReaderRead<T extends NonShared<ArrayBufferView>>(\n  reader: ReadableStreamBYOBReader,\n  view: T,\n  min: number,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'errored') {\n    readIntoRequest._errorSteps(stream._storedError);\n  } else {\n    ReadableByteStreamControllerPullInto(\n      stream._readableStreamController as ReadableByteStreamController,\n      view,\n      min,\n      readIntoRequest\n    );\n  }\n}\n\nexport function ReadableStreamBYOBReaderRelease(reader: ReadableStreamBYOBReader) {\n  ReadableStreamReaderGenericRelease(reader);\n  const e = new TypeError('Reader was released');\n  ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n}\n\nexport function ReadableStreamBYOBReaderErrorReadIntoRequests(reader: ReadableStreamBYOBReader, e: any) {\n  const readIntoRequests = reader._readIntoRequests;\n  reader._readIntoRequests = new SimpleQueue();\n  readIntoRequests.forEach(readIntoRequest => {\n    readIntoRequest._errorSteps(e);\n  });\n}\n\n// Helper functions for the ReadableStreamBYOBReader.\n\nfunction byobReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBReader.prototype.${name} can only be used on a ReadableStreamBYOBReader`);\n}\n", "import { assertDictionary, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ReadableStreamGetReaderOptions,\n  ValidatedReadableStreamBYOBReaderReadOptions\n} from '../readable-stream/reader-options';\n\nexport function convertReaderOptions(options: ReadableStreamGetReaderOptions | null | undefined,\n                                     context: string): ReadableStreamGetReaderOptions {\n  assertDictionary(options, context);\n  const mode = options?.mode;\n  return {\n    mode: mode === undefined ? undefined : convertReadableStreamReaderMode(mode, `${context} has member 'mode' that`)\n  };\n}\n\nfunction convertReadableStreamReaderMode(mode: string, context: string): 'byob' {\n  mode = `${mode}`;\n  if (mode !== 'byob') {\n    throw new TypeError(`${context} '${mode}' is not a valid enumeration value for ReadableStreamReaderMode`);\n  }\n  return mode;\n}\n\nexport function convertByobReadOptions(\n  options: ReadableStreamBYOBReaderReadOptions | null | undefined,\n  context: string\n): ValidatedReadableStreamBYOBReaderReadOptions {\n  assertDictionary(options, context);\n  const min = options?.min ?? 1;\n  return {\n    min: convertUnsignedLongLongWithEnforceRange(\n      min,\n      `${context} has member 'min' that`\n    )\n  };\n}\n", "import type { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport NumberIsNaN from '../../stub/number-isnan';\n\nexport function ExtractHighWaterMark(strategy: QueuingStrategy, defaultHWM: number): number {\n  const { highWaterMark } = strategy;\n\n  if (highWaterMark === undefined) {\n    return defaultHWM;\n  }\n\n  if (NumberIsNaN(highWaterMark) || highWaterMark < 0) {\n    throw new RangeError('Invalid highWaterMark');\n  }\n\n  return highWaterMark;\n}\n\nexport function ExtractSizeAlgorithm<T>(strategy: QueuingStrategy<T>): QueuingStrategySizeCallback<T> {\n  const { size } = strategy;\n\n  if (!size) {\n    return () => 1;\n  }\n\n  return size;\n}\n", "import type { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport { assertDictionary, assertFunction, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategy<T>(init: QueuingStrategy<T> | null | undefined,\n                                          context: string): QueuingStrategy<T> {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  const size = init?.size;\n  return {\n    highWaterMark: highWaterMark === undefined ? undefined : convertUnrestrictedDouble(highWaterMark),\n    size: size === undefined ? undefined : convertQueuingStrategySize(size, `${context} has member 'size' that`)\n  };\n}\n\nfunction convertQueuingStrategySize<T>(fn: QueuingStrategySizeCallback<T>,\n                                       context: string): QueuingStrategySizeCallback<T> {\n  assertFunction(fn, context);\n  return chunk => convertUnrestrictedDouble(fn(chunk));\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport type {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from '../writable-stream/underlying-sink';\nimport { WritableStreamDefaultController } from '../writable-stream';\n\nexport function convertUnderlyingSink<W>(original: UnderlyingSink<W> | null,\n                                         context: string): ValidatedUnderlyingSink<W> {\n  assertDictionary(original, context);\n  const abort = original?.abort;\n  const close = original?.close;\n  const start = original?.start;\n  const type = original?.type;\n  const write = original?.write;\n  return {\n    abort: abort === undefined ?\n      undefined :\n      convertUnderlyingSinkAbortCallback(abort, original!, `${context} has member 'abort' that`),\n    close: close === undefined ?\n      undefined :\n      convertUnderlyingSinkCloseCallback(close, original!, `${context} has member 'close' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSinkStartCallback(start, original!, `${context} has member 'start' that`),\n    write: write === undefined ?\n      undefined :\n      convertUnderlyingSinkWriteCallback(write, original!, `${context} has member 'write' that`),\n    type\n  };\n}\n\nfunction convertUnderlyingSinkAbortCallback(\n  fn: UnderlyingSinkAbortCallback,\n  original: UnderlyingSink,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSinkCloseCallback(\n  fn: UnderlyingSinkCloseCallback,\n  original: UnderlyingSink,\n  context: string\n): () => Promise<void> {\n  assertFunction(fn, context);\n  return () => promiseCall(fn, original, []);\n}\n\nfunction convertUnderlyingSinkStartCallback(\n  fn: UnderlyingSinkStartCallback,\n  original: UnderlyingSink,\n  context: string\n): UnderlyingSinkStartCallback {\n  assertFunction(fn, context);\n  return (controller: WritableStreamDefaultController) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSinkWriteCallback<W>(\n  fn: UnderlyingSinkWriteCallback<W>,\n  original: UnderlyingSink<W>,\n  context: string\n): (chunk: W, controller: WritableStreamDefaultController) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: W, controller: WritableStreamDefaultController) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import { IsWritableStream, WritableStream } from '../writable-stream';\n\nexport function assertWritableStream(x: unknown, context: string): asserts x is WritableStream {\n  if (!IsWritableStream(x)) {\n    throw new TypeError(`${context} is not a WritableStream.`);\n  }\n}\n", "/**\n * A signal object that allows you to communicate with a request and abort it if required\n * via its associated `AbortController` object.\n *\n * @remarks\n *   This interface is compatible with the `AbortSignal` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @public\n */\nexport interface AbortSignal {\n  /**\n   * Whether the request is aborted.\n   */\n  readonly aborted: boolean;\n\n  /**\n   * If aborted, returns the reason for aborting.\n   */\n  readonly reason?: any;\n\n  /**\n   * Add an event listener to be triggered when this signal becomes aborted.\n   */\n  addEventListener(type: 'abort', listener: () => void): void;\n\n  /**\n   * Remove an event listener that was previously added with {@link AbortSignal.addEventListener}.\n   */\n  removeEventListener(type: 'abort', listener: () => void): void;\n}\n\nexport function isAbortSignal(value: unknown): value is AbortSignal {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  try {\n    return typeof (value as AbortSignal).aborted === 'boolean';\n  } catch {\n    // AbortSignal.prototype.aborted throws if its brand check fails\n    return false;\n  }\n}\n\n/**\n * A controller object that allows you to abort an `AbortSignal` when desired.\n *\n * @remarks\n *   This interface is compatible with the `AbortController` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @internal\n */\nexport interface AbortController {\n  readonly signal: AbortSignal;\n\n  abort(reason?: any): void;\n}\n\ninterface AbortControllerConstructor {\n  new(): AbortController;\n}\n\nconst supportsAbortController = typeof (AbortController as any) === 'function';\n\n/**\n * Construct a new AbortController, if supported by the platform.\n *\n * @internal\n */\nexport function createAbortController(): AbortController | undefined {\n  if (supportsAbortController) {\n    return new (AbortController as AbortControllerConstructor)();\n  }\n  return undefined;\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponPromise\n} from './helpers/webidl';\nimport {\n  DequeueValue,\n  EnqueueValueWithSize,\n  PeekQueueValue,\n  type QueuePair,\n  ResetQueue\n} from './abstract-ops/queue-with-sizes';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { SimpleQueue } from './simple-queue';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { AbortSteps, ErrorSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport type {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from './writable-stream/underlying-sink';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertUnderlyingSink } from './validators/underlying-sink';\nimport { assertWritableStream } from './validators/writable-stream';\nimport { type AbortController, type AbortSignal, createAbortController } from './abort-signal';\n\ntype WritableStreamState = 'writable' | 'closed' | 'erroring' | 'errored';\n\ninterface WriteOrCloseRequest {\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n}\n\ntype WriteRequest = WriteOrCloseRequest;\ntype CloseRequest = WriteOrCloseRequest;\n\ninterface PendingAbortRequest {\n  _promise: Promise<undefined>;\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n  _reason: any;\n  _wasAlreadyErroring: boolean;\n}\n\n/**\n * A writable stream represents a destination for data, into which you can write.\n *\n * @public\n */\nclass WritableStream<W = any> {\n  /** @internal */\n  _state!: WritableStreamState;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _writer: WritableStreamDefaultWriter<W> | undefined;\n  /** @internal */\n  _writableStreamController!: WritableStreamDefaultController<W>;\n  /** @internal */\n  _writeRequests!: SimpleQueue<WriteRequest>;\n  /** @internal */\n  _inFlightWriteRequest: WriteRequest | undefined;\n  /** @internal */\n  _closeRequest: CloseRequest | undefined;\n  /** @internal */\n  _inFlightCloseRequest: CloseRequest | undefined;\n  /** @internal */\n  _pendingAbortRequest: PendingAbortRequest | undefined;\n  /** @internal */\n  _backpressure!: boolean;\n\n  constructor(underlyingSink?: UnderlyingSink<W>, strategy?: QueuingStrategy<W>);\n  constructor(rawUnderlyingSink: UnderlyingSink<W> | null | undefined = {},\n              rawStrategy: QueuingStrategy<W> | null | undefined = {}) {\n    if (rawUnderlyingSink === undefined) {\n      rawUnderlyingSink = null;\n    } else {\n      assertObject(rawUnderlyingSink, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSink = convertUnderlyingSink(rawUnderlyingSink, 'First parameter');\n\n    InitializeWritableStream(this);\n\n    const type = underlyingSink.type;\n    if (type !== undefined) {\n      throw new RangeError('Invalid type is specified');\n    }\n\n    const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n    const highWaterMark = ExtractHighWaterMark(strategy, 1);\n\n    SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);\n  }\n\n  /**\n   * Returns whether or not the writable stream is locked to a writer.\n   */\n  get locked(): boolean {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsWritableStreamLocked(this);\n  }\n\n  /**\n   * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be\n   * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort\n   * mechanism of the underlying sink.\n   *\n   * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled\n   * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel\n   * the stream) if the stream is currently locked.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('abort'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot abort a stream that already has a writer'));\n    }\n\n    return WritableStreamAbort(this, reason);\n  }\n\n  /**\n   * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its\n   * close behavior. During this time any further attempts to write will fail (without erroring the stream).\n   *\n   * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream\n   * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with\n   * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.\n   */\n  close() {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('close'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close a stream that already has a writer'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamClose(this);\n  }\n\n  /**\n   * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream\n   * is locked, no other writer can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to write to a stream\n   * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at\n   * the same time, which would cause the resulting written data to be unpredictable and probably useless.\n   */\n  getWriter(): WritableStreamDefaultWriter<W> {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('getWriter');\n    }\n\n    return AcquireWritableStreamDefaultWriter(this);\n  }\n}\n\nObject.defineProperties(WritableStream.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  getWriter: { enumerable: true },\n  locked: { enumerable: true }\n});\nsetFunctionName(WritableStream.prototype.abort, 'abort');\nsetFunctionName(WritableStream.prototype.close, 'close');\nsetFunctionName(WritableStream.prototype.getWriter, 'getWriter');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStream.prototype, Symbol.toStringTag, {\n    value: 'WritableStream',\n    configurable: true\n  });\n}\n\nexport {\n  AcquireWritableStreamDefaultWriter,\n  CreateWritableStream,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamDefaultControllerErrorIfNeeded,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite,\n  WritableStreamCloseQueuedOrInFlight\n};\n\nexport type {\n  UnderlyingSink,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkAbortCallback\n};\n\n// Abstract operations for the WritableStream.\n\nfunction AcquireWritableStreamDefaultWriter<W>(stream: WritableStream<W>): WritableStreamDefaultWriter<W> {\n  return new WritableStreamDefaultWriter(stream);\n}\n\n// Throws if and only if startAlgorithm throws.\nfunction CreateWritableStream<W>(startAlgorithm: () => void | PromiseLike<void>,\n                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                 closeAlgorithm: () => Promise<void>,\n                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                 highWaterMark = 1,\n                                 sizeAlgorithm: QueuingStrategySizeCallback<W> = () => 1) {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: WritableStream<W> = Object.create(WritableStream.prototype);\n  InitializeWritableStream(stream);\n\n  const controller: WritableStreamDefaultController<W> = Object.create(WritableStreamDefaultController.prototype);\n\n  SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm,\n                                       abortAlgorithm, highWaterMark, sizeAlgorithm);\n  return stream;\n}\n\nfunction InitializeWritableStream<W>(stream: WritableStream<W>) {\n  stream._state = 'writable';\n\n  // The error that will be reported by new method calls once the state becomes errored. Only set when [[state]] is\n  // 'erroring' or 'errored'. May be set to an undefined value.\n  stream._storedError = undefined;\n\n  stream._writer = undefined;\n\n  // Initialize to undefined first because the constructor of the controller checks this\n  // variable to validate the caller.\n  stream._writableStreamController = undefined!;\n\n  // This queue is placed here instead of the writer class in order to allow for passing a writer to the next data\n  // producer without waiting for the queued writes to finish.\n  stream._writeRequests = new SimpleQueue();\n\n  // Write requests are removed from _writeRequests when write() is called on the underlying sink. This prevents\n  // them from being erroneously rejected on error. If a write() call is in-flight, the request is stored here.\n  stream._inFlightWriteRequest = undefined;\n\n  // The promise that was returned from writer.close(). Stored here because it may be fulfilled after the writer\n  // has been detached.\n  stream._closeRequest = undefined;\n\n  // Close request is removed from _closeRequest when close() is called on the underlying sink. This prevents it\n  // from being erroneously rejected on error. If a close() call is in-flight, the request is stored here.\n  stream._inFlightCloseRequest = undefined;\n\n  // The promise that was returned from writer.abort(). This may also be fulfilled after the writer has detached.\n  stream._pendingAbortRequest = undefined;\n\n  // The backpressure signal set by the controller.\n  stream._backpressure = false;\n}\n\nfunction IsWritableStream(x: unknown): x is WritableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_writableStreamController')) {\n    return false;\n  }\n\n  return x instanceof WritableStream;\n}\n\nfunction IsWritableStreamLocked(stream: WritableStream): boolean {\n  assert(IsWritableStream(stream));\n\n  if (stream._writer === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamAbort(stream: WritableStream, reason: any): Promise<undefined> {\n  if (stream._state === 'closed' || stream._state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  stream._writableStreamController._abortReason = reason;\n  stream._writableStreamController._abortController?.abort(reason);\n\n  // TypeScript narrows the type of `stream._state` down to 'writable' | 'erroring',\n  // but it doesn't know that signaling abort runs author code that might have changed the state.\n  // Widen the type again by casting to WritableStreamState.\n  const state = stream._state as WritableStreamState;\n\n  if (state === 'closed' || state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._pendingAbortRequest !== undefined) {\n    return stream._pendingAbortRequest._promise;\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  let wasAlreadyErroring = false;\n  if (state === 'erroring') {\n    wasAlreadyErroring = true;\n    // reason will not be used, so don't keep a reference to it.\n    reason = undefined;\n  }\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    stream._pendingAbortRequest = {\n      _promise: undefined!,\n      _resolve: resolve,\n      _reject: reject,\n      _reason: reason,\n      _wasAlreadyErroring: wasAlreadyErroring\n    };\n  });\n  stream._pendingAbortRequest!._promise = promise;\n\n  if (!wasAlreadyErroring) {\n    WritableStreamStartErroring(stream, reason);\n  }\n\n  return promise;\n}\n\nfunction WritableStreamClose(stream: WritableStream<any>): Promise<undefined> {\n  const state = stream._state;\n  if (state === 'closed' || state === 'errored') {\n    return promiseRejectedWith(new TypeError(\n      `The stream (in ${state} state) is not in the writable state and cannot be closed`));\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const closeRequest: CloseRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._closeRequest = closeRequest;\n  });\n\n  const writer = stream._writer;\n  if (writer !== undefined && stream._backpressure && state === 'writable') {\n    defaultWriterReadyPromiseResolve(writer);\n  }\n\n  WritableStreamDefaultControllerClose(stream._writableStreamController);\n\n  return promise;\n}\n\n// WritableStream API exposed for controllers.\n\nfunction WritableStreamAddWriteRequest(stream: WritableStream): Promise<undefined> {\n  assert(IsWritableStreamLocked(stream));\n  assert(stream._state === 'writable');\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const writeRequest: WriteRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._writeRequests.push(writeRequest);\n  });\n\n  return promise;\n}\n\nfunction WritableStreamDealWithRejection(stream: WritableStream, error: any) {\n  const state = stream._state;\n\n  if (state === 'writable') {\n    WritableStreamStartErroring(stream, error);\n    return;\n  }\n\n  assert(state === 'erroring');\n  WritableStreamFinishErroring(stream);\n}\n\nfunction WritableStreamStartErroring(stream: WritableStream, reason: any) {\n  assert(stream._storedError === undefined);\n  assert(stream._state === 'writable');\n\n  const controller = stream._writableStreamController;\n  assert(controller !== undefined);\n\n  stream._state = 'erroring';\n  stream._storedError = reason;\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason);\n  }\n\n  if (!WritableStreamHasOperationMarkedInFlight(stream) && controller._started) {\n    WritableStreamFinishErroring(stream);\n  }\n}\n\nfunction WritableStreamFinishErroring(stream: WritableStream) {\n  assert(stream._state === 'erroring');\n  assert(!WritableStreamHasOperationMarkedInFlight(stream));\n  stream._state = 'errored';\n  stream._writableStreamController[ErrorSteps]();\n\n  const storedError = stream._storedError;\n  stream._writeRequests.forEach(writeRequest => {\n    writeRequest._reject(storedError);\n  });\n  stream._writeRequests = new SimpleQueue();\n\n  if (stream._pendingAbortRequest === undefined) {\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const abortRequest = stream._pendingAbortRequest;\n  stream._pendingAbortRequest = undefined;\n\n  if (abortRequest._wasAlreadyErroring) {\n    abortRequest._reject(storedError);\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const promise = stream._writableStreamController[AbortSteps](abortRequest._reason);\n  uponPromise(\n    promise,\n    () => {\n      abortRequest._resolve();\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n      return null;\n    },\n    (reason: any) => {\n      abortRequest._reject(reason);\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n      return null;\n    });\n}\n\nfunction WritableStreamFinishInFlightWrite(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._resolve(undefined);\n  stream._inFlightWriteRequest = undefined;\n}\n\nfunction WritableStreamFinishInFlightWriteWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._reject(error);\n  stream._inFlightWriteRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  WritableStreamDealWithRejection(stream, error);\n}\n\nfunction WritableStreamFinishInFlightClose(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._resolve(undefined);\n  stream._inFlightCloseRequest = undefined;\n\n  const state = stream._state;\n\n  assert(state === 'writable' || state === 'erroring');\n\n  if (state === 'erroring') {\n    // The error was too late to do anything, so it is ignored.\n    stream._storedError = undefined;\n    if (stream._pendingAbortRequest !== undefined) {\n      stream._pendingAbortRequest._resolve();\n      stream._pendingAbortRequest = undefined;\n    }\n  }\n\n  stream._state = 'closed';\n\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseResolve(writer);\n  }\n\n  assert(stream._pendingAbortRequest === undefined);\n  assert(stream._storedError === undefined);\n}\n\nfunction WritableStreamFinishInFlightCloseWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._reject(error);\n  stream._inFlightCloseRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  // Never execute sink abort() after sink close().\n  if (stream._pendingAbortRequest !== undefined) {\n    stream._pendingAbortRequest._reject(error);\n    stream._pendingAbortRequest = undefined;\n  }\n  WritableStreamDealWithRejection(stream, error);\n}\n\n// TODO(ricea): Fix alphabetical order.\nfunction WritableStreamCloseQueuedOrInFlight(stream: WritableStream): boolean {\n  if (stream._closeRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamHasOperationMarkedInFlight(stream: WritableStream): boolean {\n  if (stream._inFlightWriteRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamMarkCloseRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest === undefined);\n  assert(stream._closeRequest !== undefined);\n  stream._inFlightCloseRequest = stream._closeRequest;\n  stream._closeRequest = undefined;\n}\n\nfunction WritableStreamMarkFirstWriteRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest === undefined);\n  assert(stream._writeRequests.length !== 0);\n  stream._inFlightWriteRequest = stream._writeRequests.shift();\n}\n\nfunction WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream: WritableStream) {\n  assert(stream._state === 'errored');\n  if (stream._closeRequest !== undefined) {\n    assert(stream._inFlightCloseRequest === undefined);\n\n    stream._closeRequest._reject(stream._storedError);\n    stream._closeRequest = undefined;\n  }\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseReject(writer, stream._storedError);\n  }\n}\n\nfunction WritableStreamUpdateBackpressure(stream: WritableStream, backpressure: boolean) {\n  assert(stream._state === 'writable');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const writer = stream._writer;\n  if (writer !== undefined && backpressure !== stream._backpressure) {\n    if (backpressure) {\n      defaultWriterReadyPromiseReset(writer);\n    } else {\n      assert(!backpressure);\n\n      defaultWriterReadyPromiseResolve(writer);\n    }\n  }\n\n  stream._backpressure = backpressure;\n}\n\n/**\n * A default writer vended by a {@link WritableStream}.\n *\n * @public\n */\nexport class WritableStreamDefaultWriter<W = any> {\n  /** @internal */\n  _ownerWritableStream: WritableStream<W>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _closedPromiseState!: 'pending' | 'resolved' | 'rejected';\n  /** @internal */\n  _readyPromise!: Promise<undefined>;\n  /** @internal */\n  _readyPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _readyPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readyPromiseState!: 'pending' | 'fulfilled' | 'rejected';\n\n  constructor(stream: WritableStream<W>) {\n    assertRequiredArgument(stream, 1, 'WritableStreamDefaultWriter');\n    assertWritableStream(stream, 'First parameter');\n\n    if (IsWritableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive writing by another writer');\n    }\n\n    this._ownerWritableStream = stream;\n    stream._writer = this;\n\n    const state = stream._state;\n\n    if (state === 'writable') {\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure) {\n        defaultWriterReadyPromiseInitialize(this);\n      } else {\n        defaultWriterReadyPromiseInitializeAsResolved(this);\n      }\n\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'erroring') {\n      defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError);\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'closed') {\n      defaultWriterReadyPromiseInitializeAsResolved(this);\n      defaultWriterClosedPromiseInitializeAsResolved(this);\n    } else {\n      assert(state === 'errored');\n\n      const storedError = stream._storedError;\n      defaultWriterReadyPromiseInitializeAsRejected(this, storedError);\n      defaultWriterClosedPromiseInitializeAsRejected(this, storedError);\n    }\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the writer’s lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.\n   * A producer can use this information to determine the right amount of data to write.\n   *\n   * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort\n   * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when\n   * the writer’s lock is released.\n   */\n  get desiredSize(): number | null {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('desiredSize');\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      throw defaultWriterLockException('desiredSize');\n    }\n\n    return WritableStreamDefaultWriterGetDesiredSize(this);\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions\n   * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips\n   * back to zero or below, the getter will return a new promise that stays pending until the next transition.\n   *\n   * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become\n   * rejected.\n   */\n  get ready(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('ready'));\n    }\n\n    return this._readyPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('abort'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('abort'));\n    }\n\n    return WritableStreamDefaultWriterAbort(this, reason);\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.\n   */\n  close(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('close'));\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('close'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(stream)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamDefaultWriterClose(this);\n  }\n\n  /**\n   * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.\n   * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from\n   * now on; otherwise, the writer will appear closed.\n   *\n   * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the\n   * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).\n   * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents\n   * other producers from writing in an interleaved manner.\n   */\n  releaseLock(): void {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('releaseLock');\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return;\n    }\n\n    assert(stream._writer !== undefined);\n\n    WritableStreamDefaultWriterRelease(this);\n  }\n\n  /**\n   * Writes the given chunk to the writable stream, by waiting until any previous writes have finished successfully,\n   * and then sending the chunk to the underlying sink's {@link UnderlyingSink.write | write()} method. It will return\n   * a promise that fulfills with undefined upon a successful write, or rejects if the write fails or stream becomes\n   * errored before the writing process is initiated.\n   *\n   * Note that what \"success\" means is up to the underlying sink; it might indicate simply that the chunk has been\n   * accepted, and not necessarily that it is safely saved to its ultimate destination.\n   */\n  write(chunk: W): Promise<void>;\n  write(chunk: W = undefined!): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('write'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('write to'));\n    }\n\n    return WritableStreamDefaultWriterWrite(this, chunk);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  releaseLock: { enumerable: true },\n  write: { enumerable: true },\n  closed: { enumerable: true },\n  desiredSize: { enumerable: true },\n  ready: { enumerable: true }\n});\nsetFunctionName(WritableStreamDefaultWriter.prototype.abort, 'abort');\nsetFunctionName(WritableStreamDefaultWriter.prototype.close, 'close');\nsetFunctionName(WritableStreamDefaultWriter.prototype.releaseLock, 'releaseLock');\nsetFunctionName(WritableStreamDefaultWriter.prototype.write, 'write');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultWriter.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultWriter',\n    configurable: true\n  });\n}\n\n// Abstract operations for the WritableStreamDefaultWriter.\n\nfunction IsWritableStreamDefaultWriter<W = any>(x: any): x is WritableStreamDefaultWriter<W> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_ownerWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultWriter;\n}\n\n// A client of WritableStreamDefaultWriter may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultWriterAbort(writer: WritableStreamDefaultWriter, reason: any) {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamAbort(stream, reason);\n}\n\nfunction WritableStreamDefaultWriterClose(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamClose(stream);\n}\n\nfunction WritableStreamDefaultWriterCloseWithErrorPropagation(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const state = stream._state;\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  return WritableStreamDefaultWriterClose(writer);\n}\n\nfunction WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._closedPromiseState === 'pending') {\n    defaultWriterClosedPromiseReject(writer, error);\n  } else {\n    defaultWriterClosedPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._readyPromiseState === 'pending') {\n    defaultWriterReadyPromiseReject(writer, error);\n  } else {\n    defaultWriterReadyPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterGetDesiredSize(writer: WritableStreamDefaultWriter): number | null {\n  const stream = writer._ownerWritableStream;\n  const state = stream._state;\n\n  if (state === 'errored' || state === 'erroring') {\n    return null;\n  }\n\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);\n}\n\nfunction WritableStreamDefaultWriterRelease(writer: WritableStreamDefaultWriter) {\n  const stream = writer._ownerWritableStream;\n  assert(stream !== undefined);\n  assert(stream._writer === writer);\n\n  const releasedError = new TypeError(\n    `Writer was released and can no longer be used to monitor the stream's closedness`);\n\n  WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError);\n\n  // The state transitions to \"errored\" before the sink abort() method runs, but the writer.closed promise is not\n  // rejected until afterwards. This means that simply testing state will not work.\n  WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError);\n\n  stream._writer = undefined;\n  writer._ownerWritableStream = undefined!;\n}\n\nfunction WritableStreamDefaultWriterWrite<W>(writer: WritableStreamDefaultWriter<W>, chunk: W): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const controller = stream._writableStreamController;\n\n  const chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);\n\n  if (stream !== writer._ownerWritableStream) {\n    return promiseRejectedWith(defaultWriterLockException('write to'));\n  }\n\n  const state = stream._state;\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseRejectedWith(new TypeError('The stream is closing or closed and cannot be written to'));\n  }\n  if (state === 'erroring') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable');\n\n  const promise = WritableStreamAddWriteRequest(stream);\n\n  WritableStreamDefaultControllerWrite(controller, chunk, chunkSize);\n\n  return promise;\n}\n\nconst closeSentinel: unique symbol = {} as any;\n\ntype QueueRecord<W> = W | typeof closeSentinel;\n\n/**\n * Allows control of a {@link WritableStream | writable stream}'s state and internal queue.\n *\n * @public\n */\nexport class WritableStreamDefaultController<W = any> {\n  /** @internal */\n  _controlledWritableStream!: WritableStream<W>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<QueueRecord<W>>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _abortReason: any;\n  /** @internal */\n  _abortController: AbortController | undefined;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<W>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _writeAlgorithm!: (chunk: W) => Promise<void>;\n  /** @internal */\n  _closeAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _abortAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * The reason which was passed to `WritableStream.abort(reason)` when the stream was aborted.\n   *\n   * @deprecated\n   *  This property has been removed from the specification, see https://github.com/whatwg/streams/pull/1177.\n   *  Use {@link WritableStreamDefaultController.signal}'s `reason` instead.\n   */\n  get abortReason(): any {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('abortReason');\n    }\n    return this._abortReason;\n  }\n\n  /**\n   * An `AbortSignal` that can be used to abort the pending write or close operation when the stream is aborted.\n   */\n  get signal(): AbortSignal {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('signal');\n    }\n    if (this._abortController === undefined) {\n      // Older browsers or older Node versions may not support `AbortController` or `AbortSignal`.\n      // We don't want to bundle and ship an `AbortController` polyfill together with our polyfill,\n      // so instead we only implement support for `signal` if we find a global `AbortController` constructor.\n      throw new TypeError('WritableStreamDefaultController.prototype.signal is not supported');\n    }\n    return this._abortController.signal;\n  }\n\n  /**\n   * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.\n   *\n   * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying\n   * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the\n   * normal lifecycle of interactions with the underlying sink.\n   */\n  error(e: any = undefined): void {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n    const state = this._controlledWritableStream._state;\n    if (state !== 'writable') {\n      // The stream is closed, errored or will be soon. The sink can't do anything useful if it gets an error here, so\n      // just treat it as a no-op.\n      return;\n    }\n\n    WritableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [AbortSteps](reason: any): Promise<void> {\n    const result = this._abortAlgorithm(reason);\n    WritableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [ErrorSteps]() {\n    ResetQueue(this);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n  abortReason: { enumerable: true },\n  signal: { enumerable: true },\n  error: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations implementing interface required by the WritableStream.\n\nfunction IsWritableStreamDefaultController(x: any): x is WritableStreamDefaultController<any> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultController;\n}\n\nfunction SetUpWritableStreamDefaultController<W>(stream: WritableStream<W>,\n                                                 controller: WritableStreamDefaultController<W>,\n                                                 startAlgorithm: () => void | PromiseLike<void>,\n                                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                                 closeAlgorithm: () => Promise<void>,\n                                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                                 highWaterMark: number,\n                                                 sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  assert(IsWritableStream(stream));\n  assert(stream._writableStreamController === undefined);\n\n  controller._controlledWritableStream = stream;\n  stream._writableStreamController = controller;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._abortReason = undefined;\n  controller._abortController = createAbortController();\n  controller._started = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._writeAlgorithm = writeAlgorithm;\n  controller._closeAlgorithm = closeAlgorithm;\n  controller._abortAlgorithm = abortAlgorithm;\n\n  const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n  WritableStreamUpdateBackpressure(stream, backpressure);\n\n  const startResult = startAlgorithm();\n  const startPromise = promiseResolvedWith(startResult);\n  uponPromise(\n    startPromise,\n    () => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n      return null;\n    },\n    r => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDealWithRejection(stream, r);\n      return null;\n    }\n  );\n}\n\nfunction SetUpWritableStreamDefaultControllerFromUnderlyingSink<W>(stream: WritableStream<W>,\n                                                                   underlyingSink: ValidatedUnderlyingSink<W>,\n                                                                   highWaterMark: number,\n                                                                   sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  const controller = Object.create(WritableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let writeAlgorithm: (chunk: W) => Promise<void>;\n  let closeAlgorithm: () => Promise<void>;\n  let abortAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingSink.start !== undefined) {\n    startAlgorithm = () => underlyingSink.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingSink.write !== undefined) {\n    writeAlgorithm = chunk => underlyingSink.write!(chunk, controller);\n  } else {\n    writeAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSink.close !== undefined) {\n    closeAlgorithm = () => underlyingSink.close!();\n  } else {\n    closeAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSink.abort !== undefined) {\n    abortAlgorithm = reason => underlyingSink.abort!(reason);\n  } else {\n    abortAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpWritableStreamDefaultController(\n    stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// ClearAlgorithms may be called twice. Erroring the same stream in multiple ways will often result in redundant calls.\nfunction WritableStreamDefaultControllerClearAlgorithms(controller: WritableStreamDefaultController<any>) {\n  controller._writeAlgorithm = undefined!;\n  controller._closeAlgorithm = undefined!;\n  controller._abortAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\nfunction WritableStreamDefaultControllerClose<W>(controller: WritableStreamDefaultController<W>) {\n  EnqueueValueWithSize(controller, closeSentinel, 0);\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\nfunction WritableStreamDefaultControllerGetChunkSize<W>(controller: WritableStreamDefaultController<W>,\n                                                        chunk: W): number {\n  try {\n    return controller._strategySizeAlgorithm(chunk);\n  } catch (chunkSizeE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE);\n    return 1;\n  }\n}\n\nfunction WritableStreamDefaultControllerGetDesiredSize(controller: WritableStreamDefaultController<any>): number {\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nfunction WritableStreamDefaultControllerWrite<W>(controller: WritableStreamDefaultController<W>,\n                                                 chunk: W,\n                                                 chunkSize: number) {\n  try {\n    EnqueueValueWithSize(controller, chunk, chunkSize);\n  } catch (enqueueE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);\n    return;\n  }\n\n  const stream = controller._controlledWritableStream;\n  if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === 'writable') {\n    const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n    WritableStreamUpdateBackpressure(stream, backpressure);\n  }\n\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\n// Abstract operations for the WritableStreamDefaultController.\n\nfunction WritableStreamDefaultControllerAdvanceQueueIfNeeded<W>(controller: WritableStreamDefaultController<W>) {\n  const stream = controller._controlledWritableStream;\n\n  if (!controller._started) {\n    return;\n  }\n\n  if (stream._inFlightWriteRequest !== undefined) {\n    return;\n  }\n\n  const state = stream._state;\n  assert(state !== 'closed' && state !== 'errored');\n  if (state === 'erroring') {\n    WritableStreamFinishErroring(stream);\n    return;\n  }\n\n  if (controller._queue.length === 0) {\n    return;\n  }\n\n  const value = PeekQueueValue(controller);\n  if (value === closeSentinel) {\n    WritableStreamDefaultControllerProcessClose(controller);\n  } else {\n    WritableStreamDefaultControllerProcessWrite(controller, value);\n  }\n}\n\nfunction WritableStreamDefaultControllerErrorIfNeeded(controller: WritableStreamDefaultController<any>, error: any) {\n  if (controller._controlledWritableStream._state === 'writable') {\n    WritableStreamDefaultControllerError(controller, error);\n  }\n}\n\nfunction WritableStreamDefaultControllerProcessClose(controller: WritableStreamDefaultController<any>) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkCloseRequestInFlight(stream);\n\n  DequeueValue(controller);\n  assert(controller._queue.length === 0);\n\n  const sinkClosePromise = controller._closeAlgorithm();\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  uponPromise(\n    sinkClosePromise,\n    () => {\n      WritableStreamFinishInFlightClose(stream);\n      return null;\n    },\n    reason => {\n      WritableStreamFinishInFlightCloseWithError(stream, reason);\n      return null;\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerProcessWrite<W>(controller: WritableStreamDefaultController<W>, chunk: W) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkFirstWriteRequestInFlight(stream);\n\n  const sinkWritePromise = controller._writeAlgorithm(chunk);\n  uponPromise(\n    sinkWritePromise,\n    () => {\n      WritableStreamFinishInFlightWrite(stream);\n\n      const state = stream._state;\n      assert(state === 'writable' || state === 'erroring');\n\n      DequeueValue(controller);\n\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && state === 'writable') {\n        const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n        WritableStreamUpdateBackpressure(stream, backpressure);\n      }\n\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n      return null;\n    },\n    reason => {\n      if (stream._state === 'writable') {\n        WritableStreamDefaultControllerClearAlgorithms(controller);\n      }\n      WritableStreamFinishInFlightWriteWithError(stream, reason);\n      return null;\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerGetBackpressure(controller: WritableStreamDefaultController<any>): boolean {\n  const desiredSize = WritableStreamDefaultControllerGetDesiredSize(controller);\n  return desiredSize <= 0;\n}\n\n// A client of WritableStreamDefaultController may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultControllerError(controller: WritableStreamDefaultController<any>, error: any) {\n  const stream = controller._controlledWritableStream;\n\n  assert(stream._state === 'writable');\n\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  WritableStreamStartErroring(stream, error);\n}\n\n// Helper functions for the WritableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`WritableStream.prototype.${name} can only be used on a WritableStream`);\n}\n\n// Helper functions for the WritableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultController.prototype.${name} can only be used on a WritableStreamDefaultController`);\n}\n\n\n// Helper functions for the WritableStreamDefaultWriter.\n\nfunction defaultWriterBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultWriter.prototype.${name} can only be used on a WritableStreamDefaultWriter`);\n}\n\nfunction defaultWriterLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released writer');\n}\n\nfunction defaultWriterClosedPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._closedPromise = newPromise((resolve, reject) => {\n    writer._closedPromise_resolve = resolve;\n    writer._closedPromise_reject = reject;\n    writer._closedPromiseState = 'pending';\n  });\n}\n\nfunction defaultWriterClosedPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseReject(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseResolve(writer);\n}\n\nfunction defaultWriterClosedPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._closedPromise_reject === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  setPromiseIsHandledToTrue(writer._closedPromise);\n  writer._closedPromise_reject(reason);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'rejected';\n}\n\nfunction defaultWriterClosedPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._closedPromise_resolve === undefined);\n  assert(writer._closedPromise_reject === undefined);\n  assert(writer._closedPromiseState !== 'pending');\n\n  defaultWriterClosedPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._closedPromise_resolve === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  writer._closedPromise_resolve(undefined);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'resolved';\n}\n\nfunction defaultWriterReadyPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._readyPromise = newPromise((resolve, reject) => {\n    writer._readyPromise_resolve = resolve;\n    writer._readyPromise_reject = reject;\n  });\n  writer._readyPromiseState = 'pending';\n}\n\nfunction defaultWriterReadyPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseReject(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseResolve(writer);\n}\n\nfunction defaultWriterReadyPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._readyPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(writer._readyPromise);\n  writer._readyPromise_reject(reason);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'rejected';\n}\n\nfunction defaultWriterReadyPromiseReset(writer: WritableStreamDefaultWriter) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitialize(writer);\n}\n\nfunction defaultWriterReadyPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._readyPromise_resolve === undefined) {\n    return;\n  }\n\n  writer._readyPromise_resolve(undefined);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'fulfilled';\n}\n", "/// <reference lib=\"dom\" />\n\nfunction getGlobals(): typeof globalThis | undefined {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis;\n  } else if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof global !== 'undefined') {\n    return global;\n  }\n  return undefined;\n}\n\nexport const globals = getGlobals();\n", "/// <reference types=\"node\" />\nimport { globals } from '../globals';\nimport { setFunctionName } from '../lib/helpers/miscellaneous';\n\ninterface DOMException extends Error {\n  name: string;\n  message: string;\n}\n\ntype DOMExceptionConstructor = new (message?: string, name?: string) => DOMException;\n\nfunction isDOMExceptionConstructor(ctor: unknown): ctor is DOMExceptionConstructor {\n  if (!(typeof ctor === 'function' || typeof ctor === 'object')) {\n    return false;\n  }\n  if ((ctor as DOMExceptionConstructor).name !== 'DOMException') {\n    return false;\n  }\n  try {\n    new (ctor as DOMExceptionConstructor)();\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Support:\n * - Web browsers\n * - Node 18 and higher (https://github.com/nodejs/node/commit/e4b1fb5e6422c1ff151234bb9de792d45dd88d87)\n */\nfunction getFromGlobal(): DOMExceptionConstructor | undefined {\n  const ctor = globals?.DOMException;\n  return isDOMExceptionConstructor(ctor) ? ctor : undefined;\n}\n\n/**\n * Support:\n * - All platforms\n */\nfunction createPolyfill(): DOMExceptionConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  const ctor = function DOMException(this: DOMException, message?: string, name?: string) {\n    this.message = message || '';\n    this.name = name || 'Error';\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  } as any;\n  setFunctionName(ctor, 'DOMException');\n  ctor.prototype = Object.create(Error.prototype);\n  Object.defineProperty(ctor.prototype, 'constructor', { value: ctor, writable: true, configurable: true });\n  return ctor;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nconst DOMException: DOMExceptionConstructor = getFromGlobal() || createPolyfill();\n\nexport { DOMException };\n", "import { IsReadableStream, IsReadableStreamLocked, ReadableStream, ReadableStreamCancel } from '../readable-stream';\nimport { AcquireReadableStreamDefaultReader, ReadableStreamDefaultReaderRead } from './default-reader';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireWritableStreamDefaultWriter,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamCloseQueuedOrInFlight,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite\n} from '../writable-stream';\nimport assert from '../../stub/assert';\nimport {\n  newPromise,\n  PerformPromiseThen,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponFulfillment,\n  uponPromise,\n  uponRejection\n} from '../helpers/webidl';\nimport { noop } from '../../utils';\nimport { type AbortSignal, isAbortSignal } from '../abort-signal';\nimport { DOMException } from '../../stub/dom-exception';\n\nexport function ReadableStreamPipeTo<T>(source: ReadableStream<T>,\n                                        dest: WritableStream<T>,\n                                        preventClose: boolean,\n                                        preventAbort: boolean,\n                                        preventCancel: boolean,\n                                        signal: AbortSignal | undefined): Promise<undefined> {\n  assert(IsReadableStream(source));\n  assert(IsWritableStream(dest));\n  assert(typeof preventClose === 'boolean');\n  assert(typeof preventAbort === 'boolean');\n  assert(typeof preventCancel === 'boolean');\n  assert(signal === undefined || isAbortSignal(signal));\n  assert(!IsReadableStreamLocked(source));\n  assert(!IsWritableStreamLocked(dest));\n\n  const reader = AcquireReadableStreamDefaultReader<T>(source);\n  const writer = AcquireWritableStreamDefaultWriter<T>(dest);\n\n  source._disturbed = true;\n\n  let shuttingDown = false;\n\n  // This is used to keep track of the spec's requirement that we wait for ongoing writes during shutdown.\n  let currentWrite = promiseResolvedWith<void>(undefined);\n\n  return newPromise((resolve, reject) => {\n    let abortAlgorithm: () => void;\n    if (signal !== undefined) {\n      abortAlgorithm = () => {\n        const error = signal.reason !== undefined ? signal.reason : new DOMException('Aborted', 'AbortError');\n        const actions: Array<() => Promise<void>> = [];\n        if (!preventAbort) {\n          actions.push(() => {\n            if (dest._state === 'writable') {\n              return WritableStreamAbort(dest, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        if (!preventCancel) {\n          actions.push(() => {\n            if (source._state === 'readable') {\n              return ReadableStreamCancel(source, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        shutdownWithAction(() => Promise.all(actions.map(action => action())), true, error);\n      };\n\n      if (signal.aborted) {\n        abortAlgorithm();\n        return;\n      }\n\n      signal.addEventListener('abort', abortAlgorithm);\n    }\n\n    // Using reader and writer, read all chunks from this and write them to dest\n    // - Backpressure must be enforced\n    // - Shutdown must stop all activity\n    function pipeLoop() {\n      return newPromise<void>((resolveLoop, rejectLoop) => {\n        function next(done: boolean) {\n          if (done) {\n            resolveLoop();\n          } else {\n            // Use `PerformPromiseThen` instead of `uponPromise` to avoid\n            // adding unnecessary `.catch(rethrowAssertionErrorRejection)` handlers\n            PerformPromiseThen(pipeStep(), next, rejectLoop);\n          }\n        }\n\n        next(false);\n      });\n    }\n\n    function pipeStep(): Promise<boolean> {\n      if (shuttingDown) {\n        return promiseResolvedWith(true);\n      }\n\n      return PerformPromiseThen(writer._readyPromise, () => {\n        return newPromise<boolean>((resolveRead, rejectRead) => {\n          ReadableStreamDefaultReaderRead(\n            reader,\n            {\n              _chunkSteps: chunk => {\n                currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), undefined, noop);\n                resolveRead(false);\n              },\n              _closeSteps: () => resolveRead(true),\n              _errorSteps: rejectRead\n            }\n          );\n        });\n      });\n    }\n\n    // Errors must be propagated forward\n    isOrBecomesErrored(source, reader._closedPromise, storedError => {\n      if (!preventAbort) {\n        shutdownWithAction(() => WritableStreamAbort(dest, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n      return null;\n    });\n\n    // Errors must be propagated backward\n    isOrBecomesErrored(dest, writer._closedPromise, storedError => {\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n      return null;\n    });\n\n    // Closing must be propagated forward\n    isOrBecomesClosed(source, reader._closedPromise, () => {\n      if (!preventClose) {\n        shutdownWithAction(() => WritableStreamDefaultWriterCloseWithErrorPropagation(writer));\n      } else {\n        shutdown();\n      }\n      return null;\n    });\n\n    // Closing must be propagated backward\n    if (WritableStreamCloseQueuedOrInFlight(dest) || dest._state === 'closed') {\n      const destClosed = new TypeError('the destination writable stream closed before all data could be piped to it');\n\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, destClosed), true, destClosed);\n      } else {\n        shutdown(true, destClosed);\n      }\n    }\n\n    setPromiseIsHandledToTrue(pipeLoop());\n\n    function waitForWritesToFinish(): Promise<void> {\n      // Another write may have started while we were waiting on this currentWrite, so we have to be sure to wait\n      // for that too.\n      const oldCurrentWrite = currentWrite;\n      return PerformPromiseThen(\n        currentWrite,\n        () => oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : undefined\n      );\n    }\n\n    function isOrBecomesErrored(stream: ReadableStream | WritableStream,\n                                promise: Promise<void>,\n                                action: (reason: any) => null) {\n      if (stream._state === 'errored') {\n        action(stream._storedError);\n      } else {\n        uponRejection(promise, action);\n      }\n    }\n\n    function isOrBecomesClosed(stream: ReadableStream | WritableStream, promise: Promise<void>, action: () => null) {\n      if (stream._state === 'closed') {\n        action();\n      } else {\n        uponFulfillment(promise, action);\n      }\n    }\n\n    function shutdownWithAction(action: () => Promise<unknown>, originalIsError?: boolean, originalError?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), doTheRest);\n      } else {\n        doTheRest();\n      }\n\n      function doTheRest(): null {\n        uponPromise(\n          action(),\n          () => finalize(originalIsError, originalError),\n          newError => finalize(true, newError)\n        );\n        return null;\n      }\n    }\n\n    function shutdown(isError?: boolean, error?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), () => finalize(isError, error));\n      } else {\n        finalize(isError, error);\n      }\n    }\n\n    function finalize(isError?: boolean, error?: any): null {\n      WritableStreamDefaultWriterRelease(writer);\n      ReadableStreamReaderGenericRelease(reader);\n\n      if (signal !== undefined) {\n        signal.removeEventListener('abort', abortAlgorithm);\n      }\n      if (isError) {\n        reject(error);\n      } else {\n        resolve(undefined);\n      }\n\n      return null;\n    }\n  });\n}\n", "import type { QueuingStrategySizeCallback } from '../queuing-strategy';\nimport assert from '../../stub/assert';\nimport { DequeueValue, EnqueueValueWithSize, type QueuePair, ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  type ReadRequest\n} from './default-reader';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsReadableStreamLocked, ReadableStream, ReadableStreamClose, ReadableStreamError } from '../readable-stream';\nimport type { ValidatedUnderlyingSource } from './underlying-source';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { CancelSteps, PullSteps, ReleaseSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\n\n/**\n * Allows control of a {@link ReadableStream | readable stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableStreamDefaultController<R> {\n  /** @internal */\n  _controlledReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<R>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<R>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableStreamDefaultControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('close');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits close');\n    }\n\n    ReadableStreamDefaultControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the controlled readable stream.\n   */\n  enqueue(chunk: R): void;\n  enqueue(chunk: R = undefined!): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits enqueue');\n    }\n\n    return ReadableStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    ReadableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ResetQueue(this);\n    const result = this._cancelAlgorithm(reason);\n    ReadableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<R>): void {\n    const stream = this._controlledReadableStream;\n\n    if (this._queue.length > 0) {\n      const chunk = DequeueValue(this);\n\n      if (this._closeRequested && this._queue.length === 0) {\n        ReadableStreamDefaultControllerClearAlgorithms(this);\n        ReadableStreamClose(stream);\n      } else {\n        ReadableStreamDefaultControllerCallPullIfNeeded(this);\n      }\n\n      readRequest._chunkSteps(chunk);\n    } else {\n      ReadableStreamAddReadRequest(stream, readRequest);\n      ReadableStreamDefaultControllerCallPullIfNeeded(this);\n    }\n  }\n\n  /** @internal */\n  [ReleaseSteps](): void {\n    // Do nothing.\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultController.prototype.close, 'close');\nsetFunctionName(ReadableStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableStreamDefaultController.prototype.error, 'error');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableStreamDefaultController.\n\nfunction IsReadableStreamDefaultController<R = any>(x: any): x is ReadableStreamDefaultController<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultController;\n}\n\nfunction ReadableStreamDefaultControllerCallPullIfNeeded(controller: ReadableStreamDefaultController<any>): void {\n  const shouldPull = ReadableStreamDefaultControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      }\n\n      return null;\n    },\n    e => {\n      ReadableStreamDefaultControllerError(controller, e);\n      return null;\n    }\n  );\n}\n\nfunction ReadableStreamDefaultControllerShouldCallPull(controller: ReadableStreamDefaultController<any>): boolean {\n  const stream = controller._controlledReadableStream;\n\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableStreamDefaultControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableStreamDefaultControllerClearAlgorithms(controller: ReadableStreamDefaultController<any>) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\n// A client of ReadableStreamDefaultController may use these functions directly to bypass state check.\n\nexport function ReadableStreamDefaultControllerClose(controller: ReadableStreamDefaultController<any>) {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  controller._closeRequested = true;\n\n  if (controller._queue.length === 0) {\n    ReadableStreamDefaultControllerClearAlgorithms(controller);\n    ReadableStreamClose(stream);\n  }\n}\n\nexport function ReadableStreamDefaultControllerEnqueue<R>(\n  controller: ReadableStreamDefaultController<R>,\n  chunk: R\n): void {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    ReadableStreamFulfillReadRequest(stream, chunk, false);\n  } else {\n    let chunkSize;\n    try {\n      chunkSize = controller._strategySizeAlgorithm(chunk);\n    } catch (chunkSizeE) {\n      ReadableStreamDefaultControllerError(controller, chunkSizeE);\n      throw chunkSizeE;\n    }\n\n    try {\n      EnqueueValueWithSize(controller, chunk, chunkSize);\n    } catch (enqueueE) {\n      ReadableStreamDefaultControllerError(controller, enqueueE);\n      throw enqueueE;\n    }\n  }\n\n  ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableStreamDefaultControllerError(controller: ReadableStreamDefaultController<any>, e: any) {\n  const stream = controller._controlledReadableStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ResetQueue(controller);\n\n  ReadableStreamDefaultControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableStreamDefaultControllerGetDesiredSize(\n  controller: ReadableStreamDefaultController<any>\n): number | null {\n  const state = controller._controlledReadableStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\n// This is used in the implementation of TransformStream.\nexport function ReadableStreamDefaultControllerHasBackpressure(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  if (ReadableStreamDefaultControllerShouldCallPull(controller)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function ReadableStreamDefaultControllerCanCloseOrEnqueue(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  const state = controller._controlledReadableStream._state;\n\n  if (!controller._closeRequested && state === 'readable') {\n    return true;\n  }\n\n  return false;\n}\n\nexport function SetUpReadableStreamDefaultController<R>(stream: ReadableStream<R>,\n                                                        controller: ReadableStreamDefaultController<R>,\n                                                        startAlgorithm: () => void | PromiseLike<void>,\n                                                        pullAlgorithm: () => Promise<void>,\n                                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                                        highWaterMark: number,\n                                                        sizeAlgorithm: QueuingStrategySizeCallback<R>) {\n  assert(stream._readableStreamController === undefined);\n\n  controller._controlledReadableStream = stream;\n\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._started = false;\n  controller._closeRequested = false;\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      return null;\n    },\n    r => {\n      ReadableStreamDefaultControllerError(controller, r);\n      return null;\n    }\n  );\n}\n\nexport function SetUpReadableStreamDefaultControllerFromUnderlyingSource<R>(\n  stream: ReadableStream<R>,\n  underlyingSource: ValidatedUnderlyingSource<R>,\n  highWaterMark: number,\n  sizeAlgorithm: QueuingStrategySizeCallback<R>\n) {\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let pullAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingSource.start !== undefined) {\n    startAlgorithm = () => underlyingSource.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingSource.pull!(controller);\n  } else {\n    pullAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingSource.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// Helper functions for the ReadableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultController.prototype.${name} can only be used on a ReadableStreamDefaultController`);\n}\n", "import {\n  CreateReadableByteStream,\n  CreateReadableStream,\n  type DefaultReadableStream,\n  IsReadableStream,\n  type ReadableByteStream,\n  ReadableStream,\n  ReadableStreamCancel,\n  type ReadableStreamReader\n} from '../readable-stream';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  type ReadRequest\n} from './default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderRead,\n  type ReadIntoRequest\n} from './byob-reader';\nimport assert from '../../stub/assert';\nimport { newPromise, promiseResolvedWith, queueMicrotask, uponRejection } from '../helpers/webidl';\nimport {\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError\n} from './default-controller';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamControllerClose,\n  ReadableByteStreamControllerEnqueue,\n  ReadableByteStreamControllerError,\n  ReadableByteStreamControllerGetBYOBRequest,\n  ReadableByteStreamControllerRespond,\n  ReadableByteStreamControllerRespondWithNewView\n} from './byte-stream-controller';\nimport { CreateArrayFromList } from '../abstract-ops/ecmascript';\nimport { CloneAsUint8Array } from '../abstract-ops/miscellaneous';\nimport type { NonShared } from '../helpers/array-buffer-view';\n\nexport function ReadableStreamTee<R>(stream: ReadableStream<R>,\n                                     cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n  if (IsReadableByteStreamController(stream._readableStreamController)) {\n    return ReadableByteStreamTee(stream as unknown as ReadableByteStream) as\n      unknown as [ReadableStream<R>, ReadableStream<R>];\n  }\n  return ReadableStreamDefaultTee(stream, cloneForBranch2);\n}\n\nexport function ReadableStreamDefaultTee<R>(\n  stream: ReadableStream<R>,\n  cloneForBranch2: boolean\n): [DefaultReadableStream<R>, DefaultReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n\n  let reading = false;\n  let readAgain = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: DefaultReadableStream<R>;\n  let branch2: DefaultReadableStream<R>;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<undefined>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function pullAlgorithm(): Promise<void> {\n    if (reading) {\n      readAgain = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgain = false;\n          const chunk1 = chunk;\n          const chunk2 = chunk;\n\n          // There is no way to access the cloning code right now in the reference implementation.\n          // If we add one then we'll need an implementation for serializable objects.\n          // if (!canceled2 && cloneForBranch2) {\n          //   chunk2 = StructuredDeserialize(StructuredSerialize(chunk2));\n          // }\n\n          if (!canceled1) {\n            ReadableStreamDefaultControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableStreamDefaultControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgain) {\n            pullAlgorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableStreamDefaultControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableStreamDefaultControllerClose(branch2._readableStreamController);\n        }\n\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm() {\n    // do nothing\n  }\n\n  branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm);\n  branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm);\n\n  uponRejection(reader._closedPromise, (r: any) => {\n    ReadableStreamDefaultControllerError(branch1._readableStreamController, r);\n    ReadableStreamDefaultControllerError(branch2._readableStreamController, r);\n    if (!canceled1 || !canceled2) {\n      resolveCancelPromise(undefined);\n    }\n    return null;\n  });\n\n  return [branch1, branch2];\n}\n\nexport function ReadableByteStreamTee(stream: ReadableByteStream): [ReadableByteStream, ReadableByteStream] {\n  assert(IsReadableStream(stream));\n  assert(IsReadableByteStreamController(stream._readableStreamController));\n\n  let reader: ReadableStreamReader<NonShared<Uint8Array>> = AcquireReadableStreamDefaultReader(stream);\n  let reading = false;\n  let readAgainForBranch1 = false;\n  let readAgainForBranch2 = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableByteStream;\n  let branch2: ReadableByteStream;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<void>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function forwardReaderError(thisReader: ReadableStreamReader<NonShared<Uint8Array>>) {\n    uponRejection(thisReader._closedPromise, r => {\n      if (thisReader !== reader) {\n        return null;\n      }\n      ReadableByteStreamControllerError(branch1._readableStreamController, r);\n      ReadableByteStreamControllerError(branch2._readableStreamController, r);\n      if (!canceled1 || !canceled2) {\n        resolveCancelPromise(undefined);\n      }\n      return null;\n    });\n  }\n\n  function pullWithDefaultReader() {\n    if (IsReadableStreamBYOBReader(reader)) {\n      assert(reader._readIntoRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamDefaultReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const readRequest: ReadRequest<NonShared<Uint8Array>> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const chunk1 = chunk;\n          let chunk2 = chunk;\n          if (!canceled1 && !canceled2) {\n            try {\n              chunk2 = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(branch1._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(branch2._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n          }\n\n          if (!canceled1) {\n            ReadableByteStreamControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableByteStreamControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableByteStreamControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableByteStreamControllerClose(branch2._readableStreamController);\n        }\n        if (branch1._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch1._readableStreamController, 0);\n        }\n        if (branch2._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch2._readableStreamController, 0);\n        }\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n  }\n\n  function pullWithBYOBReader(view: NonShared<ArrayBufferView>, forBranch2: boolean) {\n    if (IsReadableStreamDefaultReader<NonShared<Uint8Array>>(reader)) {\n      assert(reader._readRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamBYOBReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const byobBranch = forBranch2 ? branch2 : branch1;\n    const otherBranch = forBranch2 ? branch1 : branch2;\n\n    const readIntoRequest: ReadIntoRequest<NonShared<ArrayBufferView>> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const byobCanceled = forBranch2 ? canceled2 : canceled1;\n          const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n          if (!otherCanceled) {\n            let clonedChunk;\n            try {\n              clonedChunk = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(byobBranch._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(otherBranch._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n            if (!byobCanceled) {\n              ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n            }\n            ReadableByteStreamControllerEnqueue(otherBranch._readableStreamController, clonedChunk);\n          } else if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: chunk => {\n        reading = false;\n\n        const byobCanceled = forBranch2 ? canceled2 : canceled1;\n        const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n        if (!byobCanceled) {\n          ReadableByteStreamControllerClose(byobBranch._readableStreamController);\n        }\n        if (!otherCanceled) {\n          ReadableByteStreamControllerClose(otherBranch._readableStreamController);\n        }\n\n        if (chunk !== undefined) {\n          assert(chunk.byteLength === 0);\n\n          if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n          if (!otherCanceled && otherBranch._readableStreamController._pendingPullIntos.length > 0) {\n            ReadableByteStreamControllerRespond(otherBranch._readableStreamController, 0);\n          }\n        }\n\n        if (!byobCanceled || !otherCanceled) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamBYOBReaderRead(reader, view, 1, readIntoRequest);\n  }\n\n  function pull1Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch1 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch1._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, false);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function pull2Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch2 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch2._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, true);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm(): void {\n    return;\n  }\n\n  branch1 = CreateReadableByteStream(startAlgorithm, pull1Algorithm, cancel1Algorithm);\n  branch2 = CreateReadableByteStream(startAlgorithm, pull2Algorithm, cancel2Algorithm);\n\n  forwardReaderError(reader);\n\n  return [branch1, branch2];\n}\n", "import { CreateReadableStream, type DefaultReadableStream } from '../readable-stream';\nimport {\n  isReadableStreamLike,\n  type ReadableStreamDefaultReaderLike,\n  type ReadableStreamLike\n} from './readable-stream-like';\nimport { ReadableStreamDefaultControllerClose, ReadableStreamDefaultControllerEnqueue } from './default-controller';\nimport { GetIterator, GetMethod, IteratorComplete, IteratorNext, IteratorValue } from '../abstract-ops/ecmascript';\nimport { promiseRejectedWith, promiseResolvedWith, reflectCall, transformPromiseWith } from '../helpers/webidl';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { noop } from '../../utils';\n\nexport function ReadableStreamFrom<R>(\n  source: Iterable<R> | AsyncIterable<R> | ReadableStreamLike<R>\n): DefaultReadableStream<R> {\n  if (isReadableStreamLike(source)) {\n    return ReadableStreamFromDefaultReader(source.getReader());\n  }\n  return ReadableStreamFromIterable(source);\n}\n\nexport function ReadableStreamFromIterable<R>(asyncIterable: Iterable<R> | AsyncIterable<R>): DefaultReadableStream<R> {\n  let stream: DefaultReadableStream<R>;\n  const iteratorRecord = GetIterator(asyncIterable, 'async');\n\n  const startAlgorithm = noop;\n\n  function pullAlgorithm(): Promise<void> {\n    let nextResult;\n    try {\n      nextResult = IteratorNext(iteratorRecord);\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const nextPromise = promiseResolvedWith(nextResult);\n    return transformPromiseWith(nextPromise, iterResult => {\n      if (!typeIsObject(iterResult)) {\n        throw new TypeError('The promise returned by the iterator.next() method must fulfill with an object');\n      }\n      const done = IteratorComplete(iterResult);\n      if (done) {\n        ReadableStreamDefaultControllerClose(stream._readableStreamController);\n      } else {\n        const value = IteratorValue(iterResult);\n        ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n      }\n    });\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    const iterator = iteratorRecord.iterator;\n    let returnMethod: (typeof iterator)['return'] | undefined;\n    try {\n      returnMethod = GetMethod(iterator, 'return');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    if (returnMethod === undefined) {\n      return promiseResolvedWith(undefined);\n    }\n    let returnResult: IteratorResult<R> | Promise<IteratorResult<R>>;\n    try {\n      returnResult = reflectCall(returnMethod, iterator, [reason]);\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const returnPromise = promiseResolvedWith(returnResult);\n    return transformPromiseWith(returnPromise, iterResult => {\n      if (!typeIsObject(iterResult)) {\n        throw new TypeError('The promise returned by the iterator.return() method must fulfill with an object');\n      }\n      return undefined;\n    });\n  }\n\n  stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n  return stream;\n}\n\nexport function ReadableStreamFromDefaultReader<R>(\n  reader: ReadableStreamDefaultReaderLike<R>\n): DefaultReadableStream<R> {\n  let stream: DefaultReadableStream<R>;\n\n  const startAlgorithm = noop;\n\n  function pullAlgorithm(): Promise<void> {\n    let readPromise;\n    try {\n      readPromise = reader.read();\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    return transformPromiseWith(readPromise, readResult => {\n      if (!typeIsObject(readResult)) {\n        throw new TypeError('The promise returned by the reader.read() method must fulfill with an object');\n      }\n      if (readResult.done) {\n        ReadableStreamDefaultControllerClose(stream._readableStreamController);\n      } else {\n        const value = readResult.value;\n        ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n      }\n    });\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    try {\n      return promiseResolvedWith(reader.cancel(reason));\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n  }\n\n  stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n  return stream;\n}\n", "import { typeIsObject } from '../helpers/miscellaneous';\nimport type { ReadableStreamDefaultReadResult } from './default-reader';\n\n/**\n * A common interface for a `ReadadableStream` implementation.\n *\n * @public\n */\nexport interface ReadableStreamLike<R = any> {\n  readonly locked: boolean;\n\n  getReader(): ReadableStreamDefaultReaderLike<R>;\n}\n\n/**\n * A common interface for a `ReadableStreamDefaultReader` implementation.\n *\n * @public\n */\nexport interface ReadableStreamDefaultReaderLike<R = any> {\n  readonly closed: Promise<undefined>;\n\n  cancel(reason?: any): Promise<void>;\n\n  read(): Promise<ReadableStreamDefaultReadResult<R>>;\n\n  releaseLock(): void;\n}\n\nexport function isReadableStreamLike<R>(stream: unknown): stream is ReadableStreamLike<R> {\n  return typeIsObject(stream) && typeof (stream as ReadableStreamLike<R>).getReader !== 'undefined';\n}\n", "import { assertDictionary, assertFunction, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport type {\n  ReadableStreamController,\n  UnderlyingByteSource,\n  UnderlyingDefaultOrByteSource,\n  UnderlyingDefaultOrByteSourcePullCallback,\n  UnderlyingDefaultOrByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  ValidatedUnderlyingDefaultOrByteSource\n} from '../readable-stream/underlying-source';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\n\nexport function convertUnderlyingDefaultOrByteSource<R>(\n  source: UnderlyingSource<R> | UnderlyingByteSource | null,\n  context: string\n): ValidatedUnderlyingDefaultOrByteSource<R> {\n  assertDictionary(source, context);\n  const original = source as (UnderlyingDefaultOrByteSource<R> | null);\n  const autoAllocateChunkSize = original?.autoAllocateChunkSize;\n  const cancel = original?.cancel;\n  const pull = original?.pull;\n  const start = original?.start;\n  const type = original?.type;\n  return {\n    autoAllocateChunkSize: autoAllocateChunkSize === undefined ?\n      undefined :\n      convertUnsignedLongLongWithEnforceRange(\n        autoAllocateChunkSize,\n        `${context} has member 'autoAllocateChunkSize' that`\n      ),\n    cancel: cancel === undefined ?\n      undefined :\n      convertUnderlyingSourceCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    pull: pull === undefined ?\n      undefined :\n      convertUnderlyingSourcePullCallback(pull, original!, `${context} has member 'pull' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSourceStartCallback(start, original!, `${context} has member 'start' that`),\n    type: type === undefined ? undefined : convertReadableStreamType(type, `${context} has member 'type' that`)\n  };\n}\n\nfunction convertUnderlyingSourceCancelCallback(\n  fn: UnderlyingSourceCancelCallback,\n  original: UnderlyingDefaultOrByteSource,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSourcePullCallback<R>(\n  fn: UnderlyingDefaultOrByteSourcePullCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): (controller: ReadableStreamController<R>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSourceStartCallback<R>(\n  fn: UnderlyingDefaultOrByteSourceStartCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): UnderlyingDefaultOrByteSourceStartCallback<R> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertReadableStreamType(type: string, context: string): 'bytes' {\n  type = `${type}`;\n  if (type !== 'bytes') {\n    throw new TypeError(`${context} '${type}' is not a valid enumeration value for ReadableStreamType`);\n  }\n  return type;\n}\n", "import { assertDictionary } from './basic';\nimport type { StreamPipeOptions, ValidatedStreamPipeOptions } from '../readable-stream/pipe-options';\nimport { type AbortSignal, isAbortSignal } from '../abort-signal';\n\nexport function convertPipeOptions(options: StreamPipeOptions | null | undefined,\n                                   context: string): ValidatedStreamPipeOptions {\n  assertDictionary(options, context);\n  const preventAbort = options?.preventAbort;\n  const preventCancel = options?.preventCancel;\n  const preventClose = options?.preventClose;\n  const signal = options?.signal;\n  if (signal !== undefined) {\n    assertAbortSignal(signal, `${context} has member 'signal' that`);\n  }\n  return {\n    preventAbort: <PERSON><PERSON><PERSON>(preventAbort),\n    preventCancel: <PERSON><PERSON>an(preventCancel),\n    preventClose: Boolean(preventClose),\n    signal\n  };\n}\n\nfunction assertAbortSignal(signal: unknown, context: string): asserts signal is AbortSignal {\n  if (!isAbortSignal(signal)) {\n    throw new TypeError(`${context} is not an AbortSignal.`);\n  }\n}\n", "import assert from '../stub/assert';\nimport {\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith\n} from './helpers/webidl';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { AcquireReadableStreamAsyncIterator, type ReadableStreamAsyncIterator } from './readable-stream/async-iterator';\nimport { defaultReaderClosedPromiseReject, defaultReaderClosedPromiseResolve } from './readable-stream/generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderErrorReadRequests,\n  type ReadableStreamDefaultReadResult\n} from './readable-stream/default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderErrorReadIntoRequests,\n  type ReadableStreamBYOBReadResult\n} from './readable-stream/byob-reader';\nimport { ReadableStreamPipeTo } from './readable-stream/pipe';\nimport { ReadableStreamTee } from './readable-stream/tee';\nimport { ReadableStreamFrom } from './readable-stream/from';\nimport { IsWritableStream, IsWritableStreamLocked, WritableStream } from './writable-stream';\nimport { SimpleQueue } from './simple-queue';\nimport {\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  SetUpReadableByteStreamController,\n  SetUpReadableByteStreamControllerFromUnderlyingSource\n} from './readable-stream/byte-stream-controller';\nimport {\n  ReadableStreamDefaultController,\n  SetUpReadableStreamDefaultController,\n  SetUpReadableStreamDefaultControllerFromUnderlyingSource\n} from './readable-stream/default-controller';\nimport type {\n  UnderlyingByteSource,\n  UnderlyingByteSourcePullCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceStartCallback\n} from './readable-stream/underlying-source';\nimport { noop } from '../utils';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { CreateArrayFromList, SymbolAsyncIterator } from './abstract-ops/ecmascript';\nimport { CancelSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertUnderlyingDefaultOrByteSource } from './validators/underlying-source';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ReadableStreamGetReaderOptions\n} from './readable-stream/reader-options';\nimport { convertReaderOptions } from './validators/reader-options';\nimport type { StreamPipeOptions, ValidatedStreamPipeOptions } from './readable-stream/pipe-options';\nimport type { ReadableStreamIteratorOptions } from './readable-stream/iterator-options';\nimport { convertIteratorOptions } from './validators/iterator-options';\nimport { convertPipeOptions } from './validators/pipe-options';\nimport type { ReadableWritablePair } from './readable-stream/readable-writable-pair';\nimport { convertReadableWritablePair } from './validators/readable-writable-pair';\nimport type { ReadableStreamDefaultReaderLike, ReadableStreamLike } from './readable-stream/readable-stream-like';\nimport type { NonShared } from './helpers/array-buffer-view';\n\nexport type DefaultReadableStream<R = any> = ReadableStream<R> & {\n  _readableStreamController: ReadableStreamDefaultController<R>\n};\n\nexport type ReadableByteStream = ReadableStream<NonShared<Uint8Array>> & {\n  _readableStreamController: ReadableByteStreamController\n};\n\ntype ReadableStreamState = 'readable' | 'closed' | 'errored';\n\n/**\n * A readable stream represents a source of data, from which you can read.\n *\n * @public\n */\nexport class ReadableStream<R = any> implements AsyncIterable<R> {\n  /** @internal */\n  _state!: ReadableStreamState;\n  /** @internal */\n  _reader: ReadableStreamReader<R> | undefined;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _disturbed!: boolean;\n  /** @internal */\n  _readableStreamController!: ReadableStreamDefaultController<R> | ReadableByteStreamController;\n\n  constructor(underlyingSource: UnderlyingByteSource, strategy?: { highWaterMark?: number; size?: undefined });\n  constructor(underlyingSource?: UnderlyingSource<R>, strategy?: QueuingStrategy<R>);\n  constructor(rawUnderlyingSource: UnderlyingSource<R> | UnderlyingByteSource | null | undefined = {},\n              rawStrategy: QueuingStrategy<R> | null | undefined = {}) {\n    if (rawUnderlyingSource === undefined) {\n      rawUnderlyingSource = null;\n    } else {\n      assertObject(rawUnderlyingSource, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, 'First parameter');\n\n    InitializeReadableStream(this);\n\n    if (underlyingSource.type === 'bytes') {\n      if (strategy.size !== undefined) {\n        throw new RangeError('The strategy for a byte stream cannot have a size function');\n      }\n      const highWaterMark = ExtractHighWaterMark(strategy, 0);\n      SetUpReadableByteStreamControllerFromUnderlyingSource(\n        this as unknown as ReadableByteStream,\n        underlyingSource,\n        highWaterMark\n      );\n    } else {\n      assert(underlyingSource.type === undefined);\n      const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n      const highWaterMark = ExtractHighWaterMark(strategy, 1);\n      SetUpReadableStreamDefaultControllerFromUnderlyingSource(\n        this,\n        underlyingSource,\n        highWaterMark,\n        sizeAlgorithm\n      );\n    }\n  }\n\n  /**\n   * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.\n   */\n  get locked(): boolean {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsReadableStreamLocked(this);\n  }\n\n  /**\n   * Cancels the stream, signaling a loss of interest in the stream by a consumer.\n   *\n   * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}\n   * method, which might or might not use it.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('cancel'));\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot cancel a stream that already has a reader'));\n    }\n\n    return ReadableStreamCancel(this, reason);\n  }\n\n  /**\n   * Creates a {@link ReadableStreamBYOBReader} and locks the stream to the new reader.\n   *\n   * This call behaves the same way as the no-argument variant, except that it only works on readable byte streams,\n   * i.e. streams which were constructed specifically with the ability to handle \"bring your own buffer\" reading.\n   * The returned BYOB reader provides the ability to directly read individual chunks from the stream via its\n   * {@link ReadableStreamBYOBReader.read | read()} method, into developer-supplied buffers, allowing more precise\n   * control over allocation.\n   */\n  getReader({ mode }: { mode: 'byob' }): ReadableStreamBYOBReader;\n  /**\n   * Creates a {@link ReadableStreamDefaultReader} and locks the stream to the new reader.\n   * While the stream is locked, no other reader can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to consume a stream\n   * in its entirety. By getting a reader for the stream, you can ensure nobody else can interleave reads with yours\n   * or cancel the stream, which would interfere with your abstraction.\n   */\n  getReader(): ReadableStreamDefaultReader<R>;\n  getReader(\n    rawOptions: ReadableStreamGetReaderOptions | null | undefined = undefined\n  ): ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('getReader');\n    }\n\n    const options = convertReaderOptions(rawOptions, 'First parameter');\n\n    if (options.mode === undefined) {\n      return AcquireReadableStreamDefaultReader(this);\n    }\n\n    assert(options.mode === 'byob');\n    return AcquireReadableStreamBYOBReader(this as unknown as ReadableByteStream);\n  }\n\n  /**\n   * Provides a convenient, chainable way of piping this readable stream through a transform stream\n   * (or any other `{ writable, readable }` pair). It simply {@link ReadableStream.pipeTo | pipes} the stream\n   * into the writable side of the supplied pair, and returns the readable side for further use.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeThrough<RS extends ReadableStream>(\n    transform: { readable: RS; writable: WritableStream<R> },\n    options?: StreamPipeOptions\n  ): RS;\n  pipeThrough<RS extends ReadableStream>(\n    rawTransform: { readable: RS; writable: WritableStream<R> } | null | undefined,\n    rawOptions: StreamPipeOptions | null | undefined = {}\n  ): RS {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('pipeThrough');\n    }\n    assertRequiredArgument(rawTransform, 1, 'pipeThrough');\n\n    const transform = convertReadableWritablePair(rawTransform, 'First parameter');\n    const options = convertPipeOptions(rawOptions, 'Second parameter');\n\n    if (IsReadableStreamLocked(this)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream');\n    }\n    if (IsWritableStreamLocked(transform.writable)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream');\n    }\n\n    const promise = ReadableStreamPipeTo(\n      this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n\n    setPromiseIsHandledToTrue(promise);\n\n    return transform.readable;\n  }\n\n  /**\n   * Pipes this readable stream to a given writable stream. The way in which the piping process behaves under\n   * various error conditions can be customized with a number of passed options. It returns a promise that fulfills\n   * when the piping process completes successfully, or rejects if any errors were encountered.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>;\n  pipeTo(destination: WritableStream<R> | null | undefined,\n         rawOptions: StreamPipeOptions | null | undefined = {}): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('pipeTo'));\n    }\n\n    if (destination === undefined) {\n      return promiseRejectedWith(`Parameter 1 is required in 'pipeTo'.`);\n    }\n    if (!IsWritableStream(destination)) {\n      return promiseRejectedWith(\n        new TypeError(`ReadableStream.prototype.pipeTo's first argument must be a WritableStream`)\n      );\n    }\n\n    let options: ValidatedStreamPipeOptions;\n    try {\n      options = convertPipeOptions(rawOptions, 'Second parameter');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream')\n      );\n    }\n    if (IsWritableStreamLocked(destination)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream')\n      );\n    }\n\n    return ReadableStreamPipeTo<R>(\n      this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n  }\n\n  /**\n   * Tees this readable stream, returning a two-element array containing the two resulting branches as\n   * new {@link ReadableStream} instances.\n   *\n   * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.\n   * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be\n   * propagated to the stream's underlying source.\n   *\n   * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,\n   * this could allow interference between the two branches.\n   */\n  tee(): [ReadableStream<R>, ReadableStream<R>] {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('tee');\n    }\n\n    const branches = ReadableStreamTee(this, false);\n    return CreateArrayFromList(branches);\n  }\n\n  /**\n   * Asynchronously iterates over the chunks in the stream's internal queue.\n   *\n   * Asynchronously iterating over the stream will lock it, preventing any other consumer from acquiring a reader.\n   * The lock will be released if the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method\n   * is called, e.g. by breaking out of the loop.\n   *\n   * By default, calling the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method will also\n   * cancel the stream. To prevent this, use the stream's {@link ReadableStream.values | values()} method, passing\n   * `true` for the `preventCancel` option.\n   */\n  values(options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n  values(rawOptions: ReadableStreamIteratorOptions | null | undefined = undefined): ReadableStreamAsyncIterator<R> {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('values');\n    }\n\n    const options = convertIteratorOptions(rawOptions, 'First parameter');\n    return AcquireReadableStreamAsyncIterator<R>(this, options.preventCancel);\n  }\n\n  /**\n   * {@inheritDoc ReadableStream.values}\n   */\n  [Symbol.asyncIterator](options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n\n  [SymbolAsyncIterator](options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R> {\n    // Stub implementation, overridden below\n    return this.values(options);\n  }\n\n  /**\n   * Creates a new ReadableStream wrapping the provided iterable or async iterable.\n   *\n   * This can be used to adapt various kinds of objects into a readable stream,\n   * such as an array, an async generator, or a Node.js readable stream.\n   */\n  static from<R>(asyncIterable: Iterable<R> | AsyncIterable<R> | ReadableStreamLike<R>): ReadableStream<R> {\n    return ReadableStreamFrom(asyncIterable);\n  }\n}\n\nObject.defineProperties(ReadableStream, {\n  from: { enumerable: true }\n});\nObject.defineProperties(ReadableStream.prototype, {\n  cancel: { enumerable: true },\n  getReader: { enumerable: true },\n  pipeThrough: { enumerable: true },\n  pipeTo: { enumerable: true },\n  tee: { enumerable: true },\n  values: { enumerable: true },\n  locked: { enumerable: true }\n});\nsetFunctionName(ReadableStream.from, 'from');\nsetFunctionName(ReadableStream.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStream.prototype.getReader, 'getReader');\nsetFunctionName(ReadableStream.prototype.pipeThrough, 'pipeThrough');\nsetFunctionName(ReadableStream.prototype.pipeTo, 'pipeTo');\nsetFunctionName(ReadableStream.prototype.tee, 'tee');\nsetFunctionName(ReadableStream.prototype.values, 'values');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.toStringTag, {\n    value: 'ReadableStream',\n    configurable: true\n  });\n}\nObject.defineProperty(ReadableStream.prototype, SymbolAsyncIterator, {\n  value: ReadableStream.prototype.values,\n  writable: true,\n  configurable: true\n});\n\nexport type {\n  ReadableStreamAsyncIterator,\n  ReadableStreamDefaultReadResult,\n  ReadableStreamBYOBReadResult,\n  ReadableStreamBYOBReaderReadOptions,\n  UnderlyingByteSource,\n  UnderlyingSource,\n  UnderlyingSourceStartCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceCancelCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingByteSourcePullCallback,\n  StreamPipeOptions,\n  ReadableWritablePair,\n  ReadableStreamIteratorOptions,\n  ReadableStreamLike,\n  ReadableStreamDefaultReaderLike\n};\n\n// Abstract operations for the ReadableStream.\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableStream<R>(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>,\n  highWaterMark = 1,\n  sizeAlgorithm: QueuingStrategySizeCallback<R> = () => 1\n): DefaultReadableStream<R> {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: DefaultReadableStream<R> = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n\n  return stream;\n}\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableByteStream(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>\n): ReadableByteStream {\n  const stream: ReadableByteStream = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n  SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, 0, undefined);\n\n  return stream;\n}\n\nfunction InitializeReadableStream(stream: ReadableStream) {\n  stream._state = 'readable';\n  stream._reader = undefined;\n  stream._storedError = undefined;\n  stream._disturbed = false;\n}\n\nexport function IsReadableStream(x: unknown): x is ReadableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readableStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStream;\n}\n\nexport function IsReadableStreamDisturbed(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  return stream._disturbed;\n}\n\nexport function IsReadableStreamLocked(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  if (stream._reader === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamCancel<R>(stream: ReadableStream<R>, reason: any): Promise<undefined> {\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  ReadableStreamClose(stream);\n\n  const reader = stream._reader;\n  if (reader !== undefined && IsReadableStreamBYOBReader(reader)) {\n    const readIntoRequests = reader._readIntoRequests;\n    reader._readIntoRequests = new SimpleQueue();\n    readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._closeSteps(undefined);\n    });\n  }\n\n  const sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);\n  return transformPromiseWith(sourceCancelPromise, noop);\n}\n\nexport function ReadableStreamClose<R>(stream: ReadableStream<R>): void {\n  assert(stream._state === 'readable');\n\n  stream._state = 'closed';\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseResolve(reader);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    const readRequests = reader._readRequests;\n    reader._readRequests = new SimpleQueue();\n    readRequests.forEach(readRequest => {\n      readRequest._closeSteps();\n    });\n  }\n}\n\nexport function ReadableStreamError<R>(stream: ReadableStream<R>, e: any): void {\n  assert(IsReadableStream(stream));\n  assert(stream._state === 'readable');\n\n  stream._state = 'errored';\n  stream._storedError = e;\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseReject(reader, e);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n  } else {\n    assert(IsReadableStreamBYOBReader(reader));\n    ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n  }\n}\n\n// Readers\n\nexport type ReadableStreamReader<R> = ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader;\n\nexport {\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader\n};\n\n// Controllers\n\nexport {\n  ReadableStreamDefaultController,\n  ReadableStreamBYOBRequest,\n  ReadableByteStreamController\n};\n\n// Helper functions for the ReadableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStream.prototype.${name} can only be used on a ReadableStream`);\n}\n", "import { assertDictionary, assertRequiredField } from './basic';\nimport { ReadableStream } from '../readable-stream';\nimport { WritableStream } from '../writable-stream';\nimport { assertReadableStream } from './readable-stream';\nimport { assertWritableStream } from './writable-stream';\n\nexport function convertReadableWritablePair<RS extends ReadableStream, WS extends WritableStream>(\n  pair: { readable: RS; writable: WS } | null | undefined,\n  context: string\n): { readable: RS; writable: WS } {\n  assertDictionary(pair, context);\n\n  const readable = pair?.readable;\n  assertRequiredField(readable, 'readable', 'ReadableWritablePair');\n  assertReadableStream(readable, `${context} has member 'readable' that`);\n\n  const writable = pair?.writable;\n  assertRequiredField(writable, 'writable', 'ReadableWritablePair');\n  assertWritableStream(writable, `${context} has member 'writable' that`);\n\n  return { readable, writable };\n}\n", "import { assertDictionary } from './basic';\nimport type {\n  ReadableStreamIteratorOptions,\n  ValidatedReadableStreamIteratorOptions\n} from '../readable-stream/iterator-options';\n\nexport function convertIteratorOptions(options: ReadableStreamIteratorOptions | null | undefined,\n                                       context: string): ValidatedReadableStreamIteratorOptions {\n  assertDictionary(options, context);\n  const preventCancel = options?.preventCancel;\n  return { preventCancel: Boolean(preventCancel) };\n}\n", "import type { QueuingStrategyInit } from '../queuing-strategy';\nimport { assertDictionary, assertRequiredField, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategyInit(init: QueuingStrategyInit | null | undefined,\n                                           context: string): QueuingStrategyInit {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  assertRequiredField(highWaterMark, 'highWaterMark', 'QueuingStrategyInit');\n  return {\n    highWaterMark: convertUnrestrictedDouble(highWaterMark)\n  };\n}\n", "import type { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst byteLengthSizeFunction = (chunk: ArrayBufferView): number => {\n  return chunk.byteLength;\n};\nsetFunctionName(byteLengthSizeFunction, 'size');\n\n/**\n * A queuing strategy that counts the number of bytes in each chunk.\n *\n * @public\n */\nexport default class ByteLengthQueuingStrategy implements QueuingStrategy<ArrayBufferView> {\n  /** @internal */\n  readonly _byteLengthQueuingStrategyHighWaterMark: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'ByteLengthQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('highWaterMark');\n    }\n    return this._byteLengthQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by returning the value of its `byteLength` property.\n   */\n  get size(): (chunk: ArrayBufferView) => number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('size');\n    }\n    return byteLengthSizeFunction;\n  }\n}\n\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ByteLengthQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'ByteLengthQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the ByteLengthQueuingStrategy.\n\nfunction byteLengthBrandCheckException(name: string): TypeError {\n  return new TypeError(`ByteLengthQueuingStrategy.prototype.${name} can only be used on a ByteLengthQueuingStrategy`);\n}\n\nexport function IsByteLengthQueuingStrategy(x: any): x is ByteLengthQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_byteLengthQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof ByteLengthQueuingStrategy;\n}\n", "import type { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst countSizeFunction = (): 1 => {\n  return 1;\n};\nsetFunctionName(countSizeFunction, 'size');\n\n/**\n * A queuing strategy that counts the number of chunks.\n *\n * @public\n */\nexport default class CountQueuingStrategy implements QueuingStrategy<any> {\n  /** @internal */\n  readonly _countQueuingStrategyHighWaterMark!: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'CountQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._countQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('highWaterMark');\n    }\n    return this._countQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by always returning 1.\n   * This ensures that the total queue size is a count of the number of chunks in the queue.\n   */\n  get size(): (chunk: any) => 1 {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('size');\n    }\n    return countSizeFunction;\n  }\n}\n\nObject.defineProperties(CountQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(CountQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'CountQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the CountQueuingStrategy.\n\nfunction countBrandCheckException(name: string): TypeError {\n  return new TypeError(`CountQueuingStrategy.prototype.${name} can only be used on a CountQueuingStrategy`);\n}\n\nexport function IsCountQueuingStrategy(x: any): x is CountQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_countQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof CountQueuingStrategy;\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from '../transform-stream/transformer';\nimport { TransformStreamDefaultController } from '../transform-stream';\n\nexport function convertTransformer<I, O>(original: Transformer<I, O> | null,\n                                         context: string): ValidatedTransformer<I, O> {\n  assertDictionary(original, context);\n  const cancel = original?.cancel;\n  const flush = original?.flush;\n  const readableType = original?.readableType;\n  const start = original?.start;\n  const transform = original?.transform;\n  const writableType = original?.writableType;\n  return {\n    cancel: cancel === undefined ?\n      undefined :\n      convertTransformerCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    flush: flush === undefined ?\n      undefined :\n      convertTransformerFlushCallback(flush, original!, `${context} has member 'flush' that`),\n    readableType,\n    start: start === undefined ?\n      undefined :\n      convertTransformerStartCallback(start, original!, `${context} has member 'start' that`),\n    transform: transform === undefined ?\n      undefined :\n      convertTransformerTransformCallback(transform, original!, `${context} has member 'transform' that`),\n    writableType\n  };\n}\n\nfunction convertTransformerFlushCallback<I, O>(\n  fn: TransformerFlushCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): (controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertTransformerStartCallback<I, O>(\n  fn: TransformerStartCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): TransformerStartCallback<O> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertTransformerTransformCallback<I, O>(\n  fn: TransformerTransformCallback<I, O>,\n  original: Transformer<I, O>,\n  context: string\n): (chunk: I, controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: I, controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [chunk, controller]);\n}\n\nfunction convertTransformerCancelCallback<I, O>(\n  fn: TransformerCancelCallback,\n  original: Transformer<I, O>,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith,\n  uponPromise\n} from './helpers/webidl';\nimport { CreateReadableStream, type DefaultReadableStream, ReadableStream } from './readable-stream';\nimport {\n  ReadableStreamDefaultControllerCanCloseOrEnqueue,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError,\n  ReadableStreamDefaultControllerGetDesiredSize,\n  ReadableStreamDefaultControllerHasBackpressure\n} from './readable-stream/default-controller';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { CreateWritableStream, WritableStream, WritableStreamDefaultControllerErrorIfNeeded } from './writable-stream';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from './transform-stream/transformer';\nimport { convertTransformer } from './validators/transformer';\n\n// Class TransformStream\n\n/**\n * A transform stream consists of a pair of streams: a {@link WritableStream | writable stream},\n * known as its writable side, and a {@link ReadableStream | readable stream}, known as its readable side.\n * In a manner specific to the transform stream in question, writes to the writable side result in new data being\n * made available for reading from the readable side.\n *\n * @public\n */\nexport class TransformStream<I = any, O = any> {\n  /** @internal */\n  _writable!: WritableStream<I>;\n  /** @internal */\n  _readable!: DefaultReadableStream<O>;\n  /** @internal */\n  _backpressure!: boolean;\n  /** @internal */\n  _backpressureChangePromise!: Promise<void>;\n  /** @internal */\n  _backpressureChangePromise_resolve!: () => void;\n  /** @internal */\n  _transformStreamController!: TransformStreamDefaultController<O>;\n\n  constructor(\n    transformer?: Transformer<I, O>,\n    writableStrategy?: QueuingStrategy<I>,\n    readableStrategy?: QueuingStrategy<O>\n  );\n  constructor(rawTransformer: Transformer<I, O> | null | undefined = {},\n              rawWritableStrategy: QueuingStrategy<I> | null | undefined = {},\n              rawReadableStrategy: QueuingStrategy<O> | null | undefined = {}) {\n    if (rawTransformer === undefined) {\n      rawTransformer = null;\n    }\n\n    const writableStrategy = convertQueuingStrategy(rawWritableStrategy, 'Second parameter');\n    const readableStrategy = convertQueuingStrategy(rawReadableStrategy, 'Third parameter');\n\n    const transformer = convertTransformer(rawTransformer, 'First parameter');\n    if (transformer.readableType !== undefined) {\n      throw new RangeError('Invalid readableType specified');\n    }\n    if (transformer.writableType !== undefined) {\n      throw new RangeError('Invalid writableType specified');\n    }\n\n    const readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0);\n    const readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy);\n    const writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);\n    const writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);\n\n    let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n    const startPromise = newPromise<void>(resolve => {\n      startPromise_resolve = resolve;\n    });\n\n    InitializeTransformStream(\n      this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm\n    );\n    SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);\n\n    if (transformer.start !== undefined) {\n      startPromise_resolve(transformer.start(this._transformStreamController));\n    } else {\n      startPromise_resolve(undefined);\n    }\n  }\n\n  /**\n   * The readable side of the transform stream.\n   */\n  get readable(): ReadableStream<O> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('readable');\n    }\n\n    return this._readable;\n  }\n\n  /**\n   * The writable side of the transform stream.\n   */\n  get writable(): WritableStream<I> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('writable');\n    }\n\n    return this._writable;\n  }\n}\n\nObject.defineProperties(TransformStream.prototype, {\n  readable: { enumerable: true },\n  writable: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStream.prototype, Symbol.toStringTag, {\n    value: 'TransformStream',\n    configurable: true\n  });\n}\n\nexport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerStartCallback,\n  TransformerFlushCallback,\n  TransformerTransformCallback\n};\n\n// Transform Stream Abstract Operations\n\nexport function CreateTransformStream<I, O>(startAlgorithm: () => void | PromiseLike<void>,\n                                            transformAlgorithm: (chunk: I) => Promise<void>,\n                                            flushAlgorithm: () => Promise<void>,\n                                            cancelAlgorithm: (reason: any) => Promise<void>,\n                                            writableHighWaterMark = 1,\n                                            writableSizeAlgorithm: QueuingStrategySizeCallback<I> = () => 1,\n                                            readableHighWaterMark = 0,\n                                            readableSizeAlgorithm: QueuingStrategySizeCallback<O> = () => 1) {\n  assert(IsNonNegativeNumber(writableHighWaterMark));\n  assert(IsNonNegativeNumber(readableHighWaterMark));\n\n  const stream: TransformStream<I, O> = Object.create(TransformStream.prototype);\n\n  let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n  const startPromise = newPromise<void>(resolve => {\n    startPromise_resolve = resolve;\n  });\n\n  InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark,\n                            readableSizeAlgorithm);\n\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);\n\n  const startResult = startAlgorithm();\n  startPromise_resolve(startResult);\n  return stream;\n}\n\nfunction InitializeTransformStream<I, O>(stream: TransformStream<I, O>,\n                                         startPromise: Promise<void>,\n                                         writableHighWaterMark: number,\n                                         writableSizeAlgorithm: QueuingStrategySizeCallback<I>,\n                                         readableHighWaterMark: number,\n                                         readableSizeAlgorithm: QueuingStrategySizeCallback<O>) {\n  function startAlgorithm(): Promise<void> {\n    return startPromise;\n  }\n\n  function writeAlgorithm(chunk: I): Promise<void> {\n    return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);\n  }\n\n  function abortAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);\n  }\n\n  function closeAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSinkCloseAlgorithm(stream);\n  }\n\n  stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm,\n                                          writableHighWaterMark, writableSizeAlgorithm);\n\n  function pullAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSourcePullAlgorithm(stream);\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSourceCancelAlgorithm(stream, reason);\n  }\n\n  stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark,\n                                          readableSizeAlgorithm);\n\n  // The [[backpressure]] slot is set to undefined so that it can be initialised by TransformStreamSetBackpressure.\n  stream._backpressure = undefined!;\n  stream._backpressureChangePromise = undefined!;\n  stream._backpressureChangePromise_resolve = undefined!;\n  TransformStreamSetBackpressure(stream, true);\n\n  stream._transformStreamController = undefined!;\n}\n\nfunction IsTransformStream(x: unknown): x is TransformStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_transformStreamController')) {\n    return false;\n  }\n\n  return x instanceof TransformStream;\n}\n\n// This is a no-op if both sides are already errored.\nfunction TransformStreamError(stream: TransformStream, e: any) {\n  ReadableStreamDefaultControllerError(stream._readable._readableStreamController, e);\n  TransformStreamErrorWritableAndUnblockWrite(stream, e);\n}\n\nfunction TransformStreamErrorWritableAndUnblockWrite(stream: TransformStream, e: any) {\n  TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController);\n  WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e);\n  TransformStreamUnblockWrite(stream);\n}\n\nfunction TransformStreamUnblockWrite(stream: TransformStream) {\n  if (stream._backpressure) {\n    // Pretend that pull() was called to permit any pending write() calls to complete. TransformStreamSetBackpressure()\n    // cannot be called from enqueue() or pull() once the ReadableStream is errored, so this will will be the final time\n    // _backpressure is set.\n    TransformStreamSetBackpressure(stream, false);\n  }\n}\n\nfunction TransformStreamSetBackpressure(stream: TransformStream, backpressure: boolean) {\n  // Passes also when called during construction.\n  assert(stream._backpressure !== backpressure);\n\n  if (stream._backpressureChangePromise !== undefined) {\n    stream._backpressureChangePromise_resolve();\n  }\n\n  stream._backpressureChangePromise = newPromise(resolve => {\n    stream._backpressureChangePromise_resolve = resolve;\n  });\n\n  stream._backpressure = backpressure;\n}\n\n// Class TransformStreamDefaultController\n\n/**\n * Allows control of the {@link ReadableStream} and {@link WritableStream} of the associated {@link TransformStream}.\n *\n * @public\n */\nexport class TransformStreamDefaultController<O> {\n  /** @internal */\n  _controlledTransformStream: TransformStream<any, O>;\n  /** @internal */\n  _finishPromise: Promise<undefined> | undefined;\n  /** @internal */\n  _finishPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _finishPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _transformAlgorithm: (chunk: any) => Promise<void>;\n  /** @internal */\n  _flushAlgorithm: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.\n   */\n  get desiredSize(): number | null {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    const readableController = this._controlledTransformStream._readable._readableStreamController;\n    return ReadableStreamDefaultControllerGetDesiredSize(readableController);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the readable side of the controlled transform stream.\n   */\n  enqueue(chunk: O): void;\n  enqueue(chunk: O = undefined!): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    TransformStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors both the readable side and the writable side of the controlled transform stream, making all future\n   * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.\n   */\n  error(reason: any = undefined): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    TransformStreamDefaultControllerError(this, reason);\n  }\n\n  /**\n   * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the\n   * transformer only needs to consume a portion of the chunks written to the writable side.\n   */\n  terminate(): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('terminate');\n    }\n\n    TransformStreamDefaultControllerTerminate(this);\n  }\n}\n\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  terminate: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(TransformStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(TransformStreamDefaultController.prototype.error, 'error');\nsetFunctionName(TransformStreamDefaultController.prototype.terminate, 'terminate');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'TransformStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Transform Stream Default Controller Abstract Operations\n\nfunction IsTransformStreamDefaultController<O = any>(x: any): x is TransformStreamDefaultController<O> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledTransformStream')) {\n    return false;\n  }\n\n  return x instanceof TransformStreamDefaultController;\n}\n\nfunction SetUpTransformStreamDefaultController<I, O>(stream: TransformStream<I, O>,\n                                                     controller: TransformStreamDefaultController<O>,\n                                                     transformAlgorithm: (chunk: I) => Promise<void>,\n                                                     flushAlgorithm: () => Promise<void>,\n                                                     cancelAlgorithm: (reason: any) => Promise<void>) {\n  assert(IsTransformStream(stream));\n  assert(stream._transformStreamController === undefined);\n\n  controller._controlledTransformStream = stream;\n  stream._transformStreamController = controller;\n\n  controller._transformAlgorithm = transformAlgorithm;\n  controller._flushAlgorithm = flushAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._finishPromise = undefined;\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\nfunction SetUpTransformStreamDefaultControllerFromTransformer<I, O>(stream: TransformStream<I, O>,\n                                                                    transformer: ValidatedTransformer<I, O>) {\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  let transformAlgorithm: (chunk: I) => Promise<void>;\n  let flushAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (transformer.transform !== undefined) {\n    transformAlgorithm = chunk => transformer.transform!(chunk, controller);\n  } else {\n    transformAlgorithm = chunk => {\n      try {\n        TransformStreamDefaultControllerEnqueue(controller, chunk as unknown as O);\n        return promiseResolvedWith(undefined);\n      } catch (transformResultE) {\n        return promiseRejectedWith(transformResultE);\n      }\n    };\n  }\n\n  if (transformer.flush !== undefined) {\n    flushAlgorithm = () => transformer.flush!(controller);\n  } else {\n    flushAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  if (transformer.cancel !== undefined) {\n    cancelAlgorithm = reason => transformer.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);\n}\n\nfunction TransformStreamDefaultControllerClearAlgorithms(controller: TransformStreamDefaultController<any>) {\n  controller._transformAlgorithm = undefined!;\n  controller._flushAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\nfunction TransformStreamDefaultControllerEnqueue<O>(controller: TransformStreamDefaultController<O>, chunk: O) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController;\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController)) {\n    throw new TypeError('Readable side is not in a state that permits enqueue');\n  }\n\n  // We throttle transform invocations based on the backpressure of the ReadableStream, but we still\n  // accept TransformStreamDefaultControllerEnqueue() calls.\n\n  try {\n    ReadableStreamDefaultControllerEnqueue(readableController, chunk);\n  } catch (e) {\n    // This happens when readableStrategy.size() throws.\n    TransformStreamErrorWritableAndUnblockWrite(stream, e);\n\n    throw stream._readable._storedError;\n  }\n\n  const backpressure = ReadableStreamDefaultControllerHasBackpressure(readableController);\n  if (backpressure !== stream._backpressure) {\n    assert(backpressure);\n    TransformStreamSetBackpressure(stream, true);\n  }\n}\n\nfunction TransformStreamDefaultControllerError(controller: TransformStreamDefaultController<any>, e: any) {\n  TransformStreamError(controller._controlledTransformStream, e);\n}\n\nfunction TransformStreamDefaultControllerPerformTransform<I, O>(controller: TransformStreamDefaultController<O>,\n                                                                chunk: I) {\n  const transformPromise = controller._transformAlgorithm(chunk);\n  return transformPromiseWith(transformPromise, undefined, r => {\n    TransformStreamError(controller._controlledTransformStream, r);\n    throw r;\n  });\n}\n\nfunction TransformStreamDefaultControllerTerminate<O>(controller: TransformStreamDefaultController<O>) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController;\n\n  ReadableStreamDefaultControllerClose(readableController);\n\n  const error = new TypeError('TransformStream terminated');\n  TransformStreamErrorWritableAndUnblockWrite(stream, error);\n}\n\n// TransformStreamDefaultSink Algorithms\n\nfunction TransformStreamDefaultSinkWriteAlgorithm<I, O>(stream: TransformStream<I, O>, chunk: I): Promise<void> {\n  assert(stream._writable._state === 'writable');\n\n  const controller = stream._transformStreamController;\n\n  if (stream._backpressure) {\n    const backpressureChangePromise = stream._backpressureChangePromise;\n    assert(backpressureChangePromise !== undefined);\n    return transformPromiseWith(backpressureChangePromise, () => {\n      const writable = stream._writable;\n      const state = writable._state;\n      if (state === 'erroring') {\n        throw writable._storedError;\n      }\n      assert(state === 'writable');\n      return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n    });\n  }\n\n  return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n}\n\nfunction TransformStreamDefaultSinkAbortAlgorithm<I, O>(stream: TransformStream<I, O>, reason: any): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  // Assign the _finishPromise now so that if _cancelAlgorithm calls readable.cancel() internally,\n  // we don't run the _cancelAlgorithm again.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const cancelPromise = controller._cancelAlgorithm(reason);\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(cancelPromise, () => {\n    if (readable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, readable._storedError);\n    } else {\n      ReadableStreamDefaultControllerError(readable._readableStreamController, reason);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\nfunction TransformStreamDefaultSinkCloseAlgorithm<I, O>(stream: TransformStream<I, O>): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  // Assign the _finishPromise now so that if _flushAlgorithm calls readable.cancel() internally,\n  // we don't also run the _cancelAlgorithm.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const flushPromise = controller._flushAlgorithm();\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(flushPromise, () => {\n    if (readable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, readable._storedError);\n    } else {\n      ReadableStreamDefaultControllerClose(readable._readableStreamController);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\n// TransformStreamDefaultSource Algorithms\n\nfunction TransformStreamDefaultSourcePullAlgorithm(stream: TransformStream): Promise<void> {\n  // Invariant. Enforced by the promises returned by start() and pull().\n  assert(stream._backpressure);\n\n  assert(stream._backpressureChangePromise !== undefined);\n\n  TransformStreamSetBackpressure(stream, false);\n\n  // Prevent the next pull() call until there is backpressure.\n  return stream._backpressureChangePromise;\n}\n\nfunction TransformStreamDefaultSourceCancelAlgorithm<I, O>(stream: TransformStream<I, O>, reason: any): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._writable cannot change after construction, so caching it across a call to user code is safe.\n  const writable = stream._writable;\n\n  // Assign the _finishPromise now so that if _flushAlgorithm calls writable.abort() or\n  // writable.cancel() internally, we don't run the _cancelAlgorithm again, or also run the\n  // _flushAlgorithm.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const cancelPromise = controller._cancelAlgorithm(reason);\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(cancelPromise, () => {\n    if (writable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, writable._storedError);\n    } else {\n      WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, reason);\n      TransformStreamUnblockWrite(stream);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, r);\n    TransformStreamUnblockWrite(stream);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\n// Helper functions for the TransformStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStreamDefaultController.prototype.${name} can only be used on a TransformStreamDefaultController`);\n}\n\nexport function defaultControllerFinishPromiseResolve(controller: TransformStreamDefaultController<any>) {\n  if (controller._finishPromise_resolve === undefined) {\n    return;\n  }\n\n  controller._finishPromise_resolve();\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\nexport function defaultControllerFinishPromiseReject(controller: TransformStreamDefaultController<any>, reason: any) {\n  if (controller._finishPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(controller._finishPromise!);\n  controller._finishPromise_reject(reason);\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\n// Helper functions for the TransformStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStream.prototype.${name} can only be used on a TransformStream`);\n}\n", "import {\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n  ReadableByteStreamController,\n  ReadableStream,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultReader,\n  TransformStream,\n  TransformStreamDefaultController,\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter\n} from './ponyfill';\nimport { globals } from './globals';\n\n// Export\nexport * from './ponyfill';\n\nconst exports = {\n  ReadableStream,\n  ReadableStreamDefaultController,\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader,\n\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter,\n\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n\n  TransformStream,\n  TransformStreamDefaultController\n};\n\n// Add classes to global scope\nif (typeof globals !== 'undefined') {\n  for (const prop in exports) {\n    if (Object.prototype.hasOwnProperty.call(exports, prop)) {\n      Object.defineProperty(globals, prop, {\n        value: exports[prop as (keyof typeof exports)],\n        writable: true,\n        configurable: true\n      });\n    }\n  }\n}\n"], "names": ["noop", "typeIsObject", "x", "rethrowAssertionErrorRejection", "setFunctionName", "fn", "name", "Object", "defineProperty", "value", "configurable", "_a", "originalPromise", "Promise", "originalPromiseThen", "prototype", "then", "originalPromiseReject", "reject", "bind", "newPromise", "executor", "promiseResolvedWith", "resolve", "promiseRejectedWith", "reason", "PerformPromiseThen", "promise", "onFulfilled", "onRejected", "call", "uponPromise", "undefined", "uponFulfillment", "uponRejection", "transformPromiseWith", "fulfillmentH<PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>", "setPromiseIsHandledToTrue", "_queueMicrotask", "callback", "queueMicrotask", "resolvedPromise", "cb", "reflectCall", "F", "V", "args", "TypeError", "Function", "apply", "promiseCall", "SimpleQueue", "constructor", "this", "_cursor", "_size", "_front", "_elements", "_next", "_back", "length", "push", "element", "oldBack", "newBack", "QUEUE_MAX_ARRAY_SIZE", "shift", "oldFront", "newFront", "old<PERSON>ursor", "newCursor", "elements", "for<PERSON>ach", "i", "node", "peek", "front", "cursor", "AbortSteps", "Symbol", "ErrorSteps", "CancelSteps", "PullSteps", "ReleaseSteps", "ReadableStreamReaderGenericInitialize", "reader", "stream", "_ownerReadableStream", "_reader", "_state", "defaultReaderClosedPromiseInitialize", "defaultReaderClosedPromiseResolve", "defaultReaderClosedPromiseInitializeAsResolved", "defaultReaderClosedPromiseInitializeAsRejected", "_storedError", "ReadableStreamReaderGenericCancel", "ReadableStreamCancel", "ReadableStreamReaderGenericRelease", "defaultReaderClosedPromiseReject", "defaultReaderClosedPromiseResetToRejected", "_readableStreamController", "readerLockException", "_closedPromise", "_closedPromise_resolve", "_closedPromise_reject", "NumberIsFinite", "Number", "isFinite", "MathTrunc", "Math", "trunc", "v", "ceil", "floor", "assertDictionary", "obj", "context", "assertFunction", "assertObject", "isObject", "assertRequiredArgument", "position", "assertRequiredField", "field", "convertUnrestrictedDouble", "censorNegativeZero", "convertUnsignedLongLongWithEnforceRange", "upperBound", "MAX_SAFE_INTEGER", "integerPart", "assertReadableStream", "IsReadableStream", "AcquireReadableStreamDefaultReader", "ReadableStreamDefaultReader", "ReadableStreamAddReadRequest", "readRequest", "_readRequests", "ReadableStreamFulfillReadRequest", "chunk", "done", "_closeSteps", "_chunkSteps", "ReadableStreamGetNumReadRequests", "ReadableStreamHasDefaultReader", "IsReadableStreamDefaultReader", "IsReadableStreamLocked", "closed", "defaultReaderBrandCheckException", "cancel", "read", "resolvePromise", "rejectPromise", "ReadableStreamDefaultReaderRead", "_errorSteps", "e", "releaseLock", "ReadableStreamDefaultReaderErrorReadRequests", "ReadableStreamDefaultReaderRelease", "hasOwnProperty", "_disturbed", "readRequests", "defineProperties", "enumerable", "toStringTag", "AsyncIteratorPrototype", "getPrototypeOf", "async", "ReadableStreamAsyncIteratorImpl", "preventCancel", "_ongoingPromise", "_isFinished", "_preventCancel", "next", "nextSteps", "_nextSteps", "returnSteps", "_returnSteps", "result", "ReadableStreamAsyncIteratorPrototype", "IsReadableStreamAsyncIterator", "_asyncIteratorImpl", "streamAsyncIteratorBrandCheckException", "return", "setPrototypeOf", "NumberIsNaN", "isNaN", "CreateArrayFromList", "slice", "CopyDataBlockBytes", "dest", "destOffset", "src", "srcOffset", "n", "Uint8Array", "set", "TransferArrayBuffer", "O", "transfer", "buffer", "structuredClone", "IsDetachedBuffer", "detached", "byteLength", "ArrayBufferSlice", "begin", "end", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GetMethod", "receiver", "prop", "func", "String", "SymbolAsyncIterator", "_c", "asyncIterator", "_b", "for", "GetIterator", "hint", "method", "syncIteratorRecord", "syncIterable", "iterator", "next<PERSON><PERSON><PERSON>", "CreateAsyncFromSyncIterator", "CloneAsUint8Array", "byteOffset", "DequeueValue", "container", "pair", "_queue", "_queueTotalSize", "size", "EnqueueValueWithSize", "Infinity", "RangeError", "ResetQueue", "isDataViewConstructor", "ctor", "DataView", "ReadableStreamBYOBRequest", "view", "IsReadableStreamBYOBRequest", "byobRequestBrandCheckException", "_view", "respond", "bytes<PERSON>ritten", "_associatedReadableByteStreamController", "ReadableByteStreamControllerRespond", "respondWithNewView", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerRespondWithNewView", "ReadableByteStreamController", "byobRequest", "IsReadableByteStreamController", "byteStreamControllerBrandCheckException", "ReadableByteStreamControllerGetBYOBRequest", "desiredSize", "ReadableByteStreamControllerGetDesiredSize", "close", "_closeRequested", "state", "_controlledReadableByteStream", "ReadableByteStreamControllerClose", "enqueue", "ReadableByteStreamControllerEnqueue", "error", "ReadableByteStreamControllerError", "ReadableByteStreamControllerClearPendingPullIntos", "_cancelAlgorithm", "ReadableByteStreamControllerClearAlgorithms", "ReadableByteStreamControllerFillReadRequestFromQueue", "autoAllocateChunkSize", "_autoAllocateChunkSize", "bufferE", "pullIntoDescriptor", "bufferByteLength", "bytesFilled", "minimumFill", "elementSize", "viewConstructor", "readerType", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerCallPullIfNeeded", "firstPullInto", "controller", "shouldPull", "_started", "ReadableStreamHasBYOBReader", "ReadableStreamGetNumReadIntoRequests", "ReadableByteStreamControllerShouldCallPull", "_pulling", "_pullAgain", "_pullAlgorithm", "ReadableByteStreamControllerInvalidateBYOBRequest", "ReadableByteStreamControllerCommitPullIntoDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerConvertPullIntoDescriptor", "readIntoRequest", "_readIntoRequests", "ReadableStreamFulfillReadIntoRequest", "ReadableByteStreamControllerEnqueueChunkToQueue", "ReadableByteStreamControllerEnqueueClonedChunkToQueue", "clonedChunk", "cloneE", "ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue", "firstDescriptor", "ReadableByteStreamControllerShiftPendingPullInto", "ReadableByteStreamControllerFillPullIntoDescriptorFromQueue", "maxBytesToCopy", "min", "maxBytesFilled", "totalBytesToCopyRemaining", "ready", "maxAlignedBytes", "queue", "headOfQueue", "bytesToCopy", "destStart", "ReadableByteStreamControllerFillHeadPullIntoDescriptor", "ReadableByteStreamControllerHandleQueueDrain", "ReadableStreamClose", "_byobRequest", "ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue", "ReadableByteStreamControllerPullInto", "BYTES_PER_ELEMENT", "arrayBufferViewElementSize", "ReadableStreamAddReadIntoRequest", "emptyView", "ReadableByteStreamControllerRespondInternal", "ReadableByteStreamControllerRespondInClosedState", "remainderSize", "ReadableByteStreamControllerRespondInReadableState", "firstPendingPullInto", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerProcessReadRequestsUsingQueue", "ReadableStreamError", "entry", "create", "request", "SetUpReadableStreamBYOBRequest", "_strategyHWM", "viewByteLength", "SetUpReadableByteStreamController", "startAlgorithm", "pullAlgorithm", "cancelAlgorithm", "highWaterMark", "r", "convertReadableStreamReaderMode", "mode", "AcquireReadableStreamBYOBReader", "ReadableStreamBYOBReader", "IsReadableStreamBYOBReader", "byobReaderBrandCheckException", "rawOptions", "options", "convertByobReadOptions", "isDataView", "ReadableStreamBYOBReaderRead", "ReadableStreamBYOBReaderErrorReadIntoRequests", "ReadableStreamBYOBReaderRelease", "readIntoRequests", "ExtractHighWaterMark", "strategy", "defaultHWM", "ExtractSizeAlgorithm", "convertQueuingStrategy", "init", "convertQueuingStrategySize", "convertUnderlyingSinkAbortCallback", "original", "convertUnderlyingSinkCloseCallback", "convertUnderlyingSinkStartCallback", "convertUnderlyingSinkWriteCallback", "assertWritableStream", "IsWritableStream", "supportsAbortController", "AbortController", "WritableStream", "rawUnderlyingSink", "rawStrategy", "underlyingSink", "abort", "start", "type", "write", "convertUnderlyingSink", "InitializeWritableStream", "sizeAlgorithm", "WritableStreamDefaultController", "writeAlgorithm", "closeAlgorithm", "abortAlgorithm", "SetUpWritableStreamDefaultController", "SetUpWritableStreamDefaultControllerFromUnderlyingSink", "locked", "streamBrandCheckException", "IsWritableStreamLocked", "WritableStreamAbort", "WritableStreamCloseQueuedOrInFlight", "WritableStreamClose", "getWriter", "AcquireWritableStreamDefaultWriter", "WritableStreamDefaultWriter", "_writer", "_writableStreamController", "_writeRequests", "_inFlightWriteRequest", "_closeRequest", "_inFlightCloseRequest", "_pendingAbortRequest", "_backpressure", "_abortReason", "_abortController", "_promise", "wasAlreadyErroring", "_resolve", "_reject", "_reason", "_wasAlreadyErroring", "WritableStreamStartErroring", "closeRequest", "writer", "defaultWriterReadyPromiseResolve", "closeSentinel", "WritableStreamDefaultControllerAdvanceQueueIfNeeded", "WritableStreamDealWithRejection", "WritableStreamFinishErroring", "WritableStreamDefaultWriterEnsureReadyPromiseRejected", "WritableStreamHasOperationMarkedInFlight", "storedError", "writeRequest", "WritableStreamRejectCloseAndClosedPromiseIfNeeded", "abortRequest", "defaultWriterClosedPromiseReject", "WritableStreamUpdateBackpressure", "backpressure", "defaultWriterReadyPromiseInitialize", "defaultWriterReadyPromiseReset", "_ownerWritableStream", "defaultWriterReadyPromiseInitializeAsResolved", "defaultWriterClosedPromiseInitialize", "defaultWriterReadyPromiseInitializeAsRejected", "defaultWriterClosedPromiseResolve", "defaultWriterClosedPromiseInitializeAsRejected", "IsWritableStreamDefaultWriter", "defaultWriterBrandCheckException", "defaultWriterLockException", "WritableStreamDefaultControllerGetDesiredSize", "WritableStreamDefaultWriterGetDesiredSize", "_readyPromise", "WritableStreamDefaultWriterAbort", "WritableStreamDefaultWriterClose", "WritableStreamDefaultWriterRelease", "WritableStreamDefaultWriterWrite", "WritableStreamDefaultWriterEnsureClosedPromiseRejected", "_closedPromiseState", "defaultWriterClosedPromiseResetToRejected", "_readyPromiseState", "defaultWriterReadyPromiseReject", "defaultWriterReadyPromiseResetToRejected", "releasedError", "chunkSize", "_strategySizeAlgorithm", "chunkSizeE", "WritableStreamDefaultControllerErrorIfNeeded", "WritableStreamDefaultControllerGetChunkSize", "WritableStreamAddWriteRequest", "enqueueE", "_controlledWritableStream", "WritableStreamDefaultControllerGetBackpressure", "WritableStreamDefaultControllerWrite", "abortReason", "IsWritableStreamDefaultController", "defaultControllerBrandCheckException", "signal", "WritableStreamDefaultControllerError", "_abortAlgorithm", "WritableStreamDefaultControllerClearAlgorithms", "createAbortController", "_writeAlgorithm", "_closeAlgorithm", "WritableStreamMarkCloseRequestInFlight", "sinkClosePromise", "WritableStreamFinishInFlightClose", "WritableStreamFinishInFlightCloseWithError", "WritableStreamDefaultControllerProcessClose", "WritableStreamMarkFirstWriteRequestInFlight", "sinkWritePromise", "WritableStreamFinishInFlightWrite", "WritableStreamFinishInFlightWriteWithError", "WritableStreamDefaultControllerProcessWrite", "_readyPromise_resolve", "_readyPromise_reject", "globals", "globalThis", "self", "global", "DOMException", "isDOMExceptionConstructor", "getFromGlobal", "message", "Error", "captureStackTrace", "writable", "createPolyfill", "ReadableStreamPipeTo", "source", "preventClose", "preventAbort", "shuttingDown", "currentWrite", "actions", "shutdownWithAction", "all", "map", "action", "aborted", "addEventListener", "isOrBecomesErrored", "shutdown", "WritableStreamDefaultWriterCloseWithErrorPropagation", "destClosed", "waitForWritesToFinish", "oldCurrentWrite", "originalIsError", "originalError", "doTheRest", "finalize", "newError", "isError", "removeEventListener", "resolveLoop", "rejectLoop", "resolveRead", "rejectRead", "ReadableStreamDefaultController", "IsReadableStreamDefaultController", "ReadableStreamDefaultControllerGetDesiredSize", "ReadableStreamDefaultControllerCanCloseOrEnqueue", "ReadableStreamDefaultControllerClose", "ReadableStreamDefaultControllerEnqueue", "ReadableStreamDefaultControllerError", "ReadableStreamDefaultControllerClearAlgorithms", "_controlledReadableStream", "ReadableStreamDefaultControllerCallPullIfNeeded", "ReadableStreamDefaultControllerShouldCallPull", "SetUpReadableStreamDefaultController", "ReadableStreamTee", "cloneForBranch2", "reason1", "reason2", "branch1", "branch2", "resolveCancelPromise", "reading", "readAgainForBranch1", "readAgainForBranch2", "canceled1", "canceled2", "cancelPromise", "forwardReaderError", "thisReader", "pullWithDefaultReader", "chunk1", "chunk2", "pull1Algorithm", "pull2Algorithm", "pullWithBYOBReader", "forBranch2", "byobBranch", "otherBranch", "byobCanceled", "otherCanceled", "cancel1Algorithm", "compositeReason", "cancelResult", "cancel2Algorithm", "CreateReadableByteStream", "ReadableByteStreamTee", "readAgain", "CreateReadableStream", "ReadableStreamDefaultTee", "ReadableStreamFrom", "<PERSON><PERSON><PERSON><PERSON>", "readPromise", "readResult", "ReadableStreamFromDefaultReader", "asyncIterable", "iteratorRecord", "nextResult", "IteratorNext", "iterResult", "Boolean", "IteratorComplete", "IteratorValue", "return<PERSON><PERSON><PERSON>", "returnResult", "ReadableStreamFromIterable", "convertUnderlyingSourceCancelCallback", "convertUnderlyingSourcePullCallback", "convertUnderlyingSourceStartCallback", "convertReadableStreamType", "convertPipeOptions", "isAbortSignal", "assertAbortSignal", "ReadableStream", "rawUnderlyingSource", "underlyingSource", "pull", "convertUnderlyingDefaultOrByteSource", "InitializeReadableStream", "underlyingByteSource", "SetUpReadableByteStreamControllerFromUnderlyingSource", "SetUpReadableStreamDefaultControllerFromUnderlyingSource", "convertReaderOptions", "pipeThrough", "rawTransform", "transform", "readable", "convertReadableWritablePair", "pipeTo", "destination", "tee", "values", "impl", "AcquireReadableStreamAsyncIterator", "convertIteratorOptions", "from", "convertQueuingStrategyInit", "byteLengthSizeFunction", "ByteLengthQueuingStrategy", "_byteLengthQueuingStrategyHighWaterMark", "IsByteLengthQueuingStrategy", "byteLengthBrandCheckException", "countSizeFunction", "CountQueuingStrategy", "_countQueuingStrategyHighWaterMark", "IsCountQueuingStrategy", "countBrandCheckException", "convertTransformerFlushCallback", "convertTransformerStartCallback", "convertTransformerTransformCallback", "convertTransformerCancelCallback", "TransformStream", "rawTransformer", "rawWritableStrategy", "rawReadableStrategy", "writableStrategy", "readableStrategy", "transformer", "flush", "readableType", "writableType", "convertTransformer", "readableHighWaterMark", "readableSizeAlgorithm", "writableHighWaterMark", "writableSizeAlgorithm", "startPromise_resolve", "startPromise", "_transformStreamController", "_backpressureChangePromise", "_writable", "TransformStreamDefaultControllerPerformTransform", "TransformStreamDefaultSinkWriteAlgorithm", "_finishPromise", "_readable", "_finishPromise_resolve", "_finishPromise_reject", "TransformStreamDefaultControllerClearAlgorithms", "defaultControllerFinishPromiseReject", "defaultControllerFinishPromiseResolve", "TransformStreamDefaultSinkAbortAlgorithm", "flushPromise", "_flushAlgorithm", "TransformStreamDefaultSinkCloseAlgorithm", "TransformStreamSetBackpressure", "TransformStreamDefaultSourcePullAlgorithm", "TransformStreamUnblockWrite", "TransformStreamDefaultSourceCancelAlgorithm", "CreateWritableStream", "_backpressureChangePromise_resolve", "InitializeTransformStream", "TransformStreamDefaultController", "transformAlgorithm", "flushAlgorithm", "TransformStreamDefaultControllerEnqueue", "transformResultE", "_controlledTransformStream", "_transformAlgorithm", "SetUpTransformStreamDefaultController", "SetUpTransformStreamDefaultControllerFromTransformer", "IsTransformStream", "TransformStreamError", "TransformStreamErrorWritableAndUnblockWrite", "IsTransformStreamDefaultController", "terminate", "TransformStreamDefaultControllerTerminate", "readableController", "ReadableStreamDefaultControllerHasBackpressure", "exports"], "mappings": ";;;;;;;mQAAgBA,IAEhB,CCCM,SAAUC,EAAaC,GAC3B,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,CACzD,CAEO,MAAMC,EAUPH,EAEU,SAAAI,EAAgBC,EAAcC,GAC5C,IACEC,OAAOC,eAAeH,EAAI,OAAQ,CAChCI,MAAOH,EACPI,cAAc,GAEjB,CAAC,MAAAC,GAGD,CACH,CC1BA,MAAMC,EAAkBC,QAClBC,EAAsBD,QAAQE,UAAUC,KACxCC,EAAwBJ,QAAQK,OAAOC,KAAKP,GAG5C,SAAUQ,EAAcC,GAI5B,OAAO,IAAIT,EAAgBS,EAC7B,CAGM,SAAUC,EAAuBb,GACrC,OAAOW,GAAWG,GAAWA,EAAQd,IACvC,CAGM,SAAUe,EAA+BC,GAC7C,OAAOR,EAAsBQ,EAC/B,UAEgBC,EACdC,EACAC,EACAC,GAGA,OAAOf,EAAoBgB,KAAKH,EAASC,EAAaC,EACxD,UAKgBE,EACdJ,EACAC,EACAC,GACAH,EACEA,EAAmBC,EAASC,EAAaC,QACzCG,EACA7B,EAEJ,CAEgB,SAAA8B,EAAmBN,EAAqBC,GACtDG,EAAYJ,EAASC,EACvB,CAEgB,SAAAM,EAAcP,EAA2BE,GACvDE,EAAYJ,OAASK,EAAWH,EAClC,UAEgBM,EACdR,EACAS,EACAC,GACA,OAAOX,EAAmBC,EAASS,EAAoBC,EACzD,CAEM,SAAUC,EAA0BX,GACxCD,EAAmBC,OAASK,EAAW7B,EACzC,CAEA,IAAIoC,EAAkDC,IACpD,GAA8B,mBAAnBC,eACTF,EAAkBE,mBACb,CACL,MAAMC,EAAkBpB,OAAoBU,GAC5CO,EAAkBI,GAAMjB,EAAmBgB,EAAiBC,EAC7D,CACD,OAAOJ,EAAgBC,EAAS,WAKlBI,EAAmCC,EAAiCC,EAAMC,GACxF,GAAiB,mBAANF,EACT,MAAM,IAAIG,UAAU,8BAEtB,OAAOC,SAASlC,UAAUmC,MAAMpB,KAAKe,EAAGC,EAAGC,EAC7C,UAEgBI,EAAmCN,EACAC,EACAC,GAIjD,IACE,OAAOzB,EAAoBsB,EAAYC,EAAGC,EAAGC,GAC9C,CAAC,MAAOtC,GACP,OAAOe,EAAoBf,EAC5B,CACH,OC/Ea2C,EAMX,WAAAC,GAHQC,KAAOC,QAAG,EACVD,KAAKE,MAAG,EAIdF,KAAKG,OAAS,CACZC,UAAW,GACXC,WAAO3B,GAETsB,KAAKM,MAAQN,KAAKG,OAIlBH,KAAKC,QAAU,EAEfD,KAAKE,MAAQ,CACd,CAED,UAAIK,GACF,OAAOP,KAAKE,KACb,CAMD,IAAAM,CAAKC,GACH,MAAMC,EAAUV,KAAKM,MACrB,IAAIK,EAAUD,EAEmBE,QAA7BF,EAAQN,UAAUG,SACpBI,EAAU,CACRP,UAAW,GACXC,WAAO3B,IAMXgC,EAAQN,UAAUI,KAAKC,GACnBE,IAAYD,IACdV,KAAKM,MAAQK,EACbD,EAAQL,MAAQM,KAEhBX,KAAKE,KACR,CAID,KAAAW,GAGE,MAAMC,EAAWd,KAAKG,OACtB,IAAIY,EAAWD,EACf,MAAME,EAAYhB,KAAKC,QACvB,IAAIgB,EAAYD,EAAY,EAE5B,MAAME,EAAWJ,EAASV,UACpBK,EAAUS,EAASF,GAmBzB,OA7FyB,QA4ErBC,IAGFF,EAAWD,EAAST,MACpBY,EAAY,KAIZjB,KAAKE,MACPF,KAAKC,QAAUgB,EACXH,IAAaC,IACff,KAAKG,OAASY,GAIhBG,EAASF,QAAatC,EAEf+B,CACR,CAUD,OAAAU,CAAQjC,GACN,IAAIkC,EAAIpB,KAAKC,QACToB,EAAOrB,KAAKG,OACZe,EAAWG,EAAKjB,UACpB,OAAOgB,IAAMF,EAASX,aAAyB7B,IAAf2C,EAAKhB,OAC/Be,IAAMF,EAASX,SAGjBc,EAAOA,EAAKhB,MACZa,EAAWG,EAAKjB,UAChBgB,EAAI,EACoB,IAApBF,EAASX,UAIfrB,EAASgC,EAASE,MAChBA,CAEL,CAID,IAAAE,GAGE,MAAMC,EAAQvB,KAAKG,OACbqB,EAASxB,KAAKC,QACpB,OAAOsB,EAAMnB,UAAUoB,EACxB,ECzII,MAAMC,EAAaC,OAAO,kBACpBC,EAAaD,OAAO,kBACpBE,EAAcF,OAAO,mBACrBG,EAAYH,OAAO,iBACnBI,EAAeJ,OAAO,oBCCnB,SAAAK,EAAyCC,EAAiCC,GACxFD,EAAOE,qBAAuBD,EAC9BA,EAAOE,QAAUH,EAEK,aAAlBC,EAAOG,OACTC,EAAqCL,GACV,WAAlBC,EAAOG,OA2Dd,SAAyDJ,GAC7DK,EAAqCL,GACrCM,EAAkCN,EACpC,CA7DIO,CAA+CP,GAI/CQ,EAA+CR,EAAQC,EAAOQ,aAElE,CAKgB,SAAAC,EAAkCV,EAAmC7D,GAGnF,OAAOwE,GAFQX,EAAOE,qBAEc/D,EACtC,CAEM,SAAUyE,EAAmCZ,GACjD,MAAMC,EAASD,EAAOE,qBAIA,aAAlBD,EAAOG,OACTS,EACEb,EACA,IAAItC,UAAU,qFAiDJ,SAA0CsC,EAAmC7D,GAI3FqE,EAA+CR,EAAQ7D,EACzD,CApDI2E,CACEd,EACA,IAAItC,UAAU,qFAGlBuC,EAAOc,0BAA0BjB,KAEjCG,EAAOE,aAAUzD,EACjBsD,EAAOE,0BAAuBxD,CAChC,CAIM,SAAUsE,EAAoBhG,GAClC,OAAO,IAAI0C,UAAU,UAAY1C,EAAO,oCAC1C,CAIM,SAAUqF,EAAqCL,GACnDA,EAAOiB,eAAiBnF,GAAW,CAACG,EAASL,KAC3CoE,EAAOkB,uBAAyBjF,EAChC+D,EAAOmB,sBAAwBvF,CAAM,GAEzC,CAEgB,SAAA4E,EAA+CR,EAAmC7D,GAChGkE,EAAqCL,GACrCa,EAAiCb,EAAQ7D,EAC3C,CAOgB,SAAA0E,EAAiCb,EAAmC7D,QAC7CO,IAAjCsD,EAAOmB,wBAIXnE,EAA0BgD,EAAOiB,gBACjCjB,EAAOmB,sBAAsBhF,GAC7B6D,EAAOkB,4BAAyBxE,EAChCsD,EAAOmB,2BAAwBzE,EACjC,CASM,SAAU4D,EAAkCN,QACVtD,IAAlCsD,EAAOkB,yBAIXlB,EAAOkB,4BAAuBxE,GAC9BsD,EAAOkB,4BAAyBxE,EAChCsD,EAAOmB,2BAAwBzE,EACjC,CClGA,MAAM0E,EAAyCC,OAAOC,UAAY,SAAU1G,GAC1E,MAAoB,iBAANA,GAAkB0G,SAAS1G,EAC3C,ECFM2G,EAA+BC,KAAKC,OAAS,SAAUC,GAC3D,OAAOA,EAAI,EAAIF,KAAKG,KAAKD,GAAKF,KAAKI,MAAMF,EAC3C,ECGgB,SAAAG,EAAiBC,EACAC,GAC/B,QAAYrF,IAARoF,IALgB,iBADOlH,EAMYkH,IALM,mBAANlH,GAMrC,MAAM,IAAI8C,UAAU,GAAGqE,uBAPrB,IAAuBnH,CAS7B,CAKgB,SAAAoH,EAAepH,EAAYmH,GACzC,GAAiB,mBAANnH,EACT,MAAM,IAAI8C,UAAU,GAAGqE,uBAE3B,CAOgB,SAAAE,EAAarH,EACAmH,GAC3B,IANI,SAAmBnH,GACvB,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,CACzD,CAIOsH,CAAStH,GACZ,MAAM,IAAI8C,UAAU,GAAGqE,sBAE3B,UAEgBI,EAA0BvH,EACAwH,EACAL,GACxC,QAAUrF,IAAN9B,EACF,MAAM,IAAI8C,UAAU,aAAa0E,qBAA4BL,MAEjE,UAEgBM,EAAuBzH,EACA0H,EACAP,GACrC,QAAUrF,IAAN9B,EACF,MAAM,IAAI8C,UAAU,GAAG4E,qBAAyBP,MAEpD,CAGM,SAAUQ,EAA0BpH,GACxC,OAAOkG,OAAOlG,EAChB,CAEA,SAASqH,EAAmB5H,GAC1B,OAAa,IAANA,EAAU,EAAIA,CACvB,CAOgB,SAAA6H,EAAwCtH,EAAgB4G,GACtE,MACMW,EAAarB,OAAOsB,iBAE1B,IAAI/H,EAAIyG,OAAOlG,GAGf,GAFAP,EAAI4H,EAAmB5H,IAElBwG,EAAexG,GAClB,MAAM,IAAI8C,UAAU,GAAGqE,4BAKzB,GAFAnH,EAhBF,SAAqBA,GACnB,OAAO4H,EAAmBjB,EAAU3G,GACtC,CAcMgI,CAAYhI,GAEZA,EAZe,GAYGA,EAAI8H,EACxB,MAAM,IAAIhF,UAAU,GAAGqE,2CAA6DW,gBAGtF,OAAKtB,EAAexG,IAAY,IAANA,EASnBA,EARE,CASX,CC3FgB,SAAAiI,EAAqBjI,EAAYmH,GAC/C,IAAKe,GAAiBlI,GACpB,MAAM,IAAI8C,UAAU,GAAGqE,6BAE3B,CCwBM,SAAUgB,EAAsC9C,GACpD,OAAO,IAAI+C,4BAA4B/C,EACzC,CAIgB,SAAAgD,EAAgChD,EACAiD,GAI7CjD,EAAOE,QAA4CgD,cAAc3E,KAAK0E,EACzE,UAEgBE,EAAoCnD,EAA2BoD,EAAsBC,GACnG,MAIMJ,EAJSjD,EAAOE,QAIKgD,cAActE,QACrCyE,EACFJ,EAAYK,cAEZL,EAAYM,YAAYH,EAE5B,CAEM,SAAUI,EAAoCxD,GAClD,OAAQA,EAAOE,QAA2CgD,cAAc5E,MAC1E,CAEM,SAAUmF,EAA+BzD,GAC7C,MAAMD,EAASC,EAAOE,QAEtB,YAAezD,IAAXsD,KAIC2D,EAA8B3D,EAKrC,OAiBagD,4BAYX,WAAAjF,CAAYkC,GAIV,GAHAkC,EAAuBlC,EAAQ,EAAG,+BAClC4C,EAAqB5C,EAAQ,mBAEzB2D,GAAuB3D,GACzB,MAAM,IAAIvC,UAAU,+EAGtBqC,EAAsC/B,KAAMiC,GAE5CjC,KAAKmF,cAAgB,IAAIrF,CAC1B,CAMD,UAAI+F,GACF,OAAKF,EAA8B3F,MAI5BA,KAAKiD,eAHH/E,EAAoB4H,EAAiC,UAI/D,CAKD,MAAAC,CAAO5H,OAAcO,GACnB,OAAKiH,EAA8B3F,WAIDtB,IAA9BsB,KAAKkC,qBACAhE,EAAoB8E,EAAoB,WAG1CN,EAAkC1C,KAAM7B,GAPtCD,EAAoB4H,EAAiC,UAQ/D,CAOD,IAAAE,GACE,IAAKL,EAA8B3F,MACjC,OAAO9B,EAAoB4H,EAAiC,SAG9D,QAAkCpH,IAA9BsB,KAAKkC,qBACP,OAAOhE,EAAoB8E,EAAoB,cAGjD,IAAIiD,EACAC,EACJ,MAAM7H,EAAUP,GAA+C,CAACG,EAASL,KACvEqI,EAAiBhI,EACjBiI,EAAgBtI,CAAM,IAQxB,OADAuI,EAAgCnG,KALI,CAClCwF,YAAaH,GAASY,EAAe,CAAE9I,MAAOkI,EAAOC,MAAM,IAC3DC,YAAa,IAAMU,EAAe,CAAE9I,WAAOuB,EAAW4G,MAAM,IAC5Dc,YAAaC,GAAKH,EAAcG,KAG3BhI,CACR,CAWD,WAAAiI,GACE,IAAKX,EAA8B3F,MACjC,MAAM8F,EAAiC,oBAGPpH,IAA9BsB,KAAKkC,sBAwDP,SAA6CF,GACjDY,EAAmCZ,GACnC,MAAMqE,EAAI,IAAI3G,UAAU,uBACxB6G,EAA6CvE,EAAQqE,EACvD,CAxDIG,CAAmCxG,KACpC,EAqBG,SAAU2F,EAAuC/I,GACrD,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,kBAItCA,aAAaoI,4BACtB,CAEgB,SAAAmB,EAAmCnE,EACAkD,GACjD,MAAMjD,EAASD,EAAOE,qBAItBD,EAAOyE,YAAa,EAEE,WAAlBzE,EAAOG,OACT8C,EAAYK,cACe,YAAlBtD,EAAOG,OAChB8C,EAAYkB,YAAYnE,EAAOQ,cAG/BR,EAAOc,0BAA0BlB,GAAWqD,EAEhD,CAQgB,SAAAqB,EAA6CvE,EAAqCqE,GAChG,MAAMM,EAAe3E,EAAOmD,cAC5BnD,EAAOmD,cAAgB,IAAIrF,EAC3B6G,EAAaxF,SAAQ+D,IACnBA,EAAYkB,YAAYC,EAAE,GAE9B,CAIA,SAASP,EAAiC9I,GACxC,OAAO,IAAI0C,UACT,yCAAyC1C,sDAC7C,CAnEAC,OAAO2J,iBAAiB5B,4BAA4BvH,UAAW,CAC7DsI,OAAQ,CAAEc,YAAY,GACtBb,KAAM,CAAEa,YAAY,GACpBP,YAAa,CAAEO,YAAY,GAC3BhB,OAAQ,CAAEgB,YAAY,KAExB/J,EAAgBkI,4BAA4BvH,UAAUsI,OAAQ,UAC9DjJ,EAAgBkI,4BAA4BvH,UAAUuI,KAAM,QAC5DlJ,EAAgBkI,4BAA4BvH,UAAU6I,YAAa,eACjC,iBAAvB5E,OAAOoF,aAChB7J,OAAOC,eAAe8H,4BAA4BvH,UAAWiE,OAAOoF,YAAa,CAC/E3J,MAAO,8BACPC,cAAc,IC1MX,MAAM2J,GACX9J,OAAO+J,eAAe/J,OAAO+J,gBAAeC,kBAAe,IAAoCxJ,iBC6BpFyJ,GAMX,WAAAnH,CAAYiC,EAAwCmF,GAH5CnH,KAAeoH,qBAA4D1I,EAC3EsB,KAAWqH,aAAG,EAGpBrH,KAAKmC,QAAUH,EACfhC,KAAKsH,eAAiBH,CACvB,CAED,IAAAI,GACE,MAAMC,EAAY,IAAMxH,KAAKyH,aAI7B,OAHAzH,KAAKoH,gBAAkBpH,KAAKoH,gBAC1BvI,EAAqBmB,KAAKoH,gBAAiBI,EAAWA,GACtDA,IACKxH,KAAKoH,eACb,CAED,OAAOjK,GACL,MAAMuK,EAAc,IAAM1H,KAAK2H,aAAaxK,GAC5C,OAAO6C,KAAKoH,gBACVvI,EAAqBmB,KAAKoH,gBAAiBM,EAAaA,GACxDA,GACH,CAEO,UAAAD,GACN,GAAIzH,KAAKqH,YACP,OAAO9J,QAAQU,QAAQ,CAAEd,WAAOuB,EAAW4G,MAAM,IAGnD,MAAMtD,EAAShC,KAAKmC,QAGpB,IAAI8D,EACAC,EACJ,MAAM7H,EAAUP,GAA+C,CAACG,EAASL,KACvEqI,EAAiBhI,EACjBiI,EAAgBtI,CAAM,IAuBxB,OADAuI,EAAgCnE,EApBI,CAClCwD,YAAaH,IACXrF,KAAKoH,qBAAkB1I,EAGvBS,GAAe,IAAM8G,EAAe,CAAE9I,MAAOkI,EAAOC,MAAM,KAAS,EAErEC,YAAa,KACXvF,KAAKoH,qBAAkB1I,EACvBsB,KAAKqH,aAAc,EACnBzE,EAAmCZ,GACnCiE,EAAe,CAAE9I,WAAOuB,EAAW4G,MAAM,GAAO,EAElDc,YAAajI,IACX6B,KAAKoH,qBAAkB1I,EACvBsB,KAAKqH,aAAc,EACnBzE,EAAmCZ,GACnCkE,EAAc/H,EAAO,IAIlBE,CACR,CAEO,YAAAsJ,CAAaxK,GACnB,GAAI6C,KAAKqH,YACP,OAAO9J,QAAQU,QAAQ,CAAEd,QAAOmI,MAAM,IAExCtF,KAAKqH,aAAc,EAEnB,MAAMrF,EAAShC,KAAKmC,QAIpB,IAAKnC,KAAKsH,eAAgB,CACxB,MAAMM,EAASlF,EAAkCV,EAAQ7E,GAEzD,OADAyF,EAAmCZ,GAC5BnD,EAAqB+I,GAAQ,KAAO,CAAEzK,QAAOmI,MAAM,KAC3D,CAGD,OADA1C,EAAmCZ,GAC5BhE,EAAoB,CAAEb,QAAOmI,MAAM,GAC3C,EAYH,MAAMuC,GAAiF,CACrF,IAAAN,GACE,OAAKO,GAA8B9H,MAG5BA,KAAK+H,mBAAmBR,OAFtBrJ,EAAoB8J,GAAuC,QAGrE,EAED,OAAuD7K,GACrD,OAAK2K,GAA8B9H,MAG5BA,KAAK+H,mBAAmBE,OAAO9K,GAF7Be,EAAoB8J,GAAuC,UAGrE,GAeH,SAASF,GAAuClL,GAC9C,IAAKD,EAAaC,GAChB,OAAO,EAGT,IAAKK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,sBAC3C,OAAO,EAGT,IAEE,OAAQA,EAA+CmL,8BACrDb,EACH,CAAC,MAAA7J,GACA,OAAO,CACR,CACH,CAIA,SAAS2K,GAAuChL,GAC9C,OAAO,IAAI0C,UAAU,+BAA+B1C,qDACtD,CAnCAC,OAAOiL,eAAeL,GAAsCd,IC3I5D,MAAMoB,GAAmC9E,OAAO+E,OAAS,SAAUxL,GAEjE,OAAOA,GAAMA,CACf,eCQM,SAAUyL,GAAqCnH,GAGnD,OAAOA,EAASoH,OAClB,CAEM,SAAUC,GAAmBC,EACAC,EACAC,EACAC,EACAC,GACjC,IAAIC,WAAWL,GAAMM,IAAI,IAAID,WAAWH,EAAKC,EAAWC,GAAIH,EAC9D,CAEO,IAAIM,GAAuBC,IAE9BD,GADwB,mBAAfC,EAAEC,SACWC,GAAUA,EAAOD,WACH,mBAApBE,gBACMD,GAAUC,gBAAgBD,EAAQ,CAAED,SAAU,CAACC,KAG/CA,GAAUA,EAE3BH,GAAoBC,IAOlBI,GAAoBJ,IAE3BI,GADwB,kBAAfJ,EAAEK,SACQH,GAAUA,EAAOG,SAGjBH,GAAgC,IAAtBA,EAAOI,WAE/BF,GAAiBJ,aAGVO,GAAiBL,EAAqBM,EAAeC,GAGnE,GAAIP,EAAOZ,MACT,OAAOY,EAAOZ,MAAMkB,EAAOC,GAE7B,MAAMlJ,EAASkJ,EAAMD,EACflB,EAAQ,IAAIoB,YAAYnJ,GAE9B,OADAgI,GAAmBD,EAAO,EAAGY,EAAQM,EAAOjJ,GACrC+H,CACT,CAMgB,SAAAqB,GAAsCC,EAAaC,GACjE,MAAMC,EAAOF,EAASC,GACtB,GAAIC,QAAJ,CAGA,GAAoB,mBAATA,EACT,MAAM,IAAIpK,UAAU,GAAGqK,OAAOF,wBAEhC,OAAOC,CAJN,CAKH,CAkCO,MAAME,GAEyB,QADpCC,WAAA5M,GAAAqE,OAAOwI,+BACG,QAAVC,GAAAzI,OAAO0I,WAAG,IAAAD,QAAA,EAAAA,GAAA3L,KAAAkD,OAAG,+BAAuB,IAAAuI,GAAAA,GACpC,kBAeF,SAASI,GACPvG,EACAwG,EAAO,OACPC,GAGA,QAAe7L,IAAX6L,EACF,GAAa,UAATD,GAEF,QAAe5L,KADf6L,EAASZ,GAAU7F,EAAyBkG,KAClB,CAGxB,OAhDF,SAAyCQ,GAK7C,MAAMC,EAAe,CACnB,CAAC/I,OAAOgJ,UAAW,IAAMF,EAAmBE,UAGxCR,EAAiBjD,kBACrB,aAAcwD,CACf,CAFkB,GAKnB,MAAO,CAAEC,SAAUR,EAAeS,WADfT,EAAc3C,KACajC,MAAM,EACtD,CAiCesF,CADoBP,GAAYvG,EAAoB,OADxC6F,GAAU7F,EAAoBpC,OAAOgJ,WAGzD,OAEDH,EAASZ,GAAU7F,EAAoBpC,OAAOgJ,UAGlD,QAAehM,IAAX6L,EACF,MAAM,IAAI7K,UAAU,8BAEtB,MAAMgL,EAAWpL,EAAYiL,EAAQzG,EAAK,IAC1C,IAAKnH,EAAa+N,GAChB,MAAM,IAAIhL,UAAU,6CAGtB,MAAO,CAAEgL,WAAUC,WADAD,EAASnD,KACGjC,MAAM,EACvC,CC1IM,SAAUuF,GAAkB7B,GAChC,MAAME,EAASK,GAAiBP,EAAEE,OAAQF,EAAE8B,WAAY9B,EAAE8B,WAAa9B,EAAEM,YACzE,OAAO,IAAIT,WAAWK,EACxB,CCTM,SAAU6B,GAAgBC,GAI9B,MAAMC,EAAOD,EAAUE,OAAOrK,QAM9B,OALAmK,EAAUG,iBAAmBF,EAAKG,KAC9BJ,EAAUG,gBAAkB,IAC9BH,EAAUG,gBAAkB,GAGvBF,EAAK9N,KACd,UAEgBkO,GAAwBL,EAAyC7N,EAAUiO,GAGzF,GDzBiB,iBADiB1H,EC0BT0H,IDrBrBjD,GAAYzE,IAIZA,EAAI,GCiB0B0H,IAASE,IACzC,MAAM,IAAIC,WAAW,wDD3BnB,IAA8B7H,EC8BlCsH,EAAUE,OAAO1K,KAAK,CAAErD,QAAOiO,SAC/BJ,EAAUG,iBAAmBC,CAC/B,CAUM,SAAUI,GAAcR,GAG5BA,EAAUE,OAAS,IAAIpL,EACvBkL,EAAUG,gBAAkB,CAC9B,CCxBA,SAASM,GAAsBC,GAC7B,OAAOA,IAASC,QAClB,OCoBaC,0BAMX,WAAA7L,GACE,MAAM,IAAIL,UAAU,sBACrB,CAKD,QAAImM,GACF,IAAKC,GAA4B9L,MAC/B,MAAM+L,GAA+B,QAGvC,OAAO/L,KAAKgM,KACb,CAUD,OAAAC,CAAQC,GACN,IAAKJ,GAA4B9L,MAC/B,MAAM+L,GAA+B,WAKvC,GAHA5H,EAAuB+H,EAAc,EAAG,WACxCA,EAAezH,EAAwCyH,EAAc,wBAEhBxN,IAAjDsB,KAAKmM,wCACP,MAAM,IAAIzM,UAAU,0CAGtB,GAAI0J,GAAiBpJ,KAAKgM,MAAO9C,QAC/B,MAAM,IAAIxJ,UAAU,mFAMtB0M,GAAoCpM,KAAKmM,wCAAyCD,EACnF,CAUD,kBAAAG,CAAmBR,GACjB,IAAKC,GAA4B9L,MAC/B,MAAM+L,GAA+B,sBAIvC,GAFA5H,EAAuB0H,EAAM,EAAG,uBAE3BnC,YAAY4C,OAAOT,GACtB,MAAM,IAAInM,UAAU,gDAGtB,QAAqDhB,IAAjDsB,KAAKmM,wCACP,MAAM,IAAIzM,UAAU,0CAGtB,GAAI0J,GAAiByC,EAAK3C,QACxB,MAAM,IAAIxJ,UAAU,iFAGtB6M,GAA+CvM,KAAKmM,wCAAyCN,EAC9F,EAGH5O,OAAO2J,iBAAiBgF,0BAA0BnO,UAAW,CAC3DwO,QAAS,CAAEpF,YAAY,GACvBwF,mBAAoB,CAAExF,YAAY,GAClCgF,KAAM,CAAEhF,YAAY,KAEtB/J,EAAgB8O,0BAA0BnO,UAAUwO,QAAS,WAC7DnP,EAAgB8O,0BAA0BnO,UAAU4O,mBAAoB,sBACtC,iBAAvB3K,OAAOoF,aAChB7J,OAAOC,eAAe0O,0BAA0BnO,UAAWiE,OAAOoF,YAAa,CAC7E3J,MAAO,4BACPC,cAAc,UA2CLoP,6BA4BX,WAAAzM,GACE,MAAM,IAAIL,UAAU,sBACrB,CAKD,eAAI+M,GACF,IAAKC,GAA+B1M,MAClC,MAAM2M,GAAwC,eAGhD,OAAOC,GAA2C5M,KACnD,CAMD,eAAI6M,GACF,IAAKH,GAA+B1M,MAClC,MAAM2M,GAAwC,eAGhD,OAAOG,GAA2C9M,KACnD,CAMD,KAAA+M,GACE,IAAKL,GAA+B1M,MAClC,MAAM2M,GAAwC,SAGhD,GAAI3M,KAAKgN,gBACP,MAAM,IAAItN,UAAU,8DAGtB,MAAMuN,EAAQjN,KAAKkN,8BAA8B9K,OACjD,GAAc,aAAV6K,EACF,MAAM,IAAIvN,UAAU,kBAAkBuN,8DAGxCE,GAAkCnN,KACnC,CAOD,OAAAoN,CAAQ/H,GACN,IAAKqH,GAA+B1M,MAClC,MAAM2M,GAAwC,WAIhD,GADAxI,EAAuBkB,EAAO,EAAG,YAC5BqE,YAAY4C,OAAOjH,GACtB,MAAM,IAAI3F,UAAU,sCAEtB,GAAyB,IAArB2F,EAAMiE,WACR,MAAM,IAAI5J,UAAU,uCAEtB,GAAgC,IAA5B2F,EAAM6D,OAAOI,WACf,MAAM,IAAI5J,UAAU,gDAGtB,GAAIM,KAAKgN,gBACP,MAAM,IAAItN,UAAU,gCAGtB,MAAMuN,EAAQjN,KAAKkN,8BAA8B9K,OACjD,GAAc,aAAV6K,EACF,MAAM,IAAIvN,UAAU,kBAAkBuN,mEAGxCI,GAAoCrN,KAAMqF,EAC3C,CAKD,KAAAiI,CAAMjH,OAAS3H,GACb,IAAKgO,GAA+B1M,MAClC,MAAM2M,GAAwC,SAGhDY,GAAkCvN,KAAMqG,EACzC,CAGD,CAACzE,GAAazD,GACZqP,GAAkDxN,MAElDwL,GAAWxL,MAEX,MAAM4H,EAAS5H,KAAKyN,iBAAiBtP,GAErC,OADAuP,GAA4C1N,MACrC4H,CACR,CAGD,CAAC/F,GAAWqD,GACV,MAAMjD,EAASjC,KAAKkN,8BAGpB,GAAIlN,KAAKmL,gBAAkB,EAIzB,YADAwC,GAAqD3N,KAAMkF,GAI7D,MAAM0I,EAAwB5N,KAAK6N,uBACnC,QAA8BnP,IAA1BkP,EAAqC,CACvC,IAAI1E,EACJ,IACEA,EAAS,IAAIQ,YAAYkE,EAC1B,CAAC,MAAOE,GAEP,YADA5I,EAAYkB,YAAY0H,EAEzB,CAED,MAAMC,EAAgD,CACpD7E,SACA8E,iBAAkBJ,EAClB9C,WAAY,EACZxB,WAAYsE,EACZK,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,gBAAiBvF,WACjBwF,WAAY,WAGdrO,KAAKsO,kBAAkB9N,KAAKuN,EAC7B,CAED9I,EAA6BhD,EAAQiD,GACrCqJ,GAA6CvO,KAC9C,CAGD,CAAC8B,KACC,GAAI9B,KAAKsO,kBAAkB/N,OAAS,EAAG,CACrC,MAAMiO,EAAgBxO,KAAKsO,kBAAkBhN,OAC7CkN,EAAcH,WAAa,OAE3BrO,KAAKsO,kBAAoB,IAAIxO,EAC7BE,KAAKsO,kBAAkB9N,KAAKgO,EAC7B,CACF,EAsBG,SAAU9B,GAA+B9P,GAC7C,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,kCAItCA,aAAa4P,6BACtB,CAEA,SAASV,GAA4BlP,GACnC,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,4CAItCA,aAAagP,0BACtB,CAEA,SAAS2C,GAA6CE,GACpD,MAAMC,EAiYR,SAAoDD,GAClD,MAAMxM,EAASwM,EAAWvB,8BAE1B,GAAsB,aAAlBjL,EAAOG,OACT,OAAO,EAGT,GAAIqM,EAAWzB,gBACb,OAAO,EAGT,IAAKyB,EAAWE,SACd,OAAO,EAGT,GAAIjJ,EAA+BzD,IAAWwD,EAAiCxD,GAAU,EACvF,OAAO,EAGT,GAAI2M,GAA4B3M,IAAW4M,GAAqC5M,GAAU,EACxF,OAAO,EAGT,MAAM4K,EAAcC,GAA2C2B,GAE/D,GAAI5B,EAAe,EACjB,OAAO,EAGT,OAAO,CACT,CA/ZqBiC,CAA2CL,GAC9D,IAAKC,EACH,OAGF,GAAID,EAAWM,SAEb,YADAN,EAAWO,YAAa,GAM1BP,EAAWM,UAAW,EAItBtQ,EADoBgQ,EAAWQ,kBAG7B,KACER,EAAWM,UAAW,EAElBN,EAAWO,aACbP,EAAWO,YAAa,EACxBT,GAA6CE,IAGxC,QAETpI,IACEkH,GAAkCkB,EAAYpI,GACvC,OAGb,CAEA,SAASmH,GAAkDiB,GACzDS,GAAkDT,GAClDA,EAAWH,kBAAoB,IAAIxO,CACrC,CAEA,SAASqP,GACPlN,EACA8L,GAKA,IAAIzI,GAAO,EACW,WAAlBrD,EAAOG,SAETkD,GAAO,GAGT,MAAM8J,EAAaC,GAAyDtB,GACtC,YAAlCA,EAAmBM,WACrBjJ,EAAiCnD,EAAQmN,EAAgD9J,YCxZxCrD,EACAoD,EACAC,GACnD,MAAMtD,EAASC,EAAOE,QAIhBmN,EAAkBtN,EAAOuN,kBAAkB1O,QAC7CyE,EACFgK,EAAgB/J,YAAYF,GAE5BiK,EAAgB9J,YAAYH,EAEhC,CD8YImK,CAAqCvN,EAAQmN,EAAY9J,EAE7D,CAEA,SAAS+J,GACPtB,GAEA,MAAME,EAAcF,EAAmBE,YACjCE,EAAcJ,EAAmBI,YAKvC,OAAO,IAAIJ,EAAmBK,gBAC5BL,EAAmB7E,OAAQ6E,EAAmBjD,WAAYmD,EAAcE,EAC5E,CAEA,SAASsB,GAAgDhB,EACAvF,EACA4B,EACAxB,GACvDmF,EAAWvD,OAAO1K,KAAK,CAAE0I,SAAQ4B,aAAYxB,eAC7CmF,EAAWtD,iBAAmB7B,CAChC,CAEA,SAASoG,GAAsDjB,EACAvF,EACA4B,EACAxB,GAC7D,IAAIqG,EACJ,IACEA,EAAcpG,GAAiBL,EAAQ4B,EAAYA,EAAaxB,EACjE,CAAC,MAAOsG,GAEP,MADArC,GAAkCkB,EAAYmB,GACxCA,CACP,CACDH,GAAgDhB,EAAYkB,EAAa,EAAGrG,EAC9E,CAEA,SAASuG,GAA2DpB,EACAqB,GAE9DA,EAAgB7B,YAAc,GAChCyB,GACEjB,EACAqB,EAAgB5G,OAChB4G,EAAgBhF,WAChBgF,EAAgB7B,aAGpB8B,GAAiDtB,EACnD,CAEA,SAASuB,GAA4DvB,EACAV,GACnE,MAAMkC,EAAiBzM,KAAK0M,IAAIzB,EAAWtD,gBACX4C,EAAmBzE,WAAayE,EAAmBE,aAC7EkC,EAAiBpC,EAAmBE,YAAcgC,EAExD,IAAIG,EAA4BH,EAC5BI,GAAQ,EAEZ,MACMC,EAAkBH,EADDA,EAAiBpC,EAAmBI,YAIvDmC,GAAmBvC,EAAmBG,cACxCkC,EAA4BE,EAAkBvC,EAAmBE,YACjEoC,GAAQ,GAGV,MAAME,EAAQ9B,EAAWvD,OAEzB,KAAOkF,EAA4B,GAAG,CACpC,MAAMI,EAAcD,EAAMjP,OAEpBmP,EAAcjN,KAAK0M,IAAIE,EAA2BI,EAAYlH,YAE9DoH,EAAY3C,EAAmBjD,WAAaiD,EAAmBE,YACrE1F,GAAmBwF,EAAmB7E,OAAQwH,EAAWF,EAAYtH,OAAQsH,EAAY1F,WAAY2F,GAEjGD,EAAYlH,aAAemH,EAC7BF,EAAM1P,SAEN2P,EAAY1F,YAAc2F,EAC1BD,EAAYlH,YAAcmH,GAE5BhC,EAAWtD,iBAAmBsF,EAE9BE,GAAuDlC,EAAYgC,EAAa1C,GAEhFqC,GAA6BK,CAC9B,CAQD,OAAOJ,CACT,CAEA,SAASM,GAAuDlC,EACArD,EACA2C,GAG9DA,EAAmBE,aAAe7C,CACpC,CAEA,SAASwF,GAA6CnC,GAGjB,IAA/BA,EAAWtD,iBAAyBsD,EAAWzB,iBACjDU,GAA4Ce,GAC5CoC,GAAoBpC,EAAWvB,gCAE/BqB,GAA6CE,EAEjD,CAEA,SAASS,GAAkDT,GACzB,OAA5BA,EAAWqC,eAIfrC,EAAWqC,aAAa3E,6CAA0CzN,EAClE+P,EAAWqC,aAAa9E,MAAQ,KAChCyC,EAAWqC,aAAe,KAC5B,CAEA,SAASC,GAAiEtC,GAGxE,KAAOA,EAAWH,kBAAkB/N,OAAS,GAAG,CAC9C,GAAmC,IAA/BkO,EAAWtD,gBACb,OAGF,MAAM4C,EAAqBU,EAAWH,kBAAkBhN,OAGpD0O,GAA4DvB,EAAYV,KAC1EgC,GAAiDtB,GAEjDU,GACEV,EAAWvB,8BACXa,GAGL,CACH,CAcM,SAAUiD,GACdvC,EACA5C,EACAqE,EACAZ,GAEA,MAAMrN,EAASwM,EAAWvB,8BAEpBxB,EAAOG,EAAK9L,YACZoO,EDhmBF,SAAgEzC,GACpE,OAAID,GAAsBC,GACjB,EAEDA,EAA0CuF,iBACpD,CC2lBsBC,CAA2BxF,IAEzCZ,WAAEA,EAAUxB,WAAEA,GAAeuC,EAE7BqC,EAAcgC,EAAM/B,EAI1B,IAAIjF,EACJ,IACEA,EAASH,GAAoB8C,EAAK3C,OACnC,CAAC,MAAO7C,GAEP,YADAiJ,EAAgBlJ,YAAYC,EAE7B,CAED,MAAM0H,EAAgD,CACpD7E,SACA8E,iBAAkB9E,EAAOI,WACzBwB,aACAxB,aACA2E,YAAa,EACbC,cACAC,cACAC,gBAAiB1C,EACjB2C,WAAY,QAGd,GAAII,EAAWH,kBAAkB/N,OAAS,EAQxC,OAPAkO,EAAWH,kBAAkB9N,KAAKuN,QAMlCoD,GAAiClP,EAAQqN,GAI3C,GAAsB,WAAlBrN,EAAOG,OAAX,CAMA,GAAIqM,EAAWtD,gBAAkB,EAAG,CAClC,GAAI6E,GAA4DvB,EAAYV,GAAqB,CAC/F,MAAMqB,EAAaC,GAAyDtB,GAK5E,OAHA6C,GAA6CnC,QAE7Ca,EAAgB9J,YAAY4J,EAE7B,CAED,GAAIX,EAAWzB,gBAAiB,CAC9B,MAAM3G,EAAI,IAAI3G,UAAU,2DAIxB,OAHA6N,GAAkCkB,EAAYpI,QAE9CiJ,EAAgBlJ,YAAYC,EAE7B,CACF,CAEDoI,EAAWH,kBAAkB9N,KAAKuN,GAElCoD,GAAoClP,EAAQqN,GAC5Cf,GAA6CE,EAxB5C,KAJD,CACE,MAAM2C,EAAY,IAAI1F,EAAKqC,EAAmB7E,OAAQ6E,EAAmBjD,WAAY,GACrFwE,EAAgB/J,YAAY6L,EAE7B,CAyBH,CAyDA,SAASC,GAA4C5C,EAA0CvC,GAC7F,MAAM4D,EAAkBrB,EAAWH,kBAAkBhN,OAGrD4N,GAAkDT,GAGpC,WADAA,EAAWvB,8BAA8B9K,OA7DzD,SAA0DqM,EACAqB,GAGrB,SAA/BA,EAAgBzB,YAClB0B,GAAiDtB,GAGnD,MAAMxM,EAASwM,EAAWvB,8BAC1B,GAAI0B,GAA4B3M,GAC9B,KAAO4M,GAAqC5M,GAAU,GAEpDkN,GAAqDlN,EAD1B8N,GAAiDtB,GAIlF,CAiDI6C,CAAiD7C,EAAYqB,GA/CjE,SAA4DrB,EACAvC,EACA6B,GAK1D,GAFA4C,GAAuDlC,EAAYvC,EAAc6B,GAE3C,SAAlCA,EAAmBM,WAGrB,OAFAwB,GAA2DpB,EAAYV,QACvEgD,GAAiEtC,GAInE,GAAIV,EAAmBE,YAAcF,EAAmBG,YAGtD,OAGF6B,GAAiDtB,GAEjD,MAAM8C,EAAgBxD,EAAmBE,YAAcF,EAAmBI,YAC1E,GAAIoD,EAAgB,EAAG,CACrB,MAAM9H,EAAMsE,EAAmBjD,WAAaiD,EAAmBE,YAC/DyB,GACEjB,EACAV,EAAmB7E,OACnBO,EAAM8H,EACNA,EAEH,CAEDxD,EAAmBE,aAAesD,EAClCpC,GAAqDV,EAAWvB,8BAA+Ba,GAE/FgD,GAAiEtC,EACnE,CAeI+C,CAAmD/C,EAAYvC,EAAc4D,GAG/EvB,GAA6CE,EAC/C,CAEA,SAASsB,GACPtB,GAIA,OADmBA,EAAWH,kBAAkBzN,OAElD,CAkCA,SAAS6M,GAA4Ce,GACnDA,EAAWQ,oBAAiBvQ,EAC5B+P,EAAWhB,sBAAmB/O,CAChC,CAIM,SAAUyO,GAAkCsB,GAChD,MAAMxM,EAASwM,EAAWvB,8BAE1B,IAAIuB,EAAWzB,iBAAqC,aAAlB/K,EAAOG,OAIzC,GAAIqM,EAAWtD,gBAAkB,EAC/BsD,EAAWzB,iBAAkB,MAD/B,CAMA,GAAIyB,EAAWH,kBAAkB/N,OAAS,EAAG,CAC3C,MAAMkR,EAAuBhD,EAAWH,kBAAkBhN,OAC1D,GAAImQ,EAAqBxD,YAAcwD,EAAqBtD,aAAgB,EAAG,CAC7E,MAAM9H,EAAI,IAAI3G,UAAU,2DAGxB,MAFA6N,GAAkCkB,EAAYpI,GAExCA,CACP,CACF,CAEDqH,GAA4Ce,GAC5CoC,GAAoB5O,EAbnB,CAcH,CAEgB,SAAAoL,GACdoB,EACApJ,GAEA,MAAMpD,EAASwM,EAAWvB,8BAE1B,GAAIuB,EAAWzB,iBAAqC,aAAlB/K,EAAOG,OACvC,OAGF,MAAM8G,OAAEA,EAAM4B,WAAEA,EAAUxB,WAAEA,GAAejE,EAC3C,GAAI+D,GAAiBF,GACnB,MAAM,IAAIxJ,UAAU,wDAEtB,MAAMgS,EAAoB3I,GAAoBG,GAE9C,GAAIuF,EAAWH,kBAAkB/N,OAAS,EAAG,CAC3C,MAAMkR,EAAuBhD,EAAWH,kBAAkBhN,OAC1D,GAAI8H,GAAiBqI,EAAqBvI,QACxC,MAAM,IAAIxJ,UACR,8FAGJwP,GAAkDT,GAClDgD,EAAqBvI,OAASH,GAAoB0I,EAAqBvI,QAC/B,SAApCuI,EAAqBpD,YACvBwB,GAA2DpB,EAAYgD,EAE1E,CAED,GAAI/L,EAA+BzD,GAEjC,GA/QJ,SAAmEwM,GACjE,MAAMzM,EAASyM,EAAWvB,8BAA8B/K,QAExD,KAAOH,EAAOmD,cAAc5E,OAAS,GAAG,CACtC,GAAmC,IAA/BkO,EAAWtD,gBACb,OAGFwC,GAAqDc,EADjCzM,EAAOmD,cAActE,QAE1C,CACH,CAoQI8Q,CAA0DlD,GACT,IAA7ChJ,EAAiCxD,GAEnCwN,GAAgDhB,EAAYiD,EAAmB5G,EAAYxB,OACtF,CAEDmF,EAAWH,kBAAkB/N,OAAS,GAExCwP,GAAiDtB,GAGnDrJ,EAAiCnD,EADT,IAAI4G,WAAW6I,EAAmB5G,EAAYxB,IACa,EACpF,MACQsF,GAA4B3M,IAErCwN,GAAgDhB,EAAYiD,EAAmB5G,EAAYxB,GAC3FyH,GAAiEtC,IAGjEgB,GAAgDhB,EAAYiD,EAAmB5G,EAAYxB,GAG7FiF,GAA6CE,EAC/C,CAEgB,SAAAlB,GAAkCkB,EAA0CpI,GAC1F,MAAMpE,EAASwM,EAAWvB,8BAEJ,aAAlBjL,EAAOG,SAIXoL,GAAkDiB,GAElDjD,GAAWiD,GACXf,GAA4Ce,GAC5CmD,GAAoB3P,EAAQoE,GAC9B,CAEgB,SAAAsH,GACdc,EACAvJ,GAIA,MAAM2M,EAAQpD,EAAWvD,OAAOrK,QAChC4N,EAAWtD,iBAAmB0G,EAAMvI,WAEpCsH,GAA6CnC,GAE7C,MAAM5C,EAAO,IAAIhD,WAAWgJ,EAAM3I,OAAQ2I,EAAM/G,WAAY+G,EAAMvI,YAClEpE,EAAYM,YAAYqG,EAC1B,CAEM,SAAUe,GACd6B,GAEA,GAAgC,OAA5BA,EAAWqC,cAAyBrC,EAAWH,kBAAkB/N,OAAS,EAAG,CAC/E,MAAMuP,EAAkBrB,EAAWH,kBAAkBhN,OAC/CuK,EAAO,IAAIhD,WAAWiH,EAAgB5G,OAChB4G,EAAgBhF,WAAagF,EAAgB7B,YAC7C6B,EAAgBxG,WAAawG,EAAgB7B,aAEnExB,EAAyCxP,OAAO6U,OAAOlG,0BAA0BnO,YA+K3F,SAAwCsU,EACAtD,EACA5C,GAKtCkG,EAAQ5F,wCAA0CsC,EAClDsD,EAAQ/F,MAAQH,CAClB,CAvLImG,CAA+BvF,EAAagC,EAAY5C,GACxD4C,EAAWqC,aAAerE,CAC3B,CACD,OAAOgC,EAAWqC,YACpB,CAEA,SAAShE,GAA2C2B,GAClD,MAAMxB,EAAQwB,EAAWvB,8BAA8B9K,OAEvD,MAAc,YAAV6K,EACK,KAEK,WAAVA,EACK,EAGFwB,EAAWwD,aAAexD,EAAWtD,eAC9C,CAEgB,SAAAiB,GAAoCqC,EAA0CvC,GAG5F,MAAM4D,EAAkBrB,EAAWH,kBAAkBhN,OAGrD,GAAc,WAFAmN,EAAWvB,8BAA8B9K,QAGrD,GAAqB,IAAjB8J,EACF,MAAM,IAAIxM,UAAU,wEAEjB,CAEL,GAAqB,IAAjBwM,EACF,MAAM,IAAIxM,UAAU,mFAEtB,GAAIoQ,EAAgB7B,YAAc/B,EAAe4D,EAAgBxG,WAC/D,MAAM,IAAIiC,WAAW,4BAExB,CAEDuE,EAAgB5G,OAASH,GAAoB+G,EAAgB5G,QAE7DmI,GAA4C5C,EAAYvC,EAC1D,CAEgB,SAAAK,GAA+CkC,EACA5C,GAI7D,MAAMiE,EAAkBrB,EAAWH,kBAAkBhN,OAGrD,GAAc,WAFAmN,EAAWvB,8BAA8B9K,QAGrD,GAAwB,IAApByJ,EAAKvC,WACP,MAAM,IAAI5J,UAAU,yFAItB,GAAwB,IAApBmM,EAAKvC,WACP,MAAM,IAAI5J,UACR,mGAKN,GAAIoQ,EAAgBhF,WAAagF,EAAgB7B,cAAgBpC,EAAKf,WACpE,MAAM,IAAIS,WAAW,2DAEvB,GAAIuE,EAAgB9B,mBAAqBnC,EAAK3C,OAAOI,WACnD,MAAM,IAAIiC,WAAW,8DAEvB,GAAIuE,EAAgB7B,YAAcpC,EAAKvC,WAAawG,EAAgBxG,WAClE,MAAM,IAAIiC,WAAW,2DAGvB,MAAM2G,EAAiBrG,EAAKvC,WAC5BwG,EAAgB5G,OAASH,GAAoB8C,EAAK3C,QAClDmI,GAA4C5C,EAAYyD,EAC1D,CAEgB,SAAAC,GAAkClQ,EACAwM,EACA2D,EACAC,EACAC,EACAC,EACA3E,GAOhDa,EAAWvB,8BAAgCjL,EAE3CwM,EAAWO,YAAa,EACxBP,EAAWM,UAAW,EAEtBN,EAAWqC,aAAe,KAG1BrC,EAAWvD,OAASuD,EAAWtD,qBAAkBzM,EACjD8M,GAAWiD,GAEXA,EAAWzB,iBAAkB,EAC7ByB,EAAWE,UAAW,EAEtBF,EAAWwD,aAAeM,EAE1B9D,EAAWQ,eAAiBoD,EAC5B5D,EAAWhB,iBAAmB6E,EAE9B7D,EAAWZ,uBAAyBD,EAEpCa,EAAWH,kBAAoB,IAAIxO,EAEnCmC,EAAOc,0BAA4B0L,EAGnChQ,EACET,EAFkBoU,MAGlB,KACE3D,EAAWE,UAAW,EAKtBJ,GAA6CE,GACtC,QAET+D,IACEjF,GAAkCkB,EAAY+D,GACvC,OAGb,CAoDA,SAASzG,GAA+B/O,GACtC,OAAO,IAAI0C,UACT,uCAAuC1C,oDAC3C,CAIA,SAAS2P,GAAwC3P,GAC/C,OAAO,IAAI0C,UACT,0CAA0C1C,uDAC9C,CEjnCA,SAASyV,GAAgCC,EAAc3O,GAErD,GAAa,UADb2O,EAAO,GAAGA,KAER,MAAM,IAAIhT,UAAU,GAAGqE,MAAY2O,oEAErC,OAAOA,CACT,CDmBM,SAAUC,GAAgC1Q,GAC9C,OAAO,IAAI2Q,yBAAyB3Q,EACtC,CAIgB,SAAAkP,GACdlP,EACAqN,GAKCrN,EAAOE,QAAsCoN,kBAAkB/O,KAAK8O,EACvE,CAiBM,SAAUT,GAAqC5M,GACnD,OAAQA,EAAOE,QAAqCoN,kBAAkBhP,MACxE,CAEM,SAAUqO,GAA4B3M,GAC1C,MAAMD,EAASC,EAAOE,QAEtB,YAAezD,IAAXsD,KAIC6Q,GAA2B7Q,EAKlC,CDsRA/E,OAAO2J,iBAAiB4F,6BAA6B/O,UAAW,CAC9DsP,MAAO,CAAElG,YAAY,GACrBuG,QAAS,CAAEvG,YAAY,GACvByG,MAAO,CAAEzG,YAAY,GACrB4F,YAAa,CAAE5F,YAAY,GAC3BgG,YAAa,CAAEhG,YAAY,KAE7B/J,EAAgB0P,6BAA6B/O,UAAUsP,MAAO,SAC9DjQ,EAAgB0P,6BAA6B/O,UAAU2P,QAAS,WAChEtQ,EAAgB0P,6BAA6B/O,UAAU6P,MAAO,SAC5B,iBAAvB5L,OAAOoF,aAChB7J,OAAOC,eAAesP,6BAA6B/O,UAAWiE,OAAOoF,YAAa,CAChF3J,MAAO,+BACPC,cAAc,UClRLwV,yBAYX,WAAA7S,CAAYkC,GAIV,GAHAkC,EAAuBlC,EAAQ,EAAG,4BAClC4C,EAAqB5C,EAAQ,mBAEzB2D,GAAuB3D,GACzB,MAAM,IAAIvC,UAAU,+EAGtB,IAAKgN,GAA+BzK,EAAOc,2BACzC,MAAM,IAAIrD,UAAU,+FAItBqC,EAAsC/B,KAAMiC,GAE5CjC,KAAKuP,kBAAoB,IAAIzP,CAC9B,CAMD,UAAI+F,GACF,OAAKgN,GAA2B7S,MAIzBA,KAAKiD,eAHH/E,EAAoB4U,GAA8B,UAI5D,CAKD,MAAA/M,CAAO5H,OAAcO,GACnB,OAAKmU,GAA2B7S,WAIEtB,IAA9BsB,KAAKkC,qBACAhE,EAAoB8E,EAAoB,WAG1CN,EAAkC1C,KAAM7B,GAPtCD,EAAoB4U,GAA8B,UAQ5D,CAWD,IAAA9M,CACE6F,EACAkH,EAAqE,IAErE,IAAKF,GAA2B7S,MAC9B,OAAO9B,EAAoB4U,GAA8B,SAG3D,IAAKpJ,YAAY4C,OAAOT,GACtB,OAAO3N,EAAoB,IAAIwB,UAAU,sCAE3C,GAAwB,IAApBmM,EAAKvC,WACP,OAAOpL,EAAoB,IAAIwB,UAAU,uCAE3C,GAA+B,IAA3BmM,EAAK3C,OAAOI,WACd,OAAOpL,EAAoB,IAAIwB,UAAU,gDAE3C,GAAI0J,GAAiByC,EAAK3C,QACxB,OAAOhL,EAAoB,IAAIwB,UAAU,oCAG3C,IAAIsT,EACJ,IACEA,EC1KU,SACdA,EACAjP,SAIA,OAFAF,EAAiBmP,EAASjP,GAEnB,CACLmM,IAAKzL,EAFqB,QAAhBpH,EAAA2V,aAAA,EAAAA,EAAS9C,WAAO,IAAA7S,EAAAA,EAAA,EAIxB,GAAG0G,2BAGT,CD8JgBkP,CAAuBF,EAAY,UAC9C,CAAC,MAAO1M,GACP,OAAOnI,EAAoBmI,EAC5B,CACD,MAAM6J,EAAM8C,EAAQ9C,IACpB,GAAY,IAARA,EACF,OAAOhS,EAAoB,IAAIwB,UAAU,uCAE3C,GF3KE,SAAqBmM,GACzB,OAAOJ,GAAsBI,EAAK9L,YACpC,CEyKSmT,CAAWrH,IAIT,GAAIqE,EAAMrE,EAAKvC,WACpB,OAAOpL,EAAoB,IAAIqN,WAAW,qEAJ1C,GAAI2E,EAAOrE,EAA+BtL,OACxC,OAAOrC,EAAoB,IAAIqN,WAAW,4DAM9C,QAAkC7M,IAA9BsB,KAAKkC,qBACP,OAAOhE,EAAoB8E,EAAoB,cAGjD,IAAIiD,EACAC,EACJ,MAAM7H,EAAUP,GAA4C,CAACG,EAASL,KACpEqI,EAAiBhI,EACjBiI,EAAgBtI,CAAM,IAQxB,OADAuV,GAA6BnT,KAAM6L,EAAMqE,EALG,CAC1C1K,YAAaH,GAASY,EAAe,CAAE9I,MAAOkI,EAAOC,MAAM,IAC3DC,YAAaF,GAASY,EAAe,CAAE9I,MAAOkI,EAAOC,MAAM,IAC3Dc,YAAaC,GAAKH,EAAcG,KAG3BhI,CACR,CAWD,WAAAiI,GACE,IAAKuM,GAA2B7S,MAC9B,MAAM8S,GAA8B,oBAGJpU,IAA9BsB,KAAKkC,sBA8DP,SAA0CF,GAC9CY,EAAmCZ,GACnC,MAAMqE,EAAI,IAAI3G,UAAU,uBACxB0T,GAA8CpR,EAAQqE,EACxD,CA9DIgN,CAAgCrT,KACjC,EAqBG,SAAU6S,GAA2BjW,GACzC,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,sBAItCA,aAAagW,yBACtB,CAEM,SAAUO,GACdnR,EACA6J,EACAqE,EACAZ,GAEA,MAAMrN,EAASD,EAAOE,qBAItBD,EAAOyE,YAAa,EAEE,YAAlBzE,EAAOG,OACTkN,EAAgBlJ,YAAYnE,EAAOQ,cAEnCuO,GACE/O,EAAOc,0BACP8I,EACAqE,EACAZ,EAGN,CAQgB,SAAA8D,GAA8CpR,EAAkCqE,GAC9F,MAAMiN,EAAmBtR,EAAOuN,kBAChCvN,EAAOuN,kBAAoB,IAAIzP,EAC/BwT,EAAiBnS,SAAQmO,IACvBA,EAAgBlJ,YAAYC,EAAE,GAElC,CAIA,SAASyM,GAA8B9V,GACrC,OAAO,IAAI0C,UACT,sCAAsC1C,mDAC1C,CEjUgB,SAAAuW,GAAqBC,EAA2BC,GAC9D,MAAMlB,cAAEA,GAAkBiB,EAE1B,QAAsB9U,IAAlB6T,EACF,OAAOkB,EAGT,GAAItL,GAAYoK,IAAkBA,EAAgB,EAChD,MAAM,IAAIhH,WAAW,yBAGvB,OAAOgH,CACT,CAEM,SAAUmB,GAAwBF,GACtC,MAAMpI,KAAEA,GAASoI,EAEjB,OAAKpI,GACI,KAAM,EAIjB,CCtBgB,SAAAuI,GAA0BC,EACA7P,GACxCF,EAAiB+P,EAAM7P,GACvB,MAAMwO,EAAgBqB,aAAA,EAAAA,EAAMrB,cACtBnH,EAAOwI,aAAA,EAAAA,EAAMxI,KACnB,MAAO,CACLmH,mBAAiC7T,IAAlB6T,OAA8B7T,EAAY6F,EAA0BgO,GACnFnH,UAAe1M,IAAT0M,OAAqB1M,EAAYmV,GAA2BzI,EAAM,GAAGrH,4BAE/E,CAEA,SAAS8P,GAA8B9W,EACAgH,GAErC,OADAC,EAAejH,EAAIgH,GACZsB,GAASd,EAA0BxH,EAAGsI,GAC/C,CCmBA,SAASyO,GACP/W,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX5F,GAAgB0B,EAAY9C,EAAIgX,EAAU,CAAC5V,GACrD,CAEA,SAAS6V,GACPjX,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACZ,IAAMlE,EAAY9C,EAAIgX,EAAU,GACzC,CAEA,SAASE,GACPlX,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX0K,GAAgDnP,EAAYvC,EAAIgX,EAAU,CAACtF,GACrF,CAEA,SAASyF,GACPnX,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACZ,CAACsB,EAAUoJ,IAAgD5O,EAAY9C,EAAIgX,EAAU,CAAC1O,EAAOoJ,GACtG,CCrEgB,SAAA0F,GAAqBvX,EAAYmH,GAC/C,IAAKqQ,GAAiBxX,GACpB,MAAM,IAAI8C,UAAU,GAAGqE,6BAE3B,CLqPA9G,OAAO2J,iBAAiBgM,yBAAyBnV,UAAW,CAC1DsI,OAAQ,CAAEc,YAAY,GACtBb,KAAM,CAAEa,YAAY,GACpBP,YAAa,CAAEO,YAAY,GAC3BhB,OAAQ,CAAEgB,YAAY,KAExB/J,EAAgB8V,yBAAyBnV,UAAUsI,OAAQ,UAC3DjJ,EAAgB8V,yBAAyBnV,UAAUuI,KAAM,QACzDlJ,EAAgB8V,yBAAyBnV,UAAU6I,YAAa,eAC9B,iBAAvB5E,OAAOoF,aAChB7J,OAAOC,eAAe0V,yBAAyBnV,UAAWiE,OAAOoF,YAAa,CAC5E3J,MAAO,2BACPC,cAAc,IMtMlB,MAAMiX,GAA8D,mBAA5BC,gBCPxC,MAAMC,eAuBJ,WAAAxU,CAAYyU,EAA0D,GAC1DC,EAAqD,CAAA,QACrC/V,IAAtB8V,EACFA,EAAoB,KAEpBvQ,EAAauQ,EAAmB,mBAGlC,MAAMhB,EAAWG,GAAuBc,EAAa,oBAC/CC,EH9EM,SAAyBX,EACAhQ,GACvCF,EAAiBkQ,EAAUhQ,GAC3B,MAAM4Q,EAAQZ,aAAA,EAAAA,EAAUY,MAClB5H,EAAQgH,aAAA,EAAAA,EAAUhH,MAClB6H,EAAQb,aAAA,EAAAA,EAAUa,MAClBC,EAAOd,aAAA,EAAAA,EAAUc,KACjBC,EAAQf,aAAA,EAAAA,EAAUe,MACxB,MAAO,CACLH,WAAiBjW,IAAViW,OACLjW,EACAoV,GAAmCa,EAAOZ,EAAW,GAAGhQ,6BAC1DgJ,WAAiBrO,IAAVqO,OACLrO,EACAsV,GAAmCjH,EAAOgH,EAAW,GAAGhQ,6BAC1D6Q,WAAiBlW,IAAVkW,OACLlW,EACAuV,GAAmCW,EAAOb,EAAW,GAAGhQ,6BAC1D+Q,WAAiBpW,IAAVoW,OACLpW,EACAwV,GAAmCY,EAAOf,EAAW,GAAGhQ,6BAC1D8Q,OAEJ,CGuD2BE,CAAsBP,EAAmB,mBAEhEQ,GAAyBhV,MAGzB,QAAatB,IADAgW,EAAeG,KAE1B,MAAM,IAAItJ,WAAW,6BAGvB,MAAM0J,EAAgBvB,GAAqBF,IAq/B/C,SAAmEvR,EACAyS,EACAnC,EACA0C,GACjE,MAAMxG,EAAaxR,OAAO6U,OAAOoD,gCAAgCzX,WAEjE,IAAI2U,EACA+C,EACAC,EACAC,EAGFjD,OAD2B1T,IAAzBgW,EAAeE,MACA,IAAMF,EAAeE,MAAOnG,GAE5B,KAAe,EAGhC0G,OAD2BzW,IAAzBgW,EAAeI,MACAzP,GAASqP,EAAeI,MAAOzP,EAAOoJ,GAEtC,IAAMzQ,OAAoBU,GAG3C0W,OAD2B1W,IAAzBgW,EAAe3H,MACA,IAAM2H,EAAe3H,QAErB,IAAM/O,OAAoBU,GAG3C2W,OAD2B3W,IAAzBgW,EAAeC,MACAxW,GAAUuW,EAAeC,MAAOxW,GAEhC,IAAMH,OAAoBU,GAG7C4W,GACErT,EAAQwM,EAAY2D,EAAgB+C,EAAgBC,EAAgBC,EAAgB9C,EAAe0C,EAEvG,CArhCIM,CAAuDvV,KAAM0U,EAFvCnB,GAAqBC,EAAU,GAEuCyB,EAC7F,CAKD,UAAIO,GACF,IAAKpB,GAAiBpU,MACpB,MAAMyV,GAA0B,UAGlC,OAAOC,GAAuB1V,KAC/B,CAWD,KAAA2U,CAAMxW,OAAcO,GAClB,OAAK0V,GAAiBpU,MAIlB0V,GAAuB1V,MAClB9B,EAAoB,IAAIwB,UAAU,oDAGpCiW,GAAoB3V,KAAM7B,GAPxBD,EAAoBuX,GAA0B,SAQxD,CAUD,KAAA1I,GACE,OAAKqH,GAAiBpU,MAIlB0V,GAAuB1V,MAClB9B,EAAoB,IAAIwB,UAAU,oDAGvCkW,GAAoC5V,MAC/B9B,EAAoB,IAAIwB,UAAU,2CAGpCmW,GAAoB7V,MAXlB9B,EAAoBuX,GAA0B,SAYxD,CAUD,SAAAK,GACE,IAAK1B,GAAiBpU,MACpB,MAAMyV,GAA0B,aAGlC,OAAOM,GAAmC/V,KAC3C,EA2CH,SAAS+V,GAAsC9T,GAC7C,OAAO,IAAI+T,4BAA4B/T,EACzC,CAqBA,SAAS+S,GAA4B/S,GACnCA,EAAOG,OAAS,WAIhBH,EAAOQ,kBAAe/D,EAEtBuD,EAAOgU,aAAUvX,EAIjBuD,EAAOiU,+BAA4BxX,EAInCuD,EAAOkU,eAAiB,IAAIrW,EAI5BmC,EAAOmU,2BAAwB1X,EAI/BuD,EAAOoU,mBAAgB3X,EAIvBuD,EAAOqU,2BAAwB5X,EAG/BuD,EAAOsU,0BAAuB7X,EAG9BuD,EAAOuU,eAAgB,CACzB,CAEA,SAASpC,GAAiBxX,GACxB,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,8BAItCA,aAAa2X,eACtB,CAEA,SAASmB,GAAuBzT,GAG9B,YAAuBvD,IAAnBuD,EAAOgU,OAKb,CAEA,SAASN,GAAoB1T,EAAwB9D,SACnD,GAAsB,WAAlB8D,EAAOG,QAAyC,YAAlBH,EAAOG,OACvC,OAAOpE,OAAoBU,GAE7BuD,EAAOiU,0BAA0BO,aAAetY,UAChDd,EAAA4E,EAAOiU,0BAA0BQ,iCAAkB/B,MAAMxW,GAKzD,MAAM8O,EAAQhL,EAAOG,OAErB,GAAc,WAAV6K,GAAgC,YAAVA,EACxB,OAAOjP,OAAoBU,GAE7B,QAAoCA,IAAhCuD,EAAOsU,qBACT,OAAOtU,EAAOsU,qBAAqBI,SAKrC,IAAIC,GAAqB,EACX,aAAV3J,IACF2J,GAAqB,EAErBzY,OAASO,GAGX,MAAML,EAAUP,GAAsB,CAACG,EAASL,KAC9CqE,EAAOsU,qBAAuB,CAC5BI,cAAUjY,EACVmY,SAAU5Y,EACV6Y,QAASlZ,EACTmZ,QAAS5Y,EACT6Y,oBAAqBJ,EACtB,IAQH,OANA3U,EAAOsU,qBAAsBI,SAAWtY,EAEnCuY,GACHK,GAA4BhV,EAAQ9D,GAG/BE,CACT,CAEA,SAASwX,GAAoB5T,GAC3B,MAAMgL,EAAQhL,EAAOG,OACrB,GAAc,WAAV6K,GAAgC,YAAVA,EACxB,OAAO/O,EAAoB,IAAIwB,UAC7B,kBAAkBuN,+DAMtB,MAAM5O,EAAUP,GAAsB,CAACG,EAASL,KAC9C,MAAMsZ,EAA6B,CACjCL,SAAU5Y,EACV6Y,QAASlZ,GAGXqE,EAAOoU,cAAgBa,CAAY,IAG/BC,EAASlV,EAAOgU,QAyxBxB,IAAiDxH,EAlxB/C,YANe/P,IAAXyY,GAAwBlV,EAAOuU,eAA2B,aAAVvJ,GAClDmK,GAAiCD,GAwxBnC9L,GAD+CoD,EApxBVxM,EAAOiU,0BAqxBXmB,GAAe,GAChDC,GAAoD7I,GApxB7CpQ,CACT,CAoBA,SAASkZ,GAAgCtV,EAAwBqL,GAGjD,aAFArL,EAAOG,OAQrBoV,GAA6BvV,GAL3BgV,GAA4BhV,EAAQqL,EAMxC,CAEA,SAAS2J,GAA4BhV,EAAwB9D,GAI3D,MAAMsQ,EAAaxM,EAAOiU,0BAG1BjU,EAAOG,OAAS,WAChBH,EAAOQ,aAAetE,EACtB,MAAMgZ,EAASlV,EAAOgU,aACPvX,IAAXyY,GACFM,GAAsDN,EAAQhZ,IAsHlE,SAAkD8D,GAChD,QAAqCvD,IAAjCuD,EAAOmU,4BAAwE1X,IAAjCuD,EAAOqU,sBACvD,OAAO,EAGT,OAAO,CACT,CAzHOoB,CAAyCzV,IAAWwM,EAAWE,UAClE6I,GAA6BvV,EAEjC,CAEA,SAASuV,GAA6BvV,GAGpCA,EAAOG,OAAS,UAChBH,EAAOiU,0BAA0BvU,KAEjC,MAAMgW,EAAc1V,EAAOQ,aAM3B,GALAR,EAAOkU,eAAehV,SAAQyW,IAC5BA,EAAad,QAAQa,EAAY,IAEnC1V,EAAOkU,eAAiB,IAAIrW,OAEQpB,IAAhCuD,EAAOsU,qBAET,YADAsB,GAAkD5V,GAIpD,MAAM6V,EAAe7V,EAAOsU,qBAG5B,GAFAtU,EAAOsU,0BAAuB7X,EAE1BoZ,EAAad,oBAGf,OAFAc,EAAahB,QAAQa,QACrBE,GAAkD5V,GAKpDxD,EADgBwD,EAAOiU,0BAA0BzU,GAAYqW,EAAaf,UAGxE,KACEe,EAAajB,WACbgB,GAAkD5V,GAC3C,QAER9D,IACC2Z,EAAahB,QAAQ3Y,GACrB0Z,GAAkD5V,GAC3C,OAEb,CA+DA,SAAS2T,GAAoC3T,GAC3C,YAA6BvD,IAAzBuD,EAAOoU,oBAAgE3X,IAAjCuD,EAAOqU,qBAKnD,CAuBA,SAASuB,GAAkD5V,QAE5BvD,IAAzBuD,EAAOoU,gBAGTpU,EAAOoU,cAAcS,QAAQ7U,EAAOQ,cACpCR,EAAOoU,mBAAgB3X,GAEzB,MAAMyY,EAASlV,EAAOgU,aACPvX,IAAXyY,GACFY,GAAiCZ,EAAQlV,EAAOQ,aAEpD,CAEA,SAASuV,GAAiC/V,EAAwBgW,GAIhE,MAAMd,EAASlV,EAAOgU,aACPvX,IAAXyY,GAAwBc,IAAiBhW,EAAOuU,gBAC9CyB,EAs0BR,SAAwCd,GAItCe,GAAoCf,EACtC,CA10BMgB,CAA+BhB,GAI/BC,GAAiCD,IAIrClV,EAAOuU,cAAgByB,CACzB,CAtZAhb,OAAO2J,iBAAiB2N,eAAe9W,UAAW,CAChDkX,MAAO,CAAE9N,YAAY,GACrBkG,MAAO,CAAElG,YAAY,GACrBiP,UAAW,CAAEjP,YAAY,GACzB2O,OAAQ,CAAE3O,YAAY,KAExB/J,EAAgByX,eAAe9W,UAAUkX,MAAO,SAChD7X,EAAgByX,eAAe9W,UAAUsP,MAAO,SAChDjQ,EAAgByX,eAAe9W,UAAUqY,UAAW,aAClB,iBAAvBpU,OAAOoF,aAChB7J,OAAOC,eAAeqX,eAAe9W,UAAWiE,OAAOoF,YAAa,CAClE3J,MAAO,iBACPC,cAAc,UAiZL4Y,4BAoBX,WAAAjW,CAAYkC,GAIV,GAHAkC,EAAuBlC,EAAQ,EAAG,+BAClCkS,GAAqBlS,EAAQ,mBAEzByT,GAAuBzT,GACzB,MAAM,IAAIvC,UAAU,+EAGtBM,KAAKoY,qBAAuBnW,EAC5BA,EAAOgU,QAAUjW,KAEjB,MAAMiN,EAAQhL,EAAOG,OAErB,GAAc,aAAV6K,GACG2I,GAAoC3T,IAAWA,EAAOuU,cACzD0B,GAAoClY,MAEpCqY,GAA8CrY,MAGhDsY,GAAqCtY,WAChC,GAAc,aAAViN,EACTsL,GAA8CvY,KAAMiC,EAAOQ,cAC3D6V,GAAqCtY,WAChC,GAAc,WAAViN,EACToL,GAA8CrY,MAqsBlDsY,GADsDnB,EAnsBHnX,MAqsBnDwY,GAAkCrB,OApsBzB,CAGL,MAAMQ,EAAc1V,EAAOQ,aAC3B8V,GAA8CvY,KAAM2X,GACpDc,GAA+CzY,KAAM2X,EACtD,CA4rBL,IAAwDR,CA3rBrD,CAMD,UAAItR,GACF,OAAK6S,GAA8B1Y,MAI5BA,KAAKiD,eAHH/E,EAAoBya,GAAiC,UAI/D,CAUD,eAAI9L,GACF,IAAK6L,GAA8B1Y,MACjC,MAAM2Y,GAAiC,eAGzC,QAAkCja,IAA9BsB,KAAKoY,qBACP,MAAMQ,GAA2B,eAGnC,OA+LJ,SAAmDzB,GACjD,MAAMlV,EAASkV,EAAOiB,qBAChBnL,EAAQhL,EAAOG,OAErB,GAAc,YAAV6K,GAAiC,aAAVA,EACzB,OAAO,KAGT,GAAc,WAAVA,EACF,OAAO,EAGT,OAAO4L,GAA8C5W,EAAOiU,0BAC9D,CA5MW4C,CAA0C9Y,KAClD,CAUD,SAAIqQ,GACF,OAAKqI,GAA8B1Y,MAI5BA,KAAK+Y,cAHH7a,EAAoBya,GAAiC,SAI/D,CAKD,KAAAhE,CAAMxW,OAAcO,GAClB,OAAKga,GAA8B1Y,WAIDtB,IAA9BsB,KAAKoY,qBACAla,EAAoB0a,GAA2B,UAgH5D,SAA0CzB,EAAqChZ,GAK7E,OAAOwX,GAJQwB,EAAOiB,qBAIaja,EACrC,CAnHW6a,CAAiChZ,KAAM7B,GAPrCD,EAAoBya,GAAiC,SAQ/D,CAKD,KAAA5L,GACE,IAAK2L,GAA8B1Y,MACjC,OAAO9B,EAAoBya,GAAiC,UAG9D,MAAM1W,EAASjC,KAAKoY,qBAEpB,YAAe1Z,IAAXuD,EACK/D,EAAoB0a,GAA2B,UAGpDhD,GAAoC3T,GAC/B/D,EAAoB,IAAIwB,UAAU,2CAGpCuZ,GAAiCjZ,KACzC,CAYD,WAAAsG,GACE,IAAKoS,GAA8B1Y,MACjC,MAAM2Y,GAAiC,oBAK1Bja,IAFAsB,KAAKoY,sBAQpBc,GAAmClZ,KACpC,CAYD,KAAA8U,CAAMzP,OAAW3G,GACf,OAAKga,GAA8B1Y,WAIDtB,IAA9BsB,KAAKoY,qBACAla,EAAoB0a,GAA2B,aAGjDO,GAAiCnZ,KAAMqF,GAPrCnH,EAAoBya,GAAiC,SAQ/D,EAyBH,SAASD,GAAuC9b,GAC9C,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,yBAItCA,aAAaoZ,4BACtB,CAYA,SAASiD,GAAiC9B,GAKxC,OAAOtB,GAJQsB,EAAOiB,qBAKxB,CAqBA,SAASgB,GAAuDjC,EAAqC7J,GAChE,YAA/B6J,EAAOkC,oBACTtB,GAAiCZ,EAAQ7J,GA6f7C,SAAmD6J,EAAqChZ,GAKtFsa,GAA+CtB,EAAQhZ,EACzD,CAjgBImb,CAA0CnC,EAAQ7J,EAEtD,CAEA,SAASmK,GAAsDN,EAAqC7J,GAChE,YAA9B6J,EAAOoC,mBACTC,GAAgCrC,EAAQ7J,GA8iB5C,SAAkD6J,EAAqChZ,GAIrFoa,GAA8CpB,EAAQhZ,EACxD,CAjjBIsb,CAAyCtC,EAAQ7J,EAErD,CAiBA,SAAS4L,GAAmC/B,GAC1C,MAAMlV,EAASkV,EAAOiB,qBAIhBsB,EAAgB,IAAIha,UACxB,oFAEF+X,GAAsDN,EAAQuC,GAI9DN,GAAuDjC,EAAQuC,GAE/DzX,EAAOgU,aAAUvX,EACjByY,EAAOiB,0BAAuB1Z,CAChC,CAEA,SAASya,GAAoChC,EAAwC9R,GACnF,MAAMpD,EAASkV,EAAOiB,qBAIhB3J,EAAaxM,EAAOiU,0BAEpByD,EA+PR,SAAwDlL,EACApJ,GACtD,IACE,OAAOoJ,EAAWmL,uBAAuBvU,EAC1C,CAAC,MAAOwU,GAEP,OADAC,GAA6CrL,EAAYoL,GAClD,CACR,CACH,CAvQoBE,CAA4CtL,EAAYpJ,GAE1E,GAAIpD,IAAWkV,EAAOiB,qBACpB,OAAOla,EAAoB0a,GAA2B,aAGxD,MAAM3L,EAAQhL,EAAOG,OACrB,GAAc,YAAV6K,EACF,OAAO/O,EAAoB+D,EAAOQ,cAEpC,GAAImT,GAAoC3T,IAAqB,WAAVgL,EACjD,OAAO/O,EAAoB,IAAIwB,UAAU,6DAE3C,GAAc,aAAVuN,EACF,OAAO/O,EAAoB+D,EAAOQ,cAKpC,MAAMpE,EAtiBR,SAAuC4D,GAarC,OATgBnE,GAAsB,CAACG,EAASL,KAC9C,MAAMga,EAA6B,CACjCf,SAAU5Y,EACV6Y,QAASlZ,GAGXqE,EAAOkU,eAAe3V,KAAKoX,EAAa,GAI5C,CAwhBkBoC,CAA8B/X,GAI9C,OAsPF,SAAiDwM,EACApJ,EACAsU,GAC/C,IACEtO,GAAqBoD,EAAYpJ,EAAOsU,EACzC,CAAC,MAAOM,GAEP,YADAH,GAA6CrL,EAAYwL,EAE1D,CAED,MAAMhY,EAASwM,EAAWyL,0BAC1B,IAAKtE,GAAoC3T,IAA6B,aAAlBA,EAAOG,OAAuB,CAEhF4V,GAAiC/V,EADZkY,GAA+C1L,GAErE,CAED6I,GAAoD7I,EACtD,CAzQE2L,CAAqC3L,EAAYpJ,EAAOsU,GAEjDtb,CACT,CAvJApB,OAAO2J,iBAAiBoP,4BAA4BvY,UAAW,CAC7DkX,MAAO,CAAE9N,YAAY,GACrBkG,MAAO,CAAElG,YAAY,GACrBP,YAAa,CAAEO,YAAY,GAC3BiO,MAAO,CAAEjO,YAAY,GACrBhB,OAAQ,CAAEgB,YAAY,GACtBgG,YAAa,CAAEhG,YAAY,GAC3BwJ,MAAO,CAAExJ,YAAY,KAEvB/J,EAAgBkZ,4BAA4BvY,UAAUkX,MAAO,SAC7D7X,EAAgBkZ,4BAA4BvY,UAAUsP,MAAO,SAC7DjQ,EAAgBkZ,4BAA4BvY,UAAU6I,YAAa,eACnExJ,EAAgBkZ,4BAA4BvY,UAAUqX,MAAO,SAC3B,iBAAvBpT,OAAOoF,aAChB7J,OAAOC,eAAe8Y,4BAA4BvY,UAAWiE,OAAOoF,YAAa,CAC/E3J,MAAO,8BACPC,cAAc,IAyIlB,MAAMia,GAA+B,CAAA,QASxBnC,gCAwBX,WAAAnV,GACE,MAAM,IAAIL,UAAU,sBACrB,CASD,eAAI2a,GACF,IAAKC,GAAkCta,MACrC,MAAMua,GAAqC,eAE7C,OAAOva,KAAKyW,YACb,CAKD,UAAI+D,GACF,IAAKF,GAAkCta,MACrC,MAAMua,GAAqC,UAE7C,QAA8B7b,IAA1BsB,KAAK0W,iBAIP,MAAM,IAAIhX,UAAU,qEAEtB,OAAOM,KAAK0W,iBAAiB8D,MAC9B,CASD,KAAAlN,CAAMjH,OAAS3H,GACb,IAAK4b,GAAkCta,MACrC,MAAMua,GAAqC,SAG/B,aADAva,KAAKka,0BAA0B9X,QAO7CqY,GAAqCza,KAAMqG,EAC5C,CAGD,CAAC5E,GAAYtD,GACX,MAAMyJ,EAAS5H,KAAK0a,gBAAgBvc,GAEpC,OADAwc,GAA+C3a,MACxC4H,CACR,CAGD,CAACjG,KACC6J,GAAWxL,KACZ,EAiBH,SAASsa,GAAkC1d,GACzC,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,8BAItCA,aAAasY,gCACtB,CAEA,SAASI,GAAwCrT,EACAwM,EACA2D,EACA+C,EACAC,EACAC,EACA9C,EACA0C,GAI/CxG,EAAWyL,0BAA4BjY,EACvCA,EAAOiU,0BAA4BzH,EAGnCA,EAAWvD,YAASxM,EACpB+P,EAAWtD,qBAAkBzM,EAC7B8M,GAAWiD,GAEXA,EAAWgI,kBAAe/X,EAC1B+P,EAAWiI,4BD/+BX,GAAIrC,GACF,OAAO,IAAKC,eAGhB,CC2+BgCsG,GAC9BnM,EAAWE,UAAW,EAEtBF,EAAWmL,uBAAyB3E,EACpCxG,EAAWwD,aAAeM,EAE1B9D,EAAWoM,gBAAkB1F,EAC7B1G,EAAWqM,gBAAkB1F,EAC7B3G,EAAWiM,gBAAkBrF,EAE7B,MAAM4C,EAAekC,GAA+C1L,GACpEuJ,GAAiC/V,EAAQgW,GAIzCxZ,EADqBT,EADDoU,MAIlB,KAEE3D,EAAWE,UAAW,EACtB2I,GAAoD7I,GAC7C,QAET+D,IAEE/D,EAAWE,UAAW,EACtB4I,GAAgCtV,EAAQuQ,GACjC,OAGb,CAwCA,SAASmI,GAA+ClM,GACtDA,EAAWoM,qBAAkBnc,EAC7B+P,EAAWqM,qBAAkBpc,EAC7B+P,EAAWiM,qBAAkBhc,EAC7B+P,EAAWmL,4BAAyBlb,CACtC,CAiBA,SAASma,GAA8CpK,GACrD,OAAOA,EAAWwD,aAAexD,EAAWtD,eAC9C,CAuBA,SAASmM,GAAuD7I,GAC9D,MAAMxM,EAASwM,EAAWyL,0BAE1B,IAAKzL,EAAWE,SACd,OAGF,QAAqCjQ,IAAjCuD,EAAOmU,sBACT,OAKF,GAAc,aAFAnU,EAAOG,OAInB,YADAoV,GAA6BvV,GAI/B,GAAiC,IAA7BwM,EAAWvD,OAAO3K,OACpB,OAGF,MAAMpD,EAAuBsR,EVzpCNvD,OAAO5J,OAClBnE,MUypCRA,IAAUka,GAahB,SAAqD5I,GACnD,MAAMxM,EAASwM,EAAWyL,2BArrB5B,SAAgDjY,GAG9CA,EAAOqU,sBAAwBrU,EAAOoU,cACtCpU,EAAOoU,mBAAgB3X,CACzB,EAkrBEqc,CAAuC9Y,GAEvC8I,GAAa0D,GAGb,MAAMuM,EAAmBvM,EAAWqM,kBACpCH,GAA+ClM,GAC/ChQ,EACEuc,GACA,KA7vBJ,SAA2C/Y,GAEzCA,EAAOqU,sBAAuBO,cAASnY,GACvCuD,EAAOqU,2BAAwB5X,EAMjB,aAJAuD,EAAOG,SAMnBH,EAAOQ,kBAAe/D,OACcA,IAAhCuD,EAAOsU,uBACTtU,EAAOsU,qBAAqBM,WAC5B5U,EAAOsU,0BAAuB7X,IAIlCuD,EAAOG,OAAS,SAEhB,MAAM+U,EAASlV,EAAOgU,aACPvX,IAAXyY,GACFqB,GAAkCrB,EAKtC,CAmuBM8D,CAAkChZ,GAC3B,QAET9D,IApuBJ,SAAoD8D,EAAwBqL,GAE1ErL,EAAOqU,sBAAuBQ,QAAQxJ,GACtCrL,EAAOqU,2BAAwB5X,OAKKA,IAAhCuD,EAAOsU,uBACTtU,EAAOsU,qBAAqBO,QAAQxJ,GACpCrL,EAAOsU,0BAAuB7X,GAEhC6Y,GAAgCtV,EAAQqL,EAC1C,CAwtBM4N,CAA2CjZ,EAAQ9D,GAC5C,OAGb,CAjCIgd,CAA4C1M,GAmChD,SAAwDA,EAAgDpJ,GACtG,MAAMpD,EAASwM,EAAWyL,2BArsB5B,SAAqDjY,GAGnDA,EAAOmU,sBAAwBnU,EAAOkU,eAAetV,OACvD,CAmsBEua,CAA4CnZ,GAE5C,MAAMoZ,EAAmB5M,EAAWoM,gBAAgBxV,GACpD5G,EACE4c,GACA,MAhyBJ,SAA2CpZ,GAEzCA,EAAOmU,sBAAuBS,cAASnY,GACvCuD,EAAOmU,2BAAwB1X,CACjC,CA6xBM4c,CAAkCrZ,GAElC,MAAMgL,EAAQhL,EAAOG,OAKrB,GAFA2I,GAAa0D,IAERmH,GAAoC3T,IAAqB,aAAVgL,EAAsB,CACxE,MAAMgL,EAAekC,GAA+C1L,GACpEuJ,GAAiC/V,EAAQgW,EAC1C,CAGD,OADAX,GAAoD7I,GAC7C,IAAI,IAEbtQ,IACwB,aAAlB8D,EAAOG,QACTuY,GAA+ClM,GA5yBvD,SAAoDxM,EAAwBqL,GAE1ErL,EAAOmU,sBAAuBU,QAAQxJ,GACtCrL,EAAOmU,2BAAwB1X,EAI/B6Y,GAAgCtV,EAAQqL,EAC1C,CAsyBMiO,CAA2CtZ,EAAQ9D,GAC5C,OAGb,CAjEIqd,CAA4C/M,EAAYtR,EAE5D,CAEA,SAAS2c,GAA6CrL,EAAkDnB,GAClD,aAAhDmB,EAAWyL,0BAA0B9X,QACvCqY,GAAqChM,EAAYnB,EAErD,CA2DA,SAAS6M,GAA+C1L,GAEtD,OADoBoK,GAA8CpK,IAC5C,CACxB,CAIA,SAASgM,GAAqChM,EAAkDnB,GAC9F,MAAMrL,EAASwM,EAAWyL,0BAI1BS,GAA+ClM,GAC/CwI,GAA4BhV,EAAQqL,EACtC,CAIA,SAASmI,GAA0BzY,GACjC,OAAO,IAAI0C,UAAU,4BAA4B1C,yCACnD,CAIA,SAASud,GAAqCvd,GAC5C,OAAO,IAAI0C,UACT,6CAA6C1C,0DACjD,CAKA,SAAS2b,GAAiC3b,GACxC,OAAO,IAAI0C,UACT,yCAAyC1C,sDAC7C,CAEA,SAAS4b,GAA2B5b,GAClC,OAAO,IAAI0C,UAAU,UAAY1C,EAAO,oCAC1C,CAEA,SAASsb,GAAqCnB,GAC5CA,EAAOlU,eAAiBnF,GAAW,CAACG,EAASL,KAC3CuZ,EAAOjU,uBAAyBjF,EAChCkZ,EAAOhU,sBAAwBvF,EAC/BuZ,EAAOkC,oBAAsB,SAAS,GAE1C,CAEA,SAASZ,GAA+CtB,EAAqChZ,GAC3Fma,GAAqCnB,GACrCY,GAAiCZ,EAAQhZ,EAC3C,CAOA,SAAS4Z,GAAiCZ,EAAqChZ,QACxCO,IAAjCyY,EAAOhU,wBAKXnE,EAA0BmY,EAAOlU,gBACjCkU,EAAOhU,sBAAsBhF,GAC7BgZ,EAAOjU,4BAAyBxE,EAChCyY,EAAOhU,2BAAwBzE,EAC/ByY,EAAOkC,oBAAsB,WAC/B,CAUA,SAASb,GAAkCrB,QACHzY,IAAlCyY,EAAOjU,yBAKXiU,EAAOjU,4BAAuBxE,GAC9ByY,EAAOjU,4BAAyBxE,EAChCyY,EAAOhU,2BAAwBzE,EAC/ByY,EAAOkC,oBAAsB,WAC/B,CAEA,SAASnB,GAAoCf,GAC3CA,EAAO4B,cAAgBjb,GAAW,CAACG,EAASL,KAC1CuZ,EAAOsE,sBAAwBxd,EAC/BkZ,EAAOuE,qBAAuB9d,CAAM,IAEtCuZ,EAAOoC,mBAAqB,SAC9B,CAEA,SAAShB,GAA8CpB,EAAqChZ,GAC1F+Z,GAAoCf,GACpCqC,GAAgCrC,EAAQhZ,EAC1C,CAEA,SAASka,GAA8ClB,GACrDe,GAAoCf,GACpCC,GAAiCD,EACnC,CAEA,SAASqC,GAAgCrC,EAAqChZ,QACxCO,IAAhCyY,EAAOuE,uBAIX1c,EAA0BmY,EAAO4B,eACjC5B,EAAOuE,qBAAqBvd,GAC5BgZ,EAAOsE,2BAAwB/c,EAC/ByY,EAAOuE,0BAAuBhd,EAC9ByY,EAAOoC,mBAAqB,WAC9B,CAgBA,SAASnC,GAAiCD,QACHzY,IAAjCyY,EAAOsE,wBAIXtE,EAAOsE,2BAAsB/c,GAC7ByY,EAAOsE,2BAAwB/c,EAC/ByY,EAAOuE,0BAAuBhd,EAC9ByY,EAAOoC,mBAAqB,YAC9B,CAjZAtc,OAAO2J,iBAAiBsO,gCAAgCzX,UAAW,CACjE4c,YAAa,CAAExT,YAAY,GAC3B2T,OAAQ,CAAE3T,YAAY,GACtByG,MAAO,CAAEzG,YAAY,KAEW,iBAAvBnF,OAAOoF,aAChB7J,OAAOC,eAAegY,gCAAgCzX,UAAWiE,OAAOoF,YAAa,CACnF3J,MAAO,kCACPC,cAAc,ICrgCX,MAAMue,GAVe,oBAAfC,WACFA,WACkB,oBAATC,KACTA,KACoB,oBAAXC,OACTA,YADF,ECiDT,MAAMC,GAzBN,WACE,MAAMrQ,EAAOiQ,cAAA,EAAAA,GAASI,aACtB,OAtBF,SAAmCrQ,GACjC,GAAsB,mBAATA,GAAuC,iBAATA,EACzC,OAAO,EAET,GAA+C,iBAA1CA,EAAiC1O,KACpC,OAAO,EAET,IAEE,OADA,IAAK0O,GACE,CACR,CAAC,MAAArO,GACA,OAAO,CACR,CACH,CASS2e,CAA0BtQ,GAAQA,OAAOhN,CAClD,CAsB8Cud,IAhB9C,WAEE,MAAMvQ,EAAO,SAA0CwQ,EAAkBlf,GACvEgD,KAAKkc,QAAUA,GAAW,GAC1Blc,KAAKhD,KAAOA,GAAQ,QAChBmf,MAAMC,mBACRD,MAAMC,kBAAkBpc,KAAMA,KAAKD,YAEvC,EAIA,OAHAjD,EAAgB4O,EAAM,gBACtBA,EAAKjO,UAAYR,OAAO6U,OAAOqK,MAAM1e,WACrCR,OAAOC,eAAewO,EAAKjO,UAAW,cAAe,CAAEN,MAAOuO,EAAM2Q,UAAU,EAAMjf,cAAc,IAC3FsO,CACT,CAGiE4Q,GC5BjD,SAAAC,GAAwBC,EACAhU,EACAiU,EACAC,EACAvV,EACAqT,GAUtC,MAAMxY,EAAS+C,EAAsCyX,GAC/CrF,EAASpB,GAAsCvN,GAErDgU,EAAO9V,YAAa,EAEpB,IAAIiW,GAAe,EAGfC,EAAe5e,OAA0BU,GAE7C,OAAOZ,GAAW,CAACG,EAASL,KAC1B,IAAIyX,EACJ,QAAe3W,IAAX8b,EAAsB,CAuBxB,GAtBAnF,EAAiB,KACf,MAAM/H,OAA0B5O,IAAlB8b,EAAOrc,OAAuBqc,EAAOrc,OAAS,IAAI4d,GAAa,UAAW,cAClFc,EAAsC,GACvCH,GACHG,EAAQrc,MAAK,IACS,aAAhBgI,EAAKpG,OACAuT,GAAoBnN,EAAM8E,GAE5BtP,OAAoBU,KAG1ByI,GACH0V,EAAQrc,MAAK,IACW,aAAlBgc,EAAOpa,OACFO,GAAqB6Z,EAAQlP,GAE/BtP,OAAoBU,KAG/Boe,GAAmB,IAAMvf,QAAQwf,IAAIF,EAAQG,KAAIC,GAAUA,SAAY,EAAM3P,EAAM,EAGjFkN,EAAO0C,QAET,YADA7H,IAIFmF,EAAO2C,iBAAiB,QAAS9H,EAClC,CA0GD,IAA2BpT,EAAyC5D,EAAwB4e,EAhC5F,GA9BAG,EAAmBZ,EAAQxa,EAAOiB,gBAAgB0U,IAC3C+E,EAGHW,GAAS,EAAM1F,GAFfmF,GAAmB,IAAMnH,GAAoBnN,EAAMmP,KAAc,EAAMA,GAIlE,QAITyF,EAAmB5U,EAAM2O,EAAOlU,gBAAgB0U,IACzCxQ,EAGHkW,GAAS,EAAM1F,GAFfmF,GAAmB,IAAMna,GAAqB6Z,EAAQ7E,KAAc,EAAMA,GAIrE,QA8CkB1V,EA1CTua,EA0CkDne,EA1C1C2D,EAAOiB,eA0C2Dga,EA1C3C,KAC1CR,EAGHY,IAFAP,GAAmB,IH0qB3B,SAA8D3F,GAC5D,MAAMlV,EAASkV,EAAOiB,qBAIhBnL,EAAQhL,EAAOG,OACrB,OAAIwT,GAAoC3T,IAAqB,WAAVgL,EAC1CjP,OAAoBU,GAGf,YAAVuO,EACK/O,EAAoB+D,EAAOQ,cAK7BwW,GAAiC9B,EAC1C,CG3rBiCmG,CAAqDnG,KAIzE,MAqCe,WAAlBlV,EAAOG,OACT6a,IAEAte,EAAgBN,EAAS4e,GApCzBrH,GAAoCpN,IAAyB,WAAhBA,EAAKpG,OAAqB,CACzE,MAAMmb,EAAa,IAAI7d,UAAU,+EAE5ByH,EAGHkW,GAAS,EAAME,GAFfT,GAAmB,IAAMna,GAAqB6Z,EAAQe,KAAa,EAAMA,EAI5E,CAID,SAASC,IAGP,MAAMC,EAAkBb,EACxB,OAAOxe,EACLwe,GACA,IAAMa,IAAoBb,EAAeY,SAA0B9e,GAEtE,CAED,SAAS0e,EAAmBnb,EACA5D,EACA4e,GACJ,YAAlBhb,EAAOG,OACT6a,EAAOhb,EAAOQ,cAEd7D,EAAcP,EAAS4e,EAE1B,CAUD,SAASH,EAAmBG,EAAgCS,EAA2BC,GAYrF,SAASC,IAMP,OALAnf,EACEwe,KACA,IAAMY,EAASH,EAAiBC,KAChCG,GAAYD,GAAS,EAAMC,KAEtB,IACR,CAlBGnB,IAGJA,GAAe,EAEK,aAAhBnU,EAAKpG,QAA0BwT,GAAoCpN,GAGrEoV,IAFAjf,EAAgB6e,IAAyBI,GAa5C,CAED,SAASP,EAASU,EAAmBzQ,GAC/BqP,IAGJA,GAAe,EAEK,aAAhBnU,EAAKpG,QAA0BwT,GAAoCpN,GAGrEqV,EAASE,EAASzQ,GAFlB3O,EAAgB6e,KAAyB,IAAMK,EAASE,EAASzQ,KAIpE,CAED,SAASuQ,EAASE,EAAmBzQ,GAanC,OAZA4L,GAAmC/B,GACnCvU,EAAmCZ,QAEpBtD,IAAX8b,GACFA,EAAOwD,oBAAoB,QAAS3I,GAElC0I,EACFngB,EAAO0P,GAEPrP,OAAQS,GAGH,IACR,CA/EDM,EA9ESlB,GAAiB,CAACmgB,EAAaC,MACpC,SAAS3W,EAAKjC,GACRA,EACF2Y,IAIA7f,EASFue,EACK3e,GAAoB,GAGtBI,EAAmB+Y,EAAO4B,eAAe,IACvCjb,GAAoB,CAACqgB,EAAaC,KACvCjY,EACEnE,EACA,CACEwD,YAAaH,IACXuX,EAAexe,EAAmB+a,GAAiChC,EAAQ9R,QAAQ3G,EAAWhC,GAC9FyhB,GAAY,EAAM,EAEpB5Y,YAAa,IAAM4Y,GAAY,GAC/B/X,YAAagY,GAEhB,MAzBgC7W,EAAM2W,EAExC,CAED3W,EAAK,EAAM,IAkJd,GAEL,OCpOa8W,gCAwBX,WAAAte,GACE,MAAM,IAAIL,UAAU,sBACrB,CAMD,eAAImN,GACF,IAAKyR,GAAkCte,MACrC,MAAMua,GAAqC,eAG7C,OAAOgE,GAA8Cve,KACtD,CAMD,KAAA+M,GACE,IAAKuR,GAAkCte,MACrC,MAAMua,GAAqC,SAG7C,IAAKiE,GAAiDxe,MACpD,MAAM,IAAIN,UAAU,mDAGtB+e,GAAqCze,KACtC,CAMD,OAAAoN,CAAQ/H,OAAW3G,GACjB,IAAK4f,GAAkCte,MACrC,MAAMua,GAAqC,WAG7C,IAAKiE,GAAiDxe,MACpD,MAAM,IAAIN,UAAU,qDAGtB,OAAOgf,GAAuC1e,KAAMqF,EACrD,CAKD,KAAAiI,CAAMjH,OAAS3H,GACb,IAAK4f,GAAkCte,MACrC,MAAMua,GAAqC,SAG7CoE,GAAqC3e,KAAMqG,EAC5C,CAGD,CAACzE,GAAazD,GACZqN,GAAWxL,MACX,MAAM4H,EAAS5H,KAAKyN,iBAAiBtP,GAErC,OADAygB,GAA+C5e,MACxC4H,CACR,CAGD,CAAC/F,GAAWqD,GACV,MAAMjD,EAASjC,KAAK6e,0BAEpB,GAAI7e,KAAKkL,OAAO3K,OAAS,EAAG,CAC1B,MAAM8E,EAAQ0F,GAAa/K,MAEvBA,KAAKgN,iBAA0C,IAAvBhN,KAAKkL,OAAO3K,QACtCqe,GAA+C5e,MAC/C6Q,GAAoB5O,IAEpB6c,GAAgD9e,MAGlDkF,EAAYM,YAAYH,EACzB,MACCJ,EAA6BhD,EAAQiD,GACrC4Z,GAAgD9e,KAEnD,CAGD,CAAC8B,KAEA,EAqBH,SAASwc,GAA2C1hB,GAClD,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,8BAItCA,aAAayhB,gCACtB,CAEA,SAASS,GAAgDrQ,GAEvD,IADmBsQ,GAA8CtQ,GAE/D,OAGF,GAAIA,EAAWM,SAEb,YADAN,EAAWO,YAAa,GAM1BP,EAAWM,UAAW,EAGtBtQ,EADoBgQ,EAAWQ,kBAG7B,KACER,EAAWM,UAAW,EAElBN,EAAWO,aACbP,EAAWO,YAAa,EACxB8P,GAAgDrQ,IAG3C,QAETpI,IACEsY,GAAqClQ,EAAYpI,GAC1C,OAGb,CAEA,SAAS0Y,GAA8CtQ,GACrD,MAAMxM,EAASwM,EAAWoQ,0BAE1B,IAAKL,GAAiD/P,GACpD,OAAO,EAGT,IAAKA,EAAWE,SACd,OAAO,EAGT,GAAI/I,GAAuB3D,IAAWwD,EAAiCxD,GAAU,EAC/E,OAAO,EAKT,OAFoBsc,GAA8C9P,GAE/C,CAKrB,CAEA,SAASmQ,GAA+CnQ,GACtDA,EAAWQ,oBAAiBvQ,EAC5B+P,EAAWhB,sBAAmB/O,EAC9B+P,EAAWmL,4BAAyBlb,CACtC,CAIM,SAAU+f,GAAqChQ,GACnD,IAAK+P,GAAiD/P,GACpD,OAGF,MAAMxM,EAASwM,EAAWoQ,0BAE1BpQ,EAAWzB,iBAAkB,EAEI,IAA7ByB,EAAWvD,OAAO3K,SACpBqe,GAA+CnQ,GAC/CoC,GAAoB5O,GAExB,CAEgB,SAAAyc,GACdjQ,EACApJ,GAEA,IAAKmZ,GAAiD/P,GACpD,OAGF,MAAMxM,EAASwM,EAAWoQ,0BAE1B,GAAIjZ,GAAuB3D,IAAWwD,EAAiCxD,GAAU,EAC/EmD,EAAiCnD,EAAQoD,GAAO,OAC3C,CACL,IAAIsU,EACJ,IACEA,EAAYlL,EAAWmL,uBAAuBvU,EAC/C,CAAC,MAAOwU,GAEP,MADA8E,GAAqClQ,EAAYoL,GAC3CA,CACP,CAED,IACExO,GAAqBoD,EAAYpJ,EAAOsU,EACzC,CAAC,MAAOM,GAEP,MADA0E,GAAqClQ,EAAYwL,GAC3CA,CACP,CACF,CAED6E,GAAgDrQ,EAClD,CAEgB,SAAAkQ,GAAqClQ,EAAkDpI,GACrG,MAAMpE,EAASwM,EAAWoQ,0BAEJ,aAAlB5c,EAAOG,SAIXoJ,GAAWiD,GAEXmQ,GAA+CnQ,GAC/CmD,GAAoB3P,EAAQoE,GAC9B,CAEM,SAAUkY,GACd9P,GAEA,MAAMxB,EAAQwB,EAAWoQ,0BAA0Bzc,OAEnD,MAAc,YAAV6K,EACK,KAEK,WAAVA,EACK,EAGFwB,EAAWwD,aAAexD,EAAWtD,eAC9C,CAaM,SAAUqT,GACd/P,GAEA,MAAMxB,EAAQwB,EAAWoQ,0BAA0Bzc,OAEnD,OAAKqM,EAAWzB,iBAA6B,aAAVC,CAKrC,CAEgB,SAAA+R,GAAwC/c,EACAwM,EACA2D,EACAC,EACAC,EACAC,EACA0C,GAGtDxG,EAAWoQ,0BAA4B5c,EAEvCwM,EAAWvD,YAASxM,EACpB+P,EAAWtD,qBAAkBzM,EAC7B8M,GAAWiD,GAEXA,EAAWE,UAAW,EACtBF,EAAWzB,iBAAkB,EAC7ByB,EAAWO,YAAa,EACxBP,EAAWM,UAAW,EAEtBN,EAAWmL,uBAAyB3E,EACpCxG,EAAWwD,aAAeM,EAE1B9D,EAAWQ,eAAiBoD,EAC5B5D,EAAWhB,iBAAmB6E,EAE9BrQ,EAAOc,0BAA4B0L,EAGnChQ,EACET,EAFkBoU,MAGlB,KACE3D,EAAWE,UAAW,EAKtBmQ,GAAgDrQ,GACzC,QAET+D,IACEmM,GAAqClQ,EAAY+D,GAC1C,OAGb,CAqCA,SAAS+H,GAAqCvd,GAC5C,OAAO,IAAI0C,UACT,6CAA6C1C,0DACjD,CCxXgB,SAAAiiB,GAAqBhd,EACAid,GAGnC,OAAIxS,GAA+BzK,EAAOc,2BAkItC,SAAgCd,GAIpC,IAMIkd,EACAC,EACAC,EACAC,EAEAC,EAXAvd,EAAsD+C,EAAmC9C,GACzFud,GAAU,EACVC,GAAsB,EACtBC,GAAsB,EACtBC,GAAY,EACZC,GAAY,EAOhB,MAAMC,EAAgB/hB,GAAiBG,IACrCshB,EAAuBthB,CAAO,IAGhC,SAAS6hB,EAAmBC,GAC1BnhB,EAAcmhB,EAAW9c,gBAAgBuP,IACnCuN,IAAe/d,IAGnBuL,GAAkC8R,EAAQtc,0BAA2ByP,GACrEjF,GAAkC+R,EAAQvc,0BAA2ByP,GAChEmN,GAAcC,GACjBL,OAAqB7gB,IALd,OASZ,CAED,SAASshB,IACHnN,GAA2B7Q,KAE7BY,EAAmCZ,GAEnCA,EAAS+C,EAAmC9C,GAC5C6d,EAAmB9d,IA8DrBmE,EAAgCnE,EA3DwB,CACtDwD,YAAaH,IAIXlG,GAAe,KACbsgB,GAAsB,EACtBC,GAAsB,EAEtB,MAAMO,EAAS5a,EACf,IAAI6a,EAAS7a,EACb,IAAKsa,IAAcC,EACjB,IACEM,EAASrV,GAAkBxF,EAC5B,CAAC,MAAOuK,GAIP,OAHArC,GAAkC8R,EAAQtc,0BAA2B6M,GACrErC,GAAkC+R,EAAQvc,0BAA2B6M,QACrE2P,EAAqB5c,GAAqBV,EAAQ2N,GAEnD,CAGE+P,GACHtS,GAAoCgS,EAAQtc,0BAA2Bkd,GAEpEL,GACHvS,GAAoCiS,EAAQvc,0BAA2Bmd,GAGzEV,GAAU,EACNC,EACFU,IACST,GACTU,GACD,GACD,EAEJ7a,YAAa,KACXia,GAAU,EACLG,GACHxS,GAAkCkS,EAAQtc,2BAEvC6c,GACHzS,GAAkCmS,EAAQvc,2BAExCsc,EAAQtc,0BAA0BuL,kBAAkB/N,OAAS,GAC/D6L,GAAoCiT,EAAQtc,0BAA2B,GAErEuc,EAAQvc,0BAA0BuL,kBAAkB/N,OAAS,GAC/D6L,GAAoCkT,EAAQvc,0BAA2B,GAEpE4c,GAAcC,GACjBL,OAAqB7gB,EACtB,EAEH0H,YAAa,KACXoZ,GAAU,CAAK,GAIpB,CAED,SAASa,EAAmBxU,EAAkCyU,GACxD3a,EAAqD3D,KAEvDY,EAAmCZ,GAEnCA,EAAS2Q,GAAgC1Q,GACzC6d,EAAmB9d,IAGrB,MAAMue,EAAaD,EAAahB,EAAUD,EACpCmB,EAAcF,EAAajB,EAAUC,EAwE3CnM,GAA6BnR,EAAQ6J,EAAM,EAtE0B,CACnErG,YAAaH,IAIXlG,GAAe,KACbsgB,GAAsB,EACtBC,GAAsB,EAEtB,MAAMe,EAAeH,EAAaV,EAAYD,EAG9C,GAFsBW,EAAaX,EAAYC,EAgBnCa,GACVlU,GAA+CgU,EAAWxd,0BAA2BsC,OAfnE,CAClB,IAAIsK,EACJ,IACEA,EAAc9E,GAAkBxF,EACjC,CAAC,MAAOuK,GAIP,OAHArC,GAAkCgT,EAAWxd,0BAA2B6M,GACxErC,GAAkCiT,EAAYzd,0BAA2B6M,QACzE2P,EAAqB5c,GAAqBV,EAAQ2N,GAEnD,CACI6Q,GACHlU,GAA+CgU,EAAWxd,0BAA2BsC,GAEvFgI,GAAoCmT,EAAYzd,0BAA2B4M,EAC5E,CAID6P,GAAU,EACNC,EACFU,IACST,GACTU,GACD,GACD,EAEJ7a,YAAaF,IACXma,GAAU,EAEV,MAAMiB,EAAeH,EAAaV,EAAYD,EACxCe,EAAgBJ,EAAaX,EAAYC,EAE1Ca,GACHtT,GAAkCoT,EAAWxd,2BAE1C2d,GACHvT,GAAkCqT,EAAYzd,gCAGlCrE,IAAV2G,IAGGob,GACHlU,GAA+CgU,EAAWxd,0BAA2BsC,IAElFqb,GAAiBF,EAAYzd,0BAA0BuL,kBAAkB/N,OAAS,GACrF6L,GAAoCoU,EAAYzd,0BAA2B,IAI1E0d,GAAiBC,GACpBnB,OAAqB7gB,EACtB,EAEH0H,YAAa,KACXoZ,GAAU,CAAK,GAIpB,CAED,SAASW,IACP,GAAIX,EAEF,OADAC,GAAsB,EACfzhB,OAAoBU,GAG7B8gB,GAAU,EAEV,MAAM/S,EAAcG,GAA2CyS,EAAQtc,2BAOvE,OANoB,OAAhB0J,EACFuT,IAEAK,EAAmB5T,EAAYT,OAAQ,GAGlChO,OAAoBU,EAC5B,CAED,SAAS0hB,IACP,GAAIZ,EAEF,OADAE,GAAsB,EACf1hB,OAAoBU,GAG7B8gB,GAAU,EAEV,MAAM/S,EAAcG,GAA2C0S,EAAQvc,2BAOvE,OANoB,OAAhB0J,EACFuT,IAEAK,EAAmB5T,EAAYT,OAAQ,GAGlChO,OAAoBU,EAC5B,CAED,SAASiiB,EAAiBxiB,GAGxB,GAFAwhB,GAAY,EACZR,EAAUhhB,EACNyhB,EAAW,CACb,MAAMgB,EAAkBvY,GAAoB,CAAC8W,EAASC,IAChDyB,EAAele,GAAqBV,EAAQ2e,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASiB,EAAiB3iB,GAGxB,GAFAyhB,GAAY,EACZR,EAAUjhB,EACNwhB,EAAW,CACb,MAAMiB,EAAkBvY,GAAoB,CAAC8W,EAASC,IAChDyB,EAAele,GAAqBV,EAAQ2e,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASzN,IAER,CAOD,OALAiN,EAAU0B,GAAyB3O,EAAgB+N,EAAgBQ,GACnErB,EAAUyB,GAAyB3O,EAAgBgO,EAAgBU,GAEnEhB,EAAmB9d,GAEZ,CAACqd,EAASC,EACnB,CAnYW0B,CAAsB/e,GAMjB,SACdA,EACAid,GAKA,MAAMld,EAAS+C,EAAsC9C,GAErD,IAIIkd,EACAC,EACAC,EACAC,EAEAC,EATAC,GAAU,EACVyB,GAAY,EACZtB,GAAY,EACZC,GAAY,EAOhB,MAAMC,EAAgB/hB,GAAsBG,IAC1CshB,EAAuBthB,CAAO,IAGhC,SAASoU,IACP,GAAImN,EAEF,OADAyB,GAAY,EACLjjB,OAAoBU,GAG7B8gB,GAAU,EAkDV,OAFArZ,EAAgCnE,EA9CI,CAClCwD,YAAaH,IAIXlG,GAAe,KACb8hB,GAAY,EACZ,MAAMhB,EAAS5a,EACT6a,EAAS7a,EAQVsa,GACHjB,GAAuCW,EAAQtc,0BAA2Bkd,GAEvEL,GACHlB,GAAuCY,EAAQvc,0BAA2Bmd,GAG5EV,GAAU,EACNyB,GACF5O,GACD,GACD,EAEJ9M,YAAa,KACXia,GAAU,EACLG,GACHlB,GAAqCY,EAAQtc,2BAE1C6c,GACHnB,GAAqCa,EAAQvc,2BAG1C4c,GAAcC,GACjBL,OAAqB7gB,EACtB,EAEH0H,YAAa,KACXoZ,GAAU,CAAK,IAKZxhB,OAAoBU,EAC5B,CAED,SAASiiB,EAAiBxiB,GAGxB,GAFAwhB,GAAY,EACZR,EAAUhhB,EACNyhB,EAAW,CACb,MAAMgB,EAAkBvY,GAAoB,CAAC8W,EAASC,IAChDyB,EAAele,GAAqBV,EAAQ2e,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASiB,EAAiB3iB,GAGxB,GAFAyhB,GAAY,EACZR,EAAUjhB,EACNwhB,EAAW,CACb,MAAMiB,EAAkBvY,GAAoB,CAAC8W,EAASC,IAChDyB,EAAele,GAAqBV,EAAQ2e,GAClDrB,EAAqBsB,EACtB,CACD,OAAOhB,CACR,CAED,SAASzN,IAER,CAcD,OAZAiN,EAAU6B,GAAqB9O,EAAgBC,EAAesO,GAC9DrB,EAAU4B,GAAqB9O,EAAgBC,EAAeyO,GAE9DliB,EAAcoD,EAAOiB,gBAAiBuP,IACpCmM,GAAqCU,EAAQtc,0BAA2ByP,GACxEmM,GAAqCW,EAAQvc,0BAA2ByP,GACnEmN,GAAcC,GACjBL,OAAqB7gB,GAEhB,QAGF,CAAC2gB,EAASC,EACnB,CA5HS6B,CAAyBlf,EAClC,CCxCM,SAAUmf,GACd5E,GAEA,OCeO7f,EAD+BsF,EDdbua,SCe6D,IAA/Cva,EAAiCof,UDiDpE,SACJrf,GAEA,IAAIC,EAIJ,SAASoQ,IACP,IAAIiP,EACJ,IACEA,EAActf,EAAOgE,MACtB,CAAC,MAAOK,GACP,OAAOnI,EAAoBmI,EAC5B,CACD,OAAOxH,EAAqByiB,GAAaC,IACvC,IAAK5kB,EAAa4kB,GAChB,MAAM,IAAI7hB,UAAU,gFAEtB,GAAI6hB,EAAWjc,KACbmZ,GAAqCxc,EAAOc,+BACvC,CACL,MAAM5F,EAAQokB,EAAWpkB,MACzBuhB,GAAuCzc,EAAOc,0BAA2B5F,EAC1E,IAEJ,CAED,SAASmV,EAAgBnU,GACvB,IACE,OAAOH,EAAoBgE,EAAO+D,OAAO5H,GAC1C,CAAC,MAAOkI,GACP,OAAOnI,EAAoBmI,EAC5B,CACF,CAGD,OADApE,EAASif,GA9BcxkB,EA8BuB2V,EAAeC,EAAiB,GACvErQ,CACT,CApGWuf,CAAgChF,EAAO6E,aAK5C,SAAwCI,GAC5C,IAAIxf,EACJ,MAAMyf,EAAiBrX,GAAYoX,EAAe,SAIlD,SAASpP,IACP,IAAIsP,EACJ,IACEA,ElBoIA,SAA0BD,GAC9B,MAAM9Z,EAAStI,EAAYoiB,EAAe/W,WAAY+W,EAAehX,SAAU,IAC/E,IAAK/N,EAAaiL,GAChB,MAAM,IAAIlI,UAAU,oDAEtB,OAAOkI,CACT,CkB1ImBga,CAAaF,EAC3B,CAAC,MAAOrb,GACP,OAAOnI,EAAoBmI,EAC5B,CAED,OAAOxH,EADab,EAAoB2jB,IACCE,IACvC,IAAKllB,EAAaklB,GAChB,MAAM,IAAIniB,UAAU,kFAEtB,MAAM4F,ElBmIN,SACJuc,GAGA,OAAOC,QAAQD,EAAWvc,KAC5B,CkBxImByc,CAAiBF,GAC9B,GAAIvc,EACFmZ,GAAqCxc,EAAOc,+BACvC,CACL,MAAM5F,ElBsIR,SAA2B0kB,GAE/B,OAAOA,EAAW1kB,KACpB,CkBzIsB6kB,CAAcH,GAC5BnD,GAAuCzc,EAAOc,0BAA2B5F,EAC1E,IAEJ,CAED,SAASmV,EAAgBnU,GACvB,MAAMuM,EAAWgX,EAAehX,SAChC,IAAIuX,EASAC,EARJ,IACED,EAAetY,GAAUe,EAAU,SACpC,CAAC,MAAOrE,GACP,OAAOnI,EAAoBmI,EAC5B,CACD,QAAqB3H,IAAjBujB,EACF,OAAOjkB,OAAoBU,GAG7B,IACEwjB,EAAe5iB,EAAY2iB,EAAcvX,EAAU,CAACvM,GACrD,CAAC,MAAOkI,GACP,OAAOnI,EAAoBmI,EAC5B,CAED,OAAOxH,EADeb,EAAoBkkB,IACCL,IACzC,IAAKllB,EAAaklB,GAChB,MAAM,IAAIniB,UAAU,mFAEN,GAEnB,CAGD,OADAuC,EAASif,GAlDcxkB,EAkDuB2V,EAAeC,EAAiB,GACvErQ,CACT,CA3DSkgB,CAA2B3F,GCW9B,IAAkCva,CDVxC,CEyBA,SAASmgB,GACPrlB,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX5F,GAAgB0B,EAAY9C,EAAIgX,EAAU,CAAC5V,GACrD,CAEA,SAASkkB,GACPtlB,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX0K,GAA4C5O,EAAY9C,EAAIgX,EAAU,CAACtF,GACjF,CAEA,SAAS6T,GACPvlB,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX0K,GAA4CnP,EAAYvC,EAAIgX,EAAU,CAACtF,GACjF,CAEA,SAAS8T,GAA0B1N,EAAc9Q,GAE/C,GAAa,WADb8Q,EAAO,GAAGA,KAER,MAAM,IAAInV,UAAU,GAAGqE,MAAY8Q,8DAErC,OAAOA,CACT,CCzEgB,SAAA2N,GAAmBxP,EACAjP,GACjCF,EAAiBmP,EAASjP,GAC1B,MAAM2Y,EAAe1J,aAAA,EAAAA,EAAS0J,aACxBvV,EAAgB6L,aAAA,EAAAA,EAAS7L,cACzBsV,EAAezJ,aAAA,EAAAA,EAASyJ,aACxBjC,EAASxH,aAAA,EAAAA,EAASwH,OAIxB,YAHe9b,IAAX8b,GAWN,SAA2BA,EAAiBzW,GAC1C,IVUI,SAAwB5G,GAC5B,GAAqB,iBAAVA,GAAgC,OAAVA,EAC/B,OAAO,EAET,IACE,MAAiD,kBAAlCA,EAAsB+f,OACtC,CAAC,MAAA7f,GAEA,OAAO,CACR,CACH,CUpBOolB,CAAcjI,GACjB,MAAM,IAAI9a,UAAU,GAAGqE,2BAE3B,CAdI2e,CAAkBlI,EAAQ,GAAGzW,8BAExB,CACL2Y,aAAcoF,QAAQpF,GACtBvV,cAAe2a,QAAQ3a,GACvBsV,aAAcqF,QAAQrF,GACtBjC,SAEJ,CLuHAvd,OAAO2J,iBAAiByX,gCAAgC5gB,UAAW,CACjEsP,MAAO,CAAElG,YAAY,GACrBuG,QAAS,CAAEvG,YAAY,GACvByG,MAAO,CAAEzG,YAAY,GACrBgG,YAAa,CAAEhG,YAAY,KAE7B/J,EAAgBuhB,gCAAgC5gB,UAAUsP,MAAO,SACjEjQ,EAAgBuhB,gCAAgC5gB,UAAU2P,QAAS,WACnEtQ,EAAgBuhB,gCAAgC5gB,UAAU6P,MAAO,SAC/B,iBAAvB5L,OAAOoF,aAChB7J,OAAOC,eAAemhB,gCAAgC5gB,UAAWiE,OAAOoF,YAAa,CACnF3J,MAAO,kCACPC,cAAc,UMhELulB,eAcX,WAAA5iB,CAAY6iB,EAAqF,GACrFnO,EAAqD,CAAA,QACnC/V,IAAxBkkB,EACFA,EAAsB,KAEtB3e,EAAa2e,EAAqB,mBAGpC,MAAMpP,EAAWG,GAAuBc,EAAa,oBAC/CoO,EFjGM,SACdrG,EACAzY,GAEAF,EAAiB2Y,EAAQzY,GACzB,MAAMgQ,EAAWyI,EACX5O,EAAwBmG,aAAA,EAAAA,EAAUnG,sBAClC7H,EAASgO,aAAA,EAAAA,EAAUhO,OACnB+c,EAAO/O,aAAA,EAAAA,EAAU+O,KACjBlO,EAAQb,aAAA,EAAAA,EAAUa,MAClBC,EAAOd,aAAA,EAAAA,EAAUc,KACvB,MAAO,CACLjH,2BAAiDlP,IAA1BkP,OACrBlP,EACA+F,EACEmJ,EACA,GAAG7J,6CAEPgC,YAAmBrH,IAAXqH,OACNrH,EACA0jB,GAAsCrc,EAAQgO,EAAW,GAAGhQ,8BAC9D+e,UAAepkB,IAATokB,OACJpkB,EACA2jB,GAAoCS,EAAM/O,EAAW,GAAGhQ,4BAC1D6Q,WAAiBlW,IAAVkW,OACLlW,EACA4jB,GAAqC1N,EAAOb,EAAW,GAAGhQ,6BAC5D8Q,UAAenW,IAATmW,OAAqBnW,EAAY6jB,GAA0B1N,EAAM,GAAG9Q,4BAE9E,CEoE6Bgf,CAAqCH,EAAqB,mBAInF,GAFAI,GAAyBhjB,MAEK,UAA1B6iB,EAAiBhO,KAAkB,CACrC,QAAsBnW,IAAlB8U,EAASpI,KACX,MAAM,IAAIG,WAAW,wElBk9B3BtJ,EACAghB,EACA1Q,GAEA,MAAM9D,EAA2CxR,OAAO6U,OAAOtF,6BAA6B/O,WAE5F,IAAI2U,EACAC,EACAC,EAGFF,OADiC1T,IAA/BukB,EAAqBrO,MACN,IAAMqO,EAAqBrO,MAAOnG,GAElC,KAAe,EAGhC4D,OADgC3T,IAA9BukB,EAAqBH,KACP,IAAMG,EAAqBH,KAAMrU,GAEjC,IAAMzQ,OAAoBU,GAG1C4T,OADkC5T,IAAhCukB,EAAqBld,OACL5H,GAAU8kB,EAAqBld,OAAQ5H,GAEvC,IAAMH,OAAoBU,GAG9C,MAAMkP,EAAwBqV,EAAqBrV,sBACnD,GAA8B,IAA1BA,EACF,MAAM,IAAIlO,UAAU,gDAGtByS,GACElQ,EAAQwM,EAAY2D,EAAgBC,EAAeC,EAAiBC,EAAe3E,EAEvF,CkBj/BMsV,CACEljB,KACA6iB,EAHoBtP,GAAqBC,EAAU,GAMtD,KAAM,CAEL,MAAMyB,EAAgBvB,GAAqBF,IN+P3C,SACJvR,EACA4gB,EACAtQ,EACA0C,GAEA,MAAMxG,EAAiDxR,OAAO6U,OAAOuM,gCAAgC5gB,WAErG,IAAI2U,EACAC,EACAC,EAGFF,OAD6B1T,IAA3BmkB,EAAiBjO,MACF,IAAMiO,EAAiBjO,MAAOnG,GAE9B,KAAe,EAGhC4D,OAD4B3T,IAA1BmkB,EAAiBC,KACH,IAAMD,EAAiBC,KAAMrU,GAE7B,IAAMzQ,OAAoBU,GAG1C4T,OAD8B5T,IAA5BmkB,EAAiB9c,OACD5H,GAAU0kB,EAAiB9c,OAAQ5H,GAEnC,IAAMH,OAAoBU,GAG9CsgB,GACE/c,EAAQwM,EAAY2D,EAAgBC,EAAeC,EAAiBC,EAAe0C,EAEvF,CM5RMkO,CACEnjB,KACA6iB,EAHoBtP,GAAqBC,EAAU,GAKnDyB,EAEH,CACF,CAKD,UAAIO,GACF,IAAK1Q,GAAiB9E,MACpB,MAAMyV,GAA0B,UAGlC,OAAO7P,GAAuB5F,KAC/B,CAQD,MAAA+F,CAAO5H,OAAcO,GACnB,OAAKoG,GAAiB9E,MAIlB4F,GAAuB5F,MAClB9B,EAAoB,IAAIwB,UAAU,qDAGpCiD,GAAqB3C,KAAM7B,GAPzBD,EAAoBuX,GAA0B,UAQxD,CAqBD,SAAA4L,CACEtO,OAAgErU,GAEhE,IAAKoG,GAAiB9E,MACpB,MAAMyV,GAA0B,aAKlC,YAAqB/W,IhB3LT,SAAqBsU,EACAjP,GACnCF,EAAiBmP,EAASjP,GAC1B,MAAM2O,EAAOM,aAAA,EAAAA,EAASN,KACtB,MAAO,CACLA,UAAehU,IAATgU,OAAqBhU,EAAY+T,GAAgCC,EAAM,GAAG3O,4BAEpF,CgBkLoBqf,CAAqBrQ,EAAY,mBAErCL,KACH3N,EAAmC/E,MAIrC2S,GAAgC3S,KACxC,CAaD,WAAAqjB,CACEC,EACAvQ,EAAmD,IAEnD,IAAKjO,GAAiB9E,MACpB,MAAMyV,GAA0B,eAElCtR,EAAuBmf,EAAc,EAAG,eAExC,MAAMC,ECxNM,SACdtY,EACAlH,GAEAF,EAAiBoH,EAAMlH,GAEvB,MAAMyf,EAAWvY,aAAA,EAAAA,EAAMuY,SACvBnf,EAAoBmf,EAAU,WAAY,wBAC1C3e,EAAqB2e,EAAU,GAAGzf,gCAElC,MAAMsY,EAAWpR,aAAA,EAAAA,EAAMoR,SAIvB,OAHAhY,EAAoBgY,EAAU,WAAY,wBAC1ClI,GAAqBkI,EAAU,GAAGtY,gCAE3B,CAAEyf,WAAUnH,WACrB,CDyMsBoH,CAA4BH,EAAc,mBACtDtQ,EAAUwP,GAAmBzP,EAAY,oBAE/C,GAAInN,GAAuB5F,MACzB,MAAM,IAAIN,UAAU,kFAEtB,GAAIgW,GAAuB6N,EAAUlH,UACnC,MAAM,IAAI3c,UAAU,kFAStB,OAFAV,EAJgBud,GACdvc,KAAMujB,EAAUlH,SAAUrJ,EAAQyJ,aAAczJ,EAAQ0J,aAAc1J,EAAQ7L,cAAe6L,EAAQwH,SAKhG+I,EAAUC,QAClB,CAUD,MAAAE,CAAOC,EACA5Q,EAAmD,IACxD,IAAKjO,GAAiB9E,MACpB,OAAO9B,EAAoBuX,GAA0B,WAGvD,QAAoB/W,IAAhBilB,EACF,OAAOzlB,EAAoB,wCAE7B,IAAKkW,GAAiBuP,GACpB,OAAOzlB,EACL,IAAIwB,UAAU,8EAIlB,IAAIsT,EACJ,IACEA,EAAUwP,GAAmBzP,EAAY,mBAC1C,CAAC,MAAO1M,GACP,OAAOnI,EAAoBmI,EAC5B,CAED,OAAIT,GAAuB5F,MAClB9B,EACL,IAAIwB,UAAU,8EAGdgW,GAAuBiO,GAClBzlB,EACL,IAAIwB,UAAU,8EAIX6c,GACLvc,KAAM2jB,EAAa3Q,EAAQyJ,aAAczJ,EAAQ0J,aAAc1J,EAAQ7L,cAAe6L,EAAQwH,OAEjG,CAaD,GAAAoJ,GACE,IAAK9e,GAAiB9E,MACpB,MAAMyV,GAA0B,OAIlC,OAAOpN,GADU4W,GAAkBjf,MAEpC,CAcD,MAAA6jB,CAAO9Q,OAA+DrU,GACpE,IAAKoG,GAAiB9E,MACpB,MAAMyV,GAA0B,UAIlC,OxBnLY,SAAsCxT,EACAkF,GACpD,MAAMnF,EAAS+C,EAAsC9C,GAC/C6hB,EAAO,IAAI5c,GAAgClF,EAAQmF,GACnDuD,EAAmDzN,OAAO6U,OAAOjK,IAEvE,OADA6C,EAAS3C,mBAAqB+b,EACvBpZ,CACT,CwB4KWqZ,CAAsC/jB,KE/TjC,SAAuBgT,EACAjP,GACrCF,EAAiBmP,EAASjP,GAC1B,MAAMoD,EAAgB6L,aAAA,EAAAA,EAAS7L,cAC/B,MAAO,CAAEA,cAAe2a,QAAQ3a,GAClC,CFyToB6c,CAAuBjR,EAAY,mBACQ5L,cAC5D,CAOD,CAAC6C,IAAqBgJ,GAEpB,OAAOhT,KAAK6jB,OAAO7Q,EACpB,CAQD,WAAOiR,CAAQxC,GACb,OAAOL,GAAmBK,EAC3B,WAwDaP,GACd9O,EACAC,EACAC,EACAC,EAAgB,EAChB0C,EAAgD,KAAM,IAItD,MAAMhT,EAAmChF,OAAO6U,OAAO6Q,eAAellB,WACtEulB,GAAyB/gB,GAOzB,OAJA+c,GACE/c,EAFqDhF,OAAO6U,OAAOuM,gCAAgC5gB,WAE/E2U,EAAgBC,EAAeC,EAAiBC,EAAe0C,GAG9EhT,CACT,UAGgB8e,GACd3O,EACAC,EACAC,GAEA,MAAMrQ,EAA6BhF,OAAO6U,OAAO6Q,eAAellB,WAChEulB,GAAyB/gB,GAKzB,OAFAkQ,GAAkClQ,EADehF,OAAO6U,OAAOtF,6BAA6B/O,WACtC2U,EAAgBC,EAAeC,EAAiB,OAAG5T,GAElGuD,CACT,CAEA,SAAS+gB,GAAyB/gB,GAChCA,EAAOG,OAAS,WAChBH,EAAOE,aAAUzD,EACjBuD,EAAOQ,kBAAe/D,EACtBuD,EAAOyE,YAAa,CACtB,CAEM,SAAU5B,GAAiBlI,GAC/B,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,8BAItCA,aAAa+lB,eACtB,CAQM,SAAU/c,GAAuB3D,GAGrC,YAAuBvD,IAAnBuD,EAAOE,OAKb,CAIgB,SAAAQ,GAAwBV,EAA2B9D,GAGjE,GAFA8D,EAAOyE,YAAa,EAEE,WAAlBzE,EAAOG,OACT,OAAOpE,OAAoBU,GAE7B,GAAsB,YAAlBuD,EAAOG,OACT,OAAOlE,EAAoB+D,EAAOQ,cAGpCoO,GAAoB5O,GAEpB,MAAMD,EAASC,EAAOE,QACtB,QAAezD,IAAXsD,GAAwB6Q,GAA2B7Q,GAAS,CAC9D,MAAMsR,EAAmBtR,EAAOuN,kBAChCvN,EAAOuN,kBAAoB,IAAIzP,EAC/BwT,EAAiBnS,SAAQmO,IACvBA,EAAgB/J,iBAAY7G,EAAU,GAEzC,CAGD,OAAOG,EADqBoD,EAAOc,0BAA0BnB,GAAazD,GACzBzB,EACnD,CAEM,SAAUmU,GAAuB5O,GAGrCA,EAAOG,OAAS,SAEhB,MAAMJ,EAASC,EAAOE,QAEtB,QAAezD,IAAXsD,IAIJM,EAAkCN,GAE9B2D,EAAiC3D,IAAS,CAC5C,MAAM2E,EAAe3E,EAAOmD,cAC5BnD,EAAOmD,cAAgB,IAAIrF,EAC3B6G,EAAaxF,SAAQ+D,IACnBA,EAAYK,aAAa,GAE5B,CACH,CAEgB,SAAAqM,GAAuB3P,EAA2BoE,GAIhEpE,EAAOG,OAAS,UAChBH,EAAOQ,aAAe4D,EAEtB,MAAMrE,EAASC,EAAOE,aAEPzD,IAAXsD,IAIJa,EAAiCb,EAAQqE,GAErCV,EAAiC3D,GACnCuE,EAA6CvE,EAAQqE,GAGrD+M,GAA8CpR,EAAQqE,GAE1D,CAqBA,SAASoP,GAA0BzY,GACjC,OAAO,IAAI0C,UAAU,4BAA4B1C,yCACnD,CGljBgB,SAAAknB,GAA2BtQ,EACA7P,GACzCF,EAAiB+P,EAAM7P,GACvB,MAAMwO,EAAgBqB,aAAA,EAAAA,EAAMrB,cAE5B,OADAlO,EAAoBkO,EAAe,gBAAiB,uBAC7C,CACLA,cAAehO,EAA0BgO,GAE7C,CHkVAtV,OAAO2J,iBAAiB+b,eAAgB,CACtCsB,KAAM,CAAEpd,YAAY,KAEtB5J,OAAO2J,iBAAiB+b,eAAellB,UAAW,CAChDsI,OAAQ,CAAEc,YAAY,GACtBwa,UAAW,CAAExa,YAAY,GACzBwc,YAAa,CAAExc,YAAY,GAC3B6c,OAAQ,CAAE7c,YAAY,GACtB+c,IAAK,CAAE/c,YAAY,GACnBgd,OAAQ,CAAEhd,YAAY,GACtB2O,OAAQ,CAAE3O,YAAY,KAExB/J,EAAgB6lB,eAAesB,KAAM,QACrCnnB,EAAgB6lB,eAAellB,UAAUsI,OAAQ,UACjDjJ,EAAgB6lB,eAAellB,UAAU4jB,UAAW,aACpDvkB,EAAgB6lB,eAAellB,UAAU4lB,YAAa,eACtDvmB,EAAgB6lB,eAAellB,UAAUimB,OAAQ,UACjD5mB,EAAgB6lB,eAAellB,UAAUmmB,IAAK,OAC9C9mB,EAAgB6lB,eAAellB,UAAUomB,OAAQ,UACf,iBAAvBniB,OAAOoF,aAChB7J,OAAOC,eAAeylB,eAAellB,UAAWiE,OAAOoF,YAAa,CAClE3J,MAAO,iBACPC,cAAc,IAGlBH,OAAOC,eAAeylB,eAAellB,UAAWuM,GAAqB,CACnE7M,MAAOwlB,eAAellB,UAAUomB,OAChCxH,UAAU,EACVjf,cAAc,IInXhB,MAAM+mB,GAA0B9e,GACvBA,EAAMiE,WAEfxM,EAAgBqnB,GAAwB,QAO1B,MAAOC,0BAInB,WAAArkB,CAAYiT,GACV7O,EAAuB6O,EAAS,EAAG,6BACnCA,EAAUkR,GAA2BlR,EAAS,mBAC9ChT,KAAKqkB,wCAA0CrR,EAAQT,aACxD,CAKD,iBAAIA,GACF,IAAK+R,GAA4BtkB,MAC/B,MAAMukB,GAA8B,iBAEtC,OAAOvkB,KAAKqkB,uCACb,CAKD,QAAIjZ,GACF,IAAKkZ,GAA4BtkB,MAC/B,MAAMukB,GAA8B,QAEtC,OAAOJ,EACR,EAgBH,SAASI,GAA8BvnB,GACrC,OAAO,IAAI0C,UAAU,uCAAuC1C,oDAC9D,CAEM,SAAUsnB,GAA4B1nB,GAC1C,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,4CAItCA,aAAawnB,0BACtB,CA3BAnnB,OAAO2J,iBAAiBwd,0BAA0B3mB,UAAW,CAC3D8U,cAAe,CAAE1L,YAAY,GAC7BuE,KAAM,CAAEvE,YAAY,KAEY,iBAAvBnF,OAAOoF,aAChB7J,OAAOC,eAAeknB,0BAA0B3mB,UAAWiE,OAAOoF,YAAa,CAC7E3J,MAAO,4BACPC,cAAc,IChDlB,MAAMonB,GAAoB,IACjB,EAET1nB,EAAgB0nB,GAAmB,QAOrB,MAAOC,qBAInB,WAAA1kB,CAAYiT,GACV7O,EAAuB6O,EAAS,EAAG,wBACnCA,EAAUkR,GAA2BlR,EAAS,mBAC9ChT,KAAK0kB,mCAAqC1R,EAAQT,aACnD,CAKD,iBAAIA,GACF,IAAKoS,GAAuB3kB,MAC1B,MAAM4kB,GAAyB,iBAEjC,OAAO5kB,KAAK0kB,kCACb,CAMD,QAAItZ,GACF,IAAKuZ,GAAuB3kB,MAC1B,MAAM4kB,GAAyB,QAEjC,OAAOJ,EACR,EAgBH,SAASI,GAAyB5nB,GAChC,OAAO,IAAI0C,UAAU,kCAAkC1C,+CACzD,CAEM,SAAU2nB,GAAuB/nB,GACrC,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,uCAItCA,aAAa6nB,qBACtB,CCpCA,SAASI,GACP9nB,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX0K,GAAoD5O,EAAY9C,EAAIgX,EAAU,CAACtF,GACzF,CAEA,SAASqW,GACP/nB,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX0K,GAAoDnP,EAAYvC,EAAIgX,EAAU,CAACtF,GACzF,CAEA,SAASsW,GACPhoB,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACZ,CAACsB,EAAUoJ,IAAoD5O,EAAY9C,EAAIgX,EAAU,CAAC1O,EAAOoJ,GAC1G,CAEA,SAASuW,GACPjoB,EACAgX,EACAhQ,GAGA,OADAC,EAAejH,EAAIgH,GACX5F,GAAgB0B,EAAY9C,EAAIgX,EAAU,CAAC5V,GACrD,CDzBAlB,OAAO2J,iBAAiB6d,qBAAqBhnB,UAAW,CACtD8U,cAAe,CAAE1L,YAAY,GAC7BuE,KAAM,CAAEvE,YAAY,KAEY,iBAAvBnF,OAAOoF,aAChB7J,OAAOC,eAAeunB,qBAAqBhnB,UAAWiE,OAAOoF,YAAa,CACxE3J,MAAO,uBACPC,cAAc,UEXL6nB,gBAmBX,WAAAllB,CAAYmlB,EAAuD,CAAE,EACzDC,EAA6D,CAAE,EAC/DC,EAA6D,SAChD1mB,IAAnBwmB,IACFA,EAAiB,MAGnB,MAAMG,EAAmB1R,GAAuBwR,EAAqB,oBAC/DG,EAAmB3R,GAAuByR,EAAqB,mBAE/DG,ED7DM,SAAyBxR,EACAhQ,GACvCF,EAAiBkQ,EAAUhQ,GAC3B,MAAMgC,EAASgO,aAAA,EAAAA,EAAUhO,OACnByf,EAAQzR,aAAA,EAAAA,EAAUyR,MAClBC,EAAe1R,aAAA,EAAAA,EAAU0R,aACzB7Q,EAAQb,aAAA,EAAAA,EAAUa,MAClB2O,EAAYxP,aAAA,EAAAA,EAAUwP,UACtBmC,EAAe3R,aAAA,EAAAA,EAAU2R,aAC/B,MAAO,CACL3f,YAAmBrH,IAAXqH,OACNrH,EACAsmB,GAAiCjf,EAAQgO,EAAW,GAAGhQ,8BACzDyhB,WAAiB9mB,IAAV8mB,OACL9mB,EACAmmB,GAAgCW,EAAOzR,EAAW,GAAGhQ,6BACvD0hB,eACA7Q,WAAiBlW,IAAVkW,OACLlW,EACAomB,GAAgClQ,EAAOb,EAAW,GAAGhQ,6BACvDwf,eAAyB7kB,IAAd6kB,OACT7kB,EACAqmB,GAAoCxB,EAAWxP,EAAW,GAAGhQ,iCAC/D2hB,eAEJ,CCoCwBC,CAAmBT,EAAgB,mBACvD,QAAiCxmB,IAA7B6mB,EAAYE,aACd,MAAM,IAAIla,WAAW,kCAEvB,QAAiC7M,IAA7B6mB,EAAYG,aACd,MAAM,IAAIna,WAAW,kCAGvB,MAAMqa,EAAwBrS,GAAqB+R,EAAkB,GAC/DO,EAAwBnS,GAAqB4R,GAC7CQ,EAAwBvS,GAAqB8R,EAAkB,GAC/DU,EAAwBrS,GAAqB2R,GAEnD,IAAIW,GA2FR,SAAyC/jB,EACAgkB,EACAH,EACAC,EACAH,EACAC,GACvC,SAASzT,IACP,OAAO6T,CACR,CAED,SAAS9Q,EAAe9P,GACtB,OA6SJ,SAAwDpD,EAA+BoD,GAGrF,MAAMoJ,EAAaxM,EAAOikB,2BAE1B,GAAIjkB,EAAOuU,cAAe,CAGxB,OAAO3X,EAF2BoD,EAAOkkB,4BAEc,KACrD,MAAM9J,EAAWpa,EAAOmkB,UAExB,GAAc,aADA/J,EAASja,OAErB,MAAMia,EAAS5Z,aAGjB,OAAO4jB,GAAuD5X,EAAYpJ,EAAM,GAEnF,CAED,OAAOghB,GAAuD5X,EAAYpJ,EAC5E,CAjUWihB,CAAyCrkB,EAAQoD,EACzD,CAED,SAASgQ,EAAelX,GACtB,OA+TJ,SAAwD8D,EAA+B9D,GACrF,MAAMsQ,EAAaxM,EAAOikB,2BAC1B,QAAkCxnB,IAA9B+P,EAAW8X,eACb,OAAO9X,EAAW8X,eAIpB,MAAM/C,EAAWvhB,EAAOukB,UAIxB/X,EAAW8X,eAAiBzoB,GAAW,CAACG,EAASL,KAC/C6Q,EAAWgY,uBAAyBxoB,EACpCwQ,EAAWiY,sBAAwB9oB,CAAM,IAG3C,MAAMiiB,EAAgBpR,EAAWhB,iBAAiBtP,GAiBlD,OAhBAwoB,GAAgDlY,GAEhDhQ,EAAYohB,GAAe,KACD,YAApB2D,EAASphB,OACXwkB,GAAqCnY,EAAY+U,EAAS/gB,eAE1Dkc,GAAqC6E,EAASzgB,0BAA2B5E,GACzE0oB,GAAsCpY,IAEjC,QACN+D,IACDmM,GAAqC6E,EAASzgB,0BAA2ByP,GACzEoU,GAAqCnY,EAAY+D,GAC1C,QAGF/D,EAAW8X,cACpB,CAjWWO,CAAyC7kB,EAAQ9D,EACzD,CAED,SAASiX,IACP,OA+VJ,SAAwDnT,GACtD,MAAMwM,EAAaxM,EAAOikB,2BAC1B,QAAkCxnB,IAA9B+P,EAAW8X,eACb,OAAO9X,EAAW8X,eAIpB,MAAM/C,EAAWvhB,EAAOukB,UAIxB/X,EAAW8X,eAAiBzoB,GAAW,CAACG,EAASL,KAC/C6Q,EAAWgY,uBAAyBxoB,EACpCwQ,EAAWiY,sBAAwB9oB,CAAM,IAG3C,MAAMmpB,EAAetY,EAAWuY,kBAiBhC,OAhBAL,GAAgDlY,GAEhDhQ,EAAYsoB,GAAc,KACA,YAApBvD,EAASphB,OACXwkB,GAAqCnY,EAAY+U,EAAS/gB,eAE1Dgc,GAAqC+E,EAASzgB,2BAC9C8jB,GAAsCpY,IAEjC,QACN+D,IACDmM,GAAqC6E,EAASzgB,0BAA2ByP,GACzEoU,GAAqCnY,EAAY+D,GAC1C,QAGF/D,EAAW8X,cACpB,CAjYWU,CAAyChlB,EACjD,CAKD,SAASoQ,IACP,OA8XJ,SAAmDpQ,GASjD,OAHAilB,GAA+BjlB,GAAQ,GAGhCA,EAAOkkB,0BAChB,CAxYWgB,CAA0CllB,EAClD,CAED,SAASqQ,EAAgBnU,GACvB,OAsYJ,SAA2D8D,EAA+B9D,GACxF,MAAMsQ,EAAaxM,EAAOikB,2BAC1B,QAAkCxnB,IAA9B+P,EAAW8X,eACb,OAAO9X,EAAW8X,eAIpB,MAAMlK,EAAWpa,EAAOmkB,UAKxB3X,EAAW8X,eAAiBzoB,GAAW,CAACG,EAASL,KAC/C6Q,EAAWgY,uBAAyBxoB,EACpCwQ,EAAWiY,sBAAwB9oB,CAAM,IAG3C,MAAMiiB,EAAgBpR,EAAWhB,iBAAiBtP,GAmBlD,OAlBAwoB,GAAgDlY,GAEhDhQ,EAAYohB,GAAe,KACD,YAApBxD,EAASja,OACXwkB,GAAqCnY,EAAY4N,EAAS5Z,eAE1DqX,GAA6CuC,EAASnG,0BAA2B/X,GACjFipB,GAA4BnlB,GAC5B4kB,GAAsCpY,IAEjC,QACN+D,IACDsH,GAA6CuC,EAASnG,0BAA2B1D,GACjF4U,GAA4BnlB,GAC5B2kB,GAAqCnY,EAAY+D,GAC1C,QAGF/D,EAAW8X,cACpB,CA3aWc,CAA4CplB,EAAQ9D,EAC5D,CATD8D,EAAOmkB,UjBwBT,SAAiChU,EACA+C,EACAC,EACAC,EACA9C,EAAgB,EAChB0C,EAAgD,KAAM,IAGrF,MAAMhT,EAA4BhF,OAAO6U,OAAOyC,eAAe9W,WAO/D,OANAuX,GAAyB/S,GAIzBqT,GAAqCrT,EAFkBhF,OAAO6U,OAAOoD,gCAAgCzX,WAE5C2U,EAAgB+C,EAAgBC,EACpDC,EAAgB9C,EAAe0C,GAC7DhT,CACT,CiBxCqBqlB,CAAqBlV,EAAgB+C,EAAgBC,EAAgBC,EAChDyQ,EAAuBC,GAU/D9jB,EAAOukB,UAAYtF,GAAqB9O,EAAgBC,EAAeC,EAAiBsT,EAChDC,GAGxC5jB,EAAOuU,mBAAgB9X,EACvBuD,EAAOkkB,gCAA6BznB,EACpCuD,EAAOslB,wCAAqC7oB,EAC5CwoB,GAA+BjlB,GAAQ,GAEvCA,EAAOikB,gCAA6BxnB,CACtC,CAjII8oB,CACExnB,KALmBlC,GAAiBG,IACpC+nB,EAAuB/nB,CAAO,IAIV6nB,EAAuBC,EAAuBH,EAAuBC,GAgT/F,SAAoE5jB,EACAsjB,GAClE,MAAM9W,EAAkDxR,OAAO6U,OAAO2V,iCAAiChqB,WAEvG,IAAIiqB,EACAC,EACArV,EAGFoV,OAD4BhpB,IAA1B6mB,EAAYhC,UACOle,GAASkgB,EAAYhC,UAAWle,EAAOoJ,GAEvCpJ,IACnB,IAEE,OADAuiB,GAAwCnZ,EAAYpJ,GAC7CrH,OAAoBU,EAC5B,CAAC,MAAOmpB,GACP,OAAO3pB,EAAoB2pB,EAC5B,GAKHF,OADwBjpB,IAAtB6mB,EAAYC,MACG,IAAMD,EAAYC,MAAO/W,GAEzB,IAAMzQ,OAAoBU,GAI3C4T,OADyB5T,IAAvB6mB,EAAYxf,OACI5H,GAAUonB,EAAYxf,OAAQ5H,GAE9B,IAAMH,OAAoBU,IAlDhD,SAAqDuD,EACAwM,EACAiZ,EACAC,EACArV,GAInD7D,EAAWqZ,2BAA6B7lB,EACxCA,EAAOikB,2BAA6BzX,EAEpCA,EAAWsZ,oBAAsBL,EACjCjZ,EAAWuY,gBAAkBW,EAC7BlZ,EAAWhB,iBAAmB6E,EAE9B7D,EAAW8X,oBAAiB7nB,EAC5B+P,EAAWgY,4BAAyB/nB,EACpC+P,EAAWiY,2BAAwBhoB,CACrC,CAmCEspB,CAAsC/lB,EAAQwM,EAAYiZ,EAAoBC,EAAgBrV,EAChG,CAhVI2V,CAAqDjoB,KAAMulB,QAEjC7mB,IAAtB6mB,EAAY3Q,MACdoR,EAAqBT,EAAY3Q,MAAM5U,KAAKkmB,6BAE5CF,OAAqBtnB,EAExB,CAKD,YAAI8kB,GACF,IAAK0E,GAAkBloB,MACrB,MAAMyV,GAA0B,YAGlC,OAAOzV,KAAKwmB,SACb,CAKD,YAAInK,GACF,IAAK6L,GAAkBloB,MACrB,MAAMyV,GAA0B,YAGlC,OAAOzV,KAAKomB,SACb,EAmGH,SAAS8B,GAAkBtrB,GACzB,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,+BAItCA,aAAaqoB,gBACtB,CAGA,SAASkD,GAAqBlmB,EAAyBoE,GACrDsY,GAAqC1c,EAAOukB,UAAUzjB,0BAA2BsD,GACjF+hB,GAA4CnmB,EAAQoE,EACtD,CAEA,SAAS+hB,GAA4CnmB,EAAyBoE,GAC5EsgB,GAAgD1kB,EAAOikB,4BACvDpM,GAA6C7X,EAAOmkB,UAAUlQ,0BAA2B7P,GACzF+gB,GAA4BnlB,EAC9B,CAEA,SAASmlB,GAA4BnlB,GAC/BA,EAAOuU,eAIT0Q,GAA+BjlB,GAAQ,EAE3C,CAEA,SAASilB,GAA+BjlB,EAAyBgW,QAIrBvZ,IAAtCuD,EAAOkkB,4BACTlkB,EAAOslB,qCAGTtlB,EAAOkkB,2BAA6BroB,GAAWG,IAC7CgE,EAAOslB,mCAAqCtpB,CAAO,IAGrDgE,EAAOuU,cAAgByB,CACzB,CA9IAhb,OAAO2J,iBAAiBqe,gBAAgBxnB,UAAW,CACjD+lB,SAAU,CAAE3c,YAAY,GACxBwV,SAAU,CAAExV,YAAY,KAEQ,iBAAvBnF,OAAOoF,aAChB7J,OAAOC,eAAe+nB,gBAAgBxnB,UAAWiE,OAAOoF,YAAa,CACnE3J,MAAO,kBACPC,cAAc,UAgJLqqB,iCAgBX,WAAA1nB,GACE,MAAM,IAAIL,UAAU,sBACrB,CAKD,eAAImN,GACF,IAAKwb,GAAmCroB,MACtC,MAAMua,GAAqC,eAI7C,OAAOgE,GADoBve,KAAK8nB,2BAA2BtB,UAAUzjB,0BAEtE,CAMD,OAAAqK,CAAQ/H,OAAW3G,GACjB,IAAK2pB,GAAmCroB,MACtC,MAAMua,GAAqC,WAG7CqN,GAAwC5nB,KAAMqF,EAC/C,CAMD,KAAAiI,CAAMnP,OAAcO,GAClB,IAAK2pB,GAAmCroB,MACtC,MAAMua,GAAqC,SAyIjD,IAAkGlU,IAtIlDlI,EAuI9CgqB,GAvIwCnoB,KAuIR8nB,2BAA4BzhB,EAtI3D,CAMD,SAAAiiB,GACE,IAAKD,GAAmCroB,MACtC,MAAMua,GAAqC,cA0IjD,SAAsD9L,GACpD,MAAMxM,EAASwM,EAAWqZ,2BAG1BrJ,GAF2Bxc,EAAOukB,UAAUzjB,2BAI5C,MAAMuK,EAAQ,IAAI5N,UAAU,8BAC5B0oB,GAA4CnmB,EAAQqL,EACtD,CA/IIib,CAA0CvoB,KAC3C,EAqBH,SAASqoB,GAA4CzrB,GACnD,QAAKD,EAAaC,OAIbK,OAAOQ,UAAUgJ,eAAejI,KAAK5B,EAAG,+BAItCA,aAAa6qB,iCACtB,CA0DA,SAASd,GAAgDlY,GACvDA,EAAWsZ,yBAAsBrpB,EACjC+P,EAAWuY,qBAAkBtoB,EAC7B+P,EAAWhB,sBAAmB/O,CAChC,CAEA,SAASkpB,GAA2CnZ,EAAiDpJ,GACnG,MAAMpD,EAASwM,EAAWqZ,2BACpBU,EAAqBvmB,EAAOukB,UAAUzjB,0BAC5C,IAAKyb,GAAiDgK,GACpD,MAAM,IAAI9oB,UAAU,wDAMtB,IACEgf,GAAuC8J,EAAoBnjB,EAC5D,CAAC,MAAOgB,GAIP,MAFA+hB,GAA4CnmB,EAAQoE,GAE9CpE,EAAOukB,UAAU/jB,YACxB,CAED,MAAMwV,EbjJF,SACJxJ,GAEA,OAAIsQ,GAA8CtQ,EAKpD,CayIuBga,CAA+CD,GAChEvQ,IAAiBhW,EAAOuU,eAE1B0Q,GAA+BjlB,GAAQ,EAE3C,CAMA,SAASokB,GAAuD5X,EACApJ,GAE9D,OAAOxG,EADkB4P,EAAWsZ,oBAAoB1iB,QACV3G,GAAW8T,IAEvD,MADA2V,GAAqB1Z,EAAWqZ,2BAA4BtV,GACtDA,CAAC,GAEX,CAmKA,SAAS+H,GAAqCvd,GAC5C,OAAO,IAAI0C,UACT,8CAA8C1C,2DAClD,CAEM,SAAU6pB,GAAsCpY,QACV/P,IAAtC+P,EAAWgY,yBAIfhY,EAAWgY,yBACXhY,EAAWgY,4BAAyB/nB,EACpC+P,EAAWiY,2BAAwBhoB,EACrC,CAEgB,SAAAkoB,GAAqCnY,EAAmDtQ,QAC7DO,IAArC+P,EAAWiY,wBAIf1nB,EAA0ByP,EAAW8X,gBACrC9X,EAAWiY,sBAAsBvoB,GACjCsQ,EAAWgY,4BAAyB/nB,EACpC+P,EAAWiY,2BAAwBhoB,EACrC,CAIA,SAAS+W,GAA0BzY,GACjC,OAAO,IAAI0C,UACT,6BAA6B1C,0CACjC,CAnUAC,OAAO2J,iBAAiB6gB,iCAAiChqB,UAAW,CAClE2P,QAAS,CAAEvG,YAAY,GACvByG,MAAO,CAAEzG,YAAY,GACrByhB,UAAW,CAAEzhB,YAAY,GACzBgG,YAAa,CAAEhG,YAAY,KAE7B/J,EAAgB2qB,iCAAiChqB,UAAU2P,QAAS,WACpEtQ,EAAgB2qB,iCAAiChqB,UAAU6P,MAAO,SAClExQ,EAAgB2qB,iCAAiChqB,UAAU6qB,UAAW,aACpC,iBAAvB5mB,OAAOoF,aAChB7J,OAAOC,eAAeuqB,iCAAiChqB,UAAWiE,OAAOoF,YAAa,CACpF3J,MAAO,mCACPC,cAAc,IClVlB,MAAMsrB,GAAU,CACd/F,8BACAtE,gEACA7R,0DACAZ,oDACA5G,wDACA4N,kDAEA2B,8BACAW,gEACAc,wDAEAoO,oDACAK,0CAEAQ,gCACAwC,mEAIF,QAAuB,IAAZ9L,GACT,IAAK,MAAM9R,KAAQ6e,GACbzrB,OAAOQ,UAAUgJ,eAAejI,KAAKkqB,GAAS7e,IAChD5M,OAAOC,eAAeye,GAAS9R,EAAM,CACnC1M,MAAOurB,GAAQ7e,GACfwS,UAAU,EACVjf,cAAc"}