#!/usr/bin/env node

/**
 * Test the message parsing logic directly
 */

import fetch from 'node-fetch';

async function testMessageParsing() {
  console.log('Testing message parsing logic...');
  
  // Test cases for different message types
  const testCases = [
    {
      name: 'Valid JSON-RPC Request',
      message: {
        jsonrpc: "2.0",
        id: "1",
        method: "tools/list",
        params: {}
      },
      shouldSucceed: true
    },
    {
      name: 'Valid JSON-RPC Notification',
      message: {
        jsonrpc: "2.0",
        method: "notification",
        params: { type: "test" }
      },
      shouldSucceed: true
    },
    {
      name: 'Valid JSON-RPC Response',
      message: {
        jsonrpc: "2.0",
        id: "1",
        result: { tools: [] }
      },
      shouldSucceed: true
    },
    {
      name: 'Invalid - Missing jsonrpc',
      message: {
        id: "1",
        method: "tools/list"
      },
      shouldSucceed: false
    },
    {
      name: 'Invalid - Wrong jsonrpc version',
      message: {
        jsonrpc: "1.0",
        id: "1",
        method: "tools/list"
      },
      shouldSucceed: false
    },
    {
      name: 'Invalid - Null ID (the original problem)',
      message: {
        jsonrpc: "2.0",
        id: null,
        method: "tools/list"
      },
      shouldSucceed: false
    },
    {
      name: 'Invalid - Missing method and result/error',
      message: {
        jsonrpc: "2.0",
        id: "1"
      },
      shouldSucceed: false
    }
  ];
  
  // Create a fake session first by connecting to SSE briefly
  console.log('Creating test session...');
  
  try {
    // Try to get a session by making a quick SSE connection
    const sseResponse = await fetch('http://localhost:3000/sse', {
      headers: { 'Accept': 'text/event-stream' }
    });
    
    if (!sseResponse.ok) {
      console.error('Failed to connect to SSE endpoint');
      return;
    }
    
    // For testing, we'll use a fake session ID since we can't easily parse SSE in this simple test
    const fakeSessionId = 'test-session-123';
    
    console.log('Testing message parsing with fake session...\n');
    
    for (const testCase of testCases) {
      console.log(`🧪 Testing: ${testCase.name}`);
      
      try {
        const response = await fetch(`http://localhost:3000/api/messages?sessionId=${fakeSessionId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testCase.message)
        });
        
        const responseText = await response.text();
        const success = response.status < 400;
        
        console.log(`   Status: ${response.status}`);
        console.log(`   Response: ${responseText}`);
        
        if (testCase.shouldSucceed && success) {
          console.log('   ✅ PASS - Message accepted as expected');
        } else if (!testCase.shouldSucceed && !success) {
          console.log('   ✅ PASS - Message rejected as expected');
        } else if (testCase.shouldSucceed && !success) {
          console.log('   ❌ FAIL - Message should have been accepted');
        } else {
          console.log('   ❌ FAIL - Message should have been rejected');
        }
        
      } catch (error) {
        console.log(`   ❌ ERROR: ${error.message}`);
      }
      
      console.log('');
    }
    
  } catch (error) {
    console.error('Error during testing:', error);
  }
}

testMessageParsing().then(() => {
  console.log('Message parsing tests completed');
  process.exit(0);
}).catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
