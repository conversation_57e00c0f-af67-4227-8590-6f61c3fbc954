# Strety MCP Server Environment Variables
# Copy this file to .env and fill in your actual values

# ✅ WORKING CONFIGURATION EXAMPLE
# These are the environment variables for a fully functional setup

# OAuth2 Client Credentials (Required)
# Get these from your Strety OAuth2 application settings
OAUTH_CLIENT_ID_OAUTH2=your_client_id_here
OAUTH_CLIENT_SECRET_OAUTH2=your_client_secret_here

# OAuth2 Tokens (Required)
# Get these by running: npm run oauth2:simple
OAUTH_TOKEN_OAUTH2=your_access_token_here
OAUTH_REFRESH_TOKEN_OAUTH2=your_refresh_token_here

# Server Configuration (Optional)
PORT=3000
LOG_LEVEL=info

# OAuth2 Setup Instructions:
# 1. Get OAuth2 credentials from your Strety account
# 2. Configure redirect URI in Strety: https://oauth.pstmn.io/v1/callback
# 3. Run: npm run oauth2:simple
# 4. Follow the browser authorization flow
# 5. Tokens will be automatically acquired and tested

# Strety API Information:
# - Base URL: https://2.strety.com
# - Authorization URL: https://2.strety.com/api/v1/oauth/authorize
# - Token URL: https://2.strety.com/api/v1/oauth/token
# - Required Redirect URI: https://oauth.pstmn.io/v1/callback
# - Token Expiration: 2 hours (automatic refresh enabled)
# - Required Scopes: read, write

# ⚠️ IMPORTANT NOTES:
# - Strety ONLY supports OAuth2 (no API keys or personal access tokens)
# - HTTPS redirect URI is required (localhost won't work)
# - Access tokens expire every 2 hours
# - Refresh tokens enable automatic renewal
# - The MCP server handles token refresh automatically

# For Raycast MCP Configuration:
# You can include these environment variables directly in your MCP config
# instead of using this .env file. See README.md for examples.

# Working Example Values (replace with your own):
# OAUTH_CLIENT_ID_OAUTH2=_9kkNXWvkKRpUMnqEStosWbZx72ru52FilDeANulRCo
# OAUTH_CLIENT_SECRET_OAUTH2=FOG7i3rs5CLHENKKWncn5iMXvPtVo-rjSsgIsHhNnuo
# OAUTH_TOKEN_OAUTH2=RA-lBMdplpoGis8NWUELHGX0k9ccB_a27gIY1SRF_WA
# OAUTH_REFRESH_TOKEN_OAUTH2=phF-ItL7kVv-hWUBYKK6WsHW9aj8rEAjAbTB6w-B1zU
