#!/usr/bin/env node

/**
 * Manual OAuth2 Setup for Strety MCP Server
 * 
 * This script provides step-by-step instructions for manual OAuth2 setup
 * based on Strety's official documentation.
 */

import dotenv from 'dotenv';
import axios from 'axios';
import https from 'https';

dotenv.config();

const CLIENT_ID = process.env.OAUTH_CLIENT_ID_OAUTH2;
const CLIENT_SECRET = process.env.OAUTH_CLIENT_SECRET_OAUTH2;

async function manualOAuth2Setup() {
  console.log('🔐 Strety MCP Server - Manual OAuth2 Setup\n');
  
  if (!CLIENT_ID || !CLIENT_SECRET) {
    console.error('❌ Missing OAuth2 credentials!');
    console.error('Please set OAUTH_CLIENT_ID_OAUTH2 and OAUTH_CLIENT_SECRET_OAUTH2 in your .env file');
    process.exit(1);
  }
  
  console.log('✅ OAuth2 credentials found');
  console.log(`Client ID: ${CLIENT_ID}`);
  console.log(`Client Secret: ${CLIENT_SECRET.substring(0, 10)}...\n`);
  
  // Step 1: Authorization URL
  console.log('📋 Step 1: Get Authorization Code');
  console.log('═'.repeat(50));
  
  const authUrl = `https://2.strety.com/api/v1/oauth/authorize?response_type=code&client_id=${CLIENT_ID}&redirect_uri=http://localhost:3001/oauth/callback&scope=read%20write&state=mcp-setup-${Date.now()}`;
  
  console.log('1. Open this URL in your browser:');
  console.log('\n' + authUrl + '\n');
  
  console.log('2. After authorization, you\'ll be redirected to a URL like:');
  console.log('   http://localhost:3001/oauth/callback?code=AUTHORIZATION_CODE&state=mcp-setup-...');
  console.log('');
  console.log('3. Copy the AUTHORIZATION_CODE from the URL');
  console.log('   (The part after "code=" and before "&state=")');
  console.log('');
  
  // Step 2: Token Exchange Instructions
  console.log('📋 Step 2: Exchange Code for Tokens');
  console.log('═'.repeat(50));
  
  console.log('After getting the authorization code, run this curl command:');
  console.log('(Replace AUTHORIZATION_CODE with the actual code from step 1)');
  console.log('');
  
  const curlCommand = `curl -X POST https://2.strety.com/api/v1/oauth/token \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -d "grant_type=authorization_code&code=AUTHORIZATION_CODE&redirect_uri=http://localhost:3001/oauth/callback&client_id=${CLIENT_ID}&client_secret=${CLIENT_SECRET}"`;
  
  console.log(curlCommand);
  console.log('');
  
  // Step 3: Environment Setup
  console.log('📋 Step 3: Update .env File');
  console.log('═'.repeat(50));
  
  console.log('Add the tokens from the curl response to your .env file:');
  console.log('');
  console.log('# OAuth2 tokens (from authorization flow)');
  console.log('OAUTH_TOKEN_OAUTH2=your_access_token_here');
  console.log('OAUTH_REFRESH_TOKEN_OAUTH2=your_refresh_token_here');
  console.log('');
  
  // Interactive token exchange
  console.log('🤖 Interactive Token Exchange');
  console.log('═'.repeat(50));
  console.log('If you have the authorization code, I can exchange it for tokens now.');
  console.log('Enter the authorization code (or press Enter to skip):');
  
  process.stdin.setEncoding('utf8');
  process.stdin.on('readable', async () => {
    const chunk = process.stdin.read();
    if (chunk !== null) {
      const authCode = chunk.trim();
      if (authCode && authCode.length > 0) {
        await exchangeCodeForTokens(authCode);
      } else {
        console.log('\n✅ Manual setup instructions provided above.');
        console.log('After completing the steps, test with: npm start');
      }
      process.exit(0);
    }
  });
}

async function exchangeCodeForTokens(authCode) {
  console.log('\n🔄 Exchanging authorization code for tokens...');
  
  try {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'authorization_code');
    formData.append('code', authCode);
    formData.append('redirect_uri', 'http://localhost:3001/oauth/callback');
    formData.append('client_id', CLIENT_ID);
    formData.append('client_secret', CLIENT_SECRET);
    
    const response = await axios({
      method: 'POST',
      url: 'https://2.strety.com/api/v1/oauth/token',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      data: formData.toString(),
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
    
    const { access_token, refresh_token, expires_in, token_type } = response.data;
    
    if (access_token) {
      console.log('✅ Tokens acquired successfully!\n');
      
      console.log('📋 Add these to your .env file:');
      console.log('═'.repeat(40));
      console.log(`OAUTH_TOKEN_OAUTH2=${access_token}`);
      if (refresh_token) {
        console.log(`OAUTH_REFRESH_TOKEN_OAUTH2=${refresh_token}`);
      }
      console.log('');
      
      console.log('📊 Token Information:');
      console.log(`Token Type: ${token_type || 'Bearer'}`);
      console.log(`Expires In: ${expires_in || 'Unknown'} seconds (${Math.round((expires_in || 7200) / 3600)} hours)`);
      console.log(`Refresh Token: ${refresh_token ? 'Yes' : 'No'}`);
      
      if (refresh_token) {
        console.log('\n✅ Refresh token available - automatic token renewal enabled!');
      } else {
        console.log('\n⚠️  No refresh token - you may need to re-authorize when the token expires.');
      }
      
      console.log('\n🎉 Setup complete! Test with: npm start');
      
    } else {
      console.error('❌ No access token in response');
      console.error('Response:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Token exchange failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response:`, error.response.data);
    } else {
      console.error(error.message);
    }
    
    console.log('\n💡 Troubleshooting:');
    console.log('1. Make sure the authorization code is correct and not expired');
    console.log('2. Verify your client ID and secret are correct');
    console.log('3. Check that the redirect URI matches exactly: https://yourapp.com/callback');
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user');
  process.exit(0);
});

manualOAuth2Setup().catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
