#!/usr/bin/env node

/**
 * ✅ WORKING TEST - Test a specific tool call to verify API connectivity
 * This test verifies that the MCP server is working correctly with OAuth2 authentication
 */

import { spawn } from 'child_process';

function testToolCall() {
  console.log('Testing MCP server tool call...\n');
  
  const server = spawn('node', ['build/index.js'], {
    cwd: process.cwd(),
    stdio: ['pipe', 'pipe', 'inherit']
  });
  
  let responseBuffer = '';
  let initComplete = false;
  
  server.stdout.on('data', (data) => {
    responseBuffer += data.toString();
    
    // Look for complete JSON-RPC messages
    const lines = responseBuffer.split('\n');
    responseBuffer = lines.pop() || ''; // Keep incomplete line
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const message = JSON.parse(line);
          console.log('📨 Received:', JSON.stringify(message, null, 2));
          
          // After initialization, send tool call
          if (message.id === 1 && message.result && !initComplete) {
            initComplete = true;
            setTimeout(() => {
              console.log('\n📤 Sending tool call (listPeople)...');
              server.stdin.write(JSON.stringify({
                jsonrpc: "2.0",
                id: 3,
                method: "tools/call",
                params: {
                  name: "listPeople",
                  arguments: {}
                }
              }) + '\n');
            }, 500);
          }
          
          // Check tool call result
          if (message.id === 3) {
            if (message.result) {
              console.log('\n✅ SUCCESS: Tool call worked!');
              console.log('✅ API connectivity confirmed!');
              
              // Show first few characters of response
              const content = message.result.content?.[0]?.text;
              if (content) {
                console.log('✅ Response preview:', content.substring(0, 200) + '...');
              }
            } else if (message.error) {
              console.log('\n❌ Tool call failed:', message.error.message);
              if (message.error.message.includes('401') || message.error.message.includes('403')) {
                console.log('❌ Authentication issue - check your OAuth credentials');
              }
            }
            
            server.kill();
            process.exit(message.result ? 0 : 1);
          }
          
        } catch (e) {
          // Ignore non-JSON lines
        }
      }
    }
  });
  
  server.on('error', (error) => {
    console.error('❌ Server error:', error);
    process.exit(1);
  });
  
  // Send initialization
  setTimeout(() => {
    console.log('📤 Sending initialize...');
    server.stdin.write(JSON.stringify({
      jsonrpc: "2.0",
      id: 1,
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {},
        clientInfo: { name: "test-client", version: "1.0.0" }
      }
    }) + '\n');
  }, 500);
  
  // Timeout after 15 seconds
  setTimeout(() => {
    console.log('\n⏰ Test timeout - killing server');
    server.kill();
    process.exit(1);
  }, 15000);
}

testToolCall();
