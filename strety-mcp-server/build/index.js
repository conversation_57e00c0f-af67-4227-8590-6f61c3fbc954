#!/usr/bin/env node
/**
 * MCP Server generated from OpenAPI spec for strety-mcp-server v1.0.0
 * Generated on: 2025-08-01T19:09:30.033Z
 */
// Load environment variables from .env file
import dotenv from 'dotenv';
dotenv.config();
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { CallToolRequestSchema, ListToolsRequestSchema } from "@modelcontextprotocol/sdk/types.js";
import { setupWebServer } from "./web-server.js";
import { z, ZodError } from 'zod';
import { jsonSchemaToZod } from 'json-schema-to-zod';
import axios from 'axios';
import https from 'https';
/**
 * Server configuration
 */
export const SERVER_NAME = "strety-mcp-server";
export const SERVER_VERSION = "1.0.0";
export const API_BASE_URL = "https://2.strety.com";
/**
 * MCP Server instance
 */
const server = new Server({ name: SERVER_NAME, version: SERVER_VERSION }, { capabilities: { tools: {} } });
/**
 * Map of tool definitions by name
 */
const toolDefinitionMap = new Map([
    ["listGoals", {
            name: "listGoals",
            description: `Retrieves a paginated list of goals with optional filtering criteria.`,
            inputSchema: { "type": "object", "properties": { "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" }, "filter[ids][]": { "type": "array", "items": { "type": "string", "format": "uuid" }, "description": "List of resource ids" }, "filter[assignee_id]": { "type": "string", "format": "uuid", "description": "Filter by the person assigned to the resource" }, "filter[parent_id]": { "type": "string", "format": "uuid", "description": "Filter by parent" }, "include": { "type": "string", "enum": ["latest_check_ins"], "description": "Related resources to include in the response. Available options: latest_check_ins\n" }, "limit_check_ins": { "type": "number", "minimum": 1, "maximum": 5, "default": 1, "description": "Maximum number of recent check-ins to include in relationships and response when include=latest_check_ins is specified. Default is 1, maximum is 5.\n" } } },
            method: "get",
            pathTemplate: "/api/v1/goals",
            executionParameters: [{ "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }, { "name": "filter[ids][]", "in": "query" }, { "name": "filter[assignee_id]", "in": "query" }, { "name": "filter[parent_id]", "in": "query" }, { "name": "include", "in": "query" }, { "name": "limit_check_ins", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["getGoal", {
            name: "getGoal",
            description: `Retrieves a single goal by its UUID.`,
            inputSchema: { "type": "object", "properties": { "id": { "type": "string", "format": "uuid", "description": "UUID of the goal to retrieve" }, "include": { "type": "string", "enum": ["latest_check_ins"], "description": "Related resources to include in the response. Available options: latest_check_ins\n" }, "limit_check_ins": { "type": "number", "minimum": 1, "maximum": 5, "default": 1, "description": "Maximum number of recent check-ins to include in relationships and response when include=latest_check_ins is specified. Default is 1, maximum is 5.\n" } }, "required": ["id"] },
            method: "get",
            pathTemplate: "/api/v1/goals/{id}",
            executionParameters: [{ "name": "id", "in": "path" }, { "name": "include", "in": "query" }, { "name": "limit_check_ins", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listGoalCheckIns", {
            name: "listGoalCheckIns",
            description: `Retrieves a paginated list of check-ins for a specific goal, with optional filtering criteria`,
            inputSchema: { "type": "object", "properties": { "goal_id": { "type": "string", "format": "uuid", "description": "UUID of the goal to retrieve check-ins for" }, "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" } }, "required": ["goal_id"] },
            method: "get",
            pathTemplate: "/api/v1/goals/{goal_id}/check_ins",
            executionParameters: [{ "name": "goal_id", "in": "path" }, { "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listHeadlines", {
            name: "listHeadlines",
            description: `Retrieves a paginated list of headlines with optional filtering criteria.`,
            inputSchema: { "type": "object", "properties": { "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" }, "filter[ids][]": { "type": "array", "items": { "type": "string", "format": "uuid" }, "description": "List of resource ids" }, "filter[owner_id]": { "type": "string", "format": "uuid", "description": "Filter by the person responsible for the resource" } } },
            method: "get",
            pathTemplate: "/api/v1/headlines",
            executionParameters: [{ "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }, { "name": "filter[ids][]", "in": "query" }, { "name": "filter[owner_id]", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listIssues", {
            name: "listIssues",
            description: `Retrieves a paginated list of issues with optional filtering criteria.`,
            inputSchema: { "type": "object", "properties": { "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" }, "filter[ids][]": { "type": "array", "items": { "type": "string", "format": "uuid" }, "description": "List of resource ids" }, "filter[owner_id]": { "type": "string", "format": "uuid", "description": "Filter by the person responsible for the resource" }, "filter[issue_type]": { "type": "string", "enum": ["long_term", "short_term", "parking_lot"], "description": "Filter by issue type (long_term, short_term, parking_lot)" } } },
            method: "get",
            pathTemplate: "/api/v1/issues",
            executionParameters: [{ "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }, { "name": "filter[ids][]", "in": "query" }, { "name": "filter[owner_id]", "in": "query" }, { "name": "filter[issue_type]", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["getIssue", {
            name: "getIssue",
            description: `Retrieves a single issue by its UUID.`,
            inputSchema: { "type": "object", "properties": { "id": { "type": "string", "format": "uuid", "description": "UUID of the issue to retrieve" } }, "required": ["id"] },
            method: "get",
            pathTemplate: "/api/v1/issues/{id}",
            executionParameters: [{ "name": "id", "in": "path" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listMeetings", {
            name: "listMeetings",
            description: `Retrieves a paginated list of meetings with optional filtering criteria.`,
            inputSchema: { "type": "object", "properties": { "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" }, "filter[ids][]": { "type": "array", "items": { "type": "string", "format": "uuid" }, "description": "List of resource ids" } } },
            method: "get",
            pathTemplate: "/api/v1/meetings",
            executionParameters: [{ "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }, { "name": "filter[ids][]", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["getMeeting", {
            name: "getMeeting",
            description: `Retrieves a single meeting by its UUID.`,
            inputSchema: { "type": "object", "properties": { "id": { "type": "string", "format": "uuid", "description": "UUID of the meeting to retrieve" }, "include": { "type": "string", "enum": ["rankings"], "description": "Comma-separated list of related resources to include in the response\n" } }, "required": ["id"] },
            method: "get",
            pathTemplate: "/api/v1/meetings/{id}",
            executionParameters: [{ "name": "id", "in": "path" }, { "name": "include", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listMetrics", {
            name: "listMetrics",
            description: `Retrieves a paginated list of metrics, with optional filtering criteria. Metrics are read-only via the API and only manual metrics are available through this endpoint.
`,
            inputSchema: { "type": "object", "properties": { "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" }, "filter[ids][]": { "type": "array", "items": { "type": "string", "format": "uuid" }, "description": "List of resource ids" }, "filter[assignee_id]": { "type": "string", "format": "uuid", "description": "Filter by the person assigned to the resource" }, "filter[checkin_frequency]": { "type": "string", "enum": ["weekly", "monthly", "quarterly", "annual"], "description": "Filter metrics by check-in frequency. Accepted values: weekly, monthly, quarterly, annual.\n" }, "include": { "type": "string", "enum": ["latest_check_ins"], "description": "Related resources to include in the response. Available options: latest_check_ins\n" }, "limit_check_ins": { "type": "number", "minimum": 1, "maximum": 5, "default": 1, "description": "Maximum number of recent check-ins to include in relationships and response when include=latest_check_ins is specified. Default is 1, maximum is 5.\n" } } },
            method: "get",
            pathTemplate: "/api/v1/metrics",
            executionParameters: [{ "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }, { "name": "filter[ids][]", "in": "query" }, { "name": "filter[assignee_id]", "in": "query" }, { "name": "filter[checkin_frequency]", "in": "query" }, { "name": "include", "in": "query" }, { "name": "limit_check_ins", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["getMetric", {
            name: "getMetric",
            description: `Retrieves a single metric by its UUID.`,
            inputSchema: { "type": "object", "properties": { "id": { "type": "string", "format": "uuid", "description": "UUID of the metric to retrieve" }, "include": { "type": "string", "enum": ["latest_check_ins"], "description": "Related resources to include in the response. Available options: latest_check_ins\n" }, "limit_check_ins": { "type": "number", "minimum": 1, "maximum": 5, "default": 1, "description": "Maximum number of recent check-ins to include in relationships and response when include=latest_check_ins is specified. Default is 1, maximum is 5.\n" } }, "required": ["id"] },
            method: "get",
            pathTemplate: "/api/v1/metrics/{id}",
            executionParameters: [{ "name": "id", "in": "path" }, { "name": "include", "in": "query" }, { "name": "limit_check_ins", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listMetricCheckIns", {
            name: "listMetricCheckIns",
            description: `Retrieves a paginated list of check-ins for a specific metric, with optional filtering criteria`,
            inputSchema: { "type": "object", "properties": { "metric_id": { "type": "string", "format": "uuid", "description": "UUID of the metric to retrieve check-ins for" }, "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" } }, "required": ["metric_id"] },
            method: "get",
            pathTemplate: "/api/v1/metrics/{metric_id}/check_ins",
            executionParameters: [{ "name": "metric_id", "in": "path" }, { "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["createMetricCheckIn", {
            name: "createMetricCheckIn",
            description: `Creates a new check-in for a specific metric.`,
            inputSchema: { "type": "object", "properties": { "metric_id": { "type": "string", "format": "uuid", "description": "UUID of the metric to create a check-in for" }, "requestBody": { "type": "string", "description": "Request body (content type: application/vnd.api+json)" } }, "required": ["metric_id", "requestBody"] },
            method: "post",
            pathTemplate: "/api/v1/metrics/{metric_id}/check_ins",
            executionParameters: [{ "name": "metric_id", "in": "path" }],
            requestBodyContentType: "application/vnd.api+json",
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["getMetricCheckIn", {
            name: "getMetricCheckIn",
            description: `Retrieves a specific check-in for a metric by its UUID.`,
            inputSchema: { "type": "object", "properties": { "metric_id": { "type": "string", "format": "uuid", "description": "UUID of the metric" }, "id": { "type": "string", "format": "uuid", "description": "UUID of the check-in to retrieve" } }, "required": ["metric_id", "id"] },
            method: "get",
            pathTemplate: "/api/v1/metrics/{metric_id}/check_ins/{id}",
            executionParameters: [{ "name": "metric_id", "in": "path" }, { "name": "id", "in": "path" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["deleteMetricCheckIn", {
            name: "deleteMetricCheckIn",
            description: `Deletes a specific check-in for a metric.`,
            inputSchema: { "type": "object", "properties": { "metric_id": { "type": "string", "format": "uuid", "description": "UUID of the metric" }, "id": { "type": "string", "format": "uuid", "description": "UUID of the check-in to delete" } }, "required": ["metric_id", "id"] },
            method: "delete",
            pathTemplate: "/api/v1/metrics/{metric_id}/check_ins/{id}",
            executionParameters: [{ "name": "metric_id", "in": "path" }, { "name": "id", "in": "path" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["updateMetricCheckIn", {
            name: "updateMetricCheckIn",
            description: `Updates an existing check-in for a metric. Only the attributes specified in the request body will be updated. If no attributes are provided, the check-in will not be modified.
`,
            inputSchema: { "type": "object", "properties": { "metric_id": { "type": "string", "format": "uuid", "description": "UUID of the metric" }, "id": { "type": "string", "format": "uuid", "description": "UUID of the check-in to update" }, "If-Match": { "type": "string", "description": "The ETag value to ensure the resource's version matches" }, "requestBody": { "type": "string", "description": "Request body (content type: application/vnd.api+json)" } }, "required": ["metric_id", "id", "If-Match", "requestBody"] },
            method: "patch",
            pathTemplate: "/api/v1/metrics/{metric_id}/check_ins/{id}",
            executionParameters: [{ "name": "metric_id", "in": "path" }, { "name": "id", "in": "path" }, { "name": "If-Match", "in": "header" }],
            requestBodyContentType: "application/vnd.api+json",
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listPeople", {
            name: "listPeople",
            description: `Retrieves a paginated list of people in the organization with optional filtering criteria.`,
            inputSchema: { "type": "object", "properties": { "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" }, "filter[ids][]": { "type": "array", "items": { "type": "string", "format": "uuid" }, "description": "List of resource ids" } } },
            method: "get",
            pathTemplate: "/api/v1/people",
            executionParameters: [{ "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }, { "name": "filter[ids][]", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["listTodos", {
            name: "listTodos",
            description: `Retrieves a paginated list of todos with optional filtering criteria.`,
            inputSchema: { "type": "object", "properties": { "page[size]": { "type": "number", "minimum": 1, "maximum": 100, "default": 20, "description": "Maximum number of results to return per page" }, "page[number]": { "type": "number", "minimum": 1, "default": 1, "description": "Page number to retrieve (1-based)" }, "filter[created_after]": { "type": "string", "format": "date-time", "description": "Filter resources created on or after this UTC datetime. ISO 8601 format.\n" }, "filter[updated_after]": { "type": "string", "format": "date-time", "description": "Filter resources updated on or after this UTC datetime. ISO 8601 format.\n" }, "filter[ids][]": { "type": "array", "items": { "type": "string", "format": "uuid" }, "description": "List of resource ids" }, "filter[assignee_id]": { "type": "string", "format": "uuid", "description": "Filter by the person assigned to the resource" } } },
            method: "get",
            pathTemplate: "/api/v1/todos",
            executionParameters: [{ "name": "page[size]", "in": "query" }, { "name": "page[number]", "in": "query" }, { "name": "filter[created_after]", "in": "query" }, { "name": "filter[updated_after]", "in": "query" }, { "name": "filter[ids][]", "in": "query" }, { "name": "filter[assignee_id]", "in": "query" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
    ["getTodo", {
            name: "getTodo",
            description: `Retrieves a single todo by its UUID.`,
            inputSchema: { "type": "object", "properties": { "id": { "type": "string", "format": "uuid", "description": "UUID of the todo to retrieve" } }, "required": ["id"] },
            method: "get",
            pathTemplate: "/api/v1/todos/{id}",
            executionParameters: [{ "name": "id", "in": "path" }],
            requestBodyContentType: undefined,
            securityRequirements: [{ "OAuth2": ["read", "write"] }, { "bearerAuth": [] }]
        }],
]);
/**
 * Security schemes from the OpenAPI spec
 */
const securitySchemes = {
    "OAuth2": {
        "type": "oauth2",
        "description": "#### OAuth 2.0 Authorization Code Flow\nThis API uses the OAuth 2.0 Authorization Code flow to allow third-party applications to access user data securely.    \n1. **Redirect the user to the authorization endpoint:**\n\n    Provide the client ID, requested scopes, and a redirect URI.\n\n    ```\n    GET /api/v1/oauth/authorize?response_type=code&\n    client_id=YOUR_CLIENT_ID&\n    redirect_uri=https://yourapp.com/callback&\n    scope=metric.read%20metric_check_in.create&\n    state=YOUR_CSRF_TOKEN\n    ```\n\n    The user will be prompted to log in and authorize the application.\n\n2. **User is redirected back to your app:**\n\n    After the user authorizes the application, they will be redirected back to your application with an authorization code.\n\n    ```\n    https://yourapp.com/callback?code=AUTHORIZATION_CODE&state=YOUR_CSRF_TOKEN\n    ```\n\n3. **Exchange the authorization code for tokens:**\n\n    ```\n    POST /api/v1/oauth/token\n    Content-Type: application/x-www-form-urlencoded\n\n    grant_type=authorization_code&\n    code=AUTHORIZATION_CODE&\n    redirect_uri=https://yourapp.com/callback&\n    client_id=YOUR_CLIENT_ID&\n    client_secret=YOUR_CLIENT_SECRET\n    ```\n\n    This returns an access token (valid for 2 hours) and a refresh token.\n\n4. **Use the access token to access protected resources:**\n\n    ```\n    GET /api/v1/metrics\n    Authorization: Bearer ACCESS_TOKEN\n    ```\n\n5. **Refresh the access token when it expires:**\n\n    ```\n    POST /api/v1/oauth/token\n    Content-Type: application/x-www-form-urlencoded\n\n    grant_type=refresh_token&\n    refresh_token=YOUR_REFRESH_TOKEN&\n    client_id=YOUR_CLIENT_ID&\n    client_secret=YOUR_CLIENT_SECRET\n    ```\n\nSee [RFC 6749 - Section 4.1](https://www.rfc-editor.org/rfc/rfc6749#section-4.1) for protocol details.\n",
        "flows": {
            "authorizationCode": {
                "authorizationUrl": "https://2.strety.com/api/v1/oauth/authorize",
                "tokenUrl": "https://2.strety.com/api/v1/oauth/token",
                "refreshUrl": "https://2.strety.com/api/v1/oauth/token",
                "scopes": {
                    "read": "Read access to resources (default)",
                    "write": "Write access to resources"
                }
            }
        }
    },
    "bearerAuth": {
        "type": "http",
        "scheme": "bearer",
        "description": "Access token required in the `Authorization: Bearer` header for all protected endpoints.\n"
    }
};
server.setRequestHandler(ListToolsRequestSchema, async () => {
    const toolsForClient = Array.from(toolDefinitionMap.values()).map(def => ({
        name: def.name,
        description: def.description,
        inputSchema: def.inputSchema
    }));
    return { tools: toolsForClient };
});
server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name: toolName, arguments: toolArgs } = request.params;
    const toolDefinition = toolDefinitionMap.get(toolName);
    if (!toolDefinition) {
        console.error(`Error: Unknown tool requested: ${toolName}`);
        return { content: [{ type: "text", text: `Error: Unknown tool requested: ${toolName}` }] };
    }
    return await executeApiTool(toolName, toolDefinition, toolArgs ?? {}, securitySchemes);
});
/**
 * Refreshes an OAuth2 token using refresh token
 *
 * @param schemeName Name of the security scheme
 * @param refreshToken The refresh token
 * @returns Refreshed token or null if unable to refresh
 */
async function refreshOAuth2Token(schemeName, refreshToken) {
    try {
        const schemeNameUpper = schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
        const clientId = process.env[`OAUTH_CLIENT_ID_${schemeNameUpper}`];
        const clientSecret = process.env[`OAUTH_CLIENT_SECRET_${schemeNameUpper}`];
        if (!clientId || !clientSecret) {
            console.error(`Missing client credentials for OAuth2 refresh for '${schemeName}'`);
            return null;
        }
        console.error(`Refreshing OAuth2 token for '${schemeName}'`);
        const formData = new URLSearchParams();
        formData.append('grant_type', 'refresh_token');
        formData.append('refresh_token', refreshToken);
        const response = await axios({
            method: 'POST',
            url: 'https://2.strety.com/api/v1/oauth/token',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
            },
            data: formData.toString(),
            httpsAgent: new https.Agent({
                rejectUnauthorized: false
            })
        });
        if (response.data?.access_token) {
            const token = response.data.access_token;
            const newRefreshToken = response.data.refresh_token || refreshToken;
            const expiresIn = response.data.expires_in || 3600;
            // Update cache with new tokens
            if (typeof global.__oauthTokenCache === 'undefined') {
                global.__oauthTokenCache = {};
            }
            const cacheKey = `${schemeName}_${clientId}`;
            global.__oauthTokenCache[cacheKey] = {
                token,
                refreshToken: newRefreshToken,
                expiresAt: Date.now() + (expiresIn * 1000) - 60000
            };
            console.error(`Successfully refreshed OAuth2 token for '${schemeName}'`);
            return token;
        }
        else {
            console.error(`Failed to refresh OAuth2 token for '${schemeName}': No access_token in response`);
            return null;
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`Error refreshing OAuth2 token for '${schemeName}':`, errorMessage);
        return null;
    }
}
/**
 * Acquires an OAuth2 token using client credentials flow or refresh token
 *
 * @param schemeName Name of the security scheme
 * @param scheme OAuth2 security scheme
 * @returns Acquired token or null if unable to acquire
 */
async function acquireOAuth2Token(schemeName, scheme) {
    try {
        // Check if we have the necessary credentials
        const schemeNameUpper = schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
        const clientId = process.env[`OAUTH_CLIENT_ID_${schemeNameUpper}`];
        const clientSecret = process.env[`OAUTH_CLIENT_SECRET_${schemeNameUpper}`];
        const scopes = process.env[`OAUTH_SCOPES_${schemeNameUpper}`];
        if (!clientId || !clientSecret) {
            console.error(`Missing client credentials for OAuth2 scheme '${schemeName}'`);
            return null;
        }
        // Initialize token cache if needed
        if (typeof global.__oauthTokenCache === 'undefined') {
            global.__oauthTokenCache = {};
        }
        // Check if we have a cached token
        const cacheKey = `${schemeName}_${clientId}`;
        const cachedToken = global.__oauthTokenCache[cacheKey];
        const now = Date.now();
        if (cachedToken && cachedToken.expiresAt > now) {
            console.error(`Using cached OAuth2 token for '${schemeName}' (expires in ${Math.floor((cachedToken.expiresAt - now) / 1000)} seconds)`);
            return cachedToken.token;
        }
        // If token is expired but we have a refresh token, try to refresh
        if (cachedToken && cachedToken.refreshToken) {
            console.error(`Cached token expired, attempting refresh for '${schemeName}'`);
            const refreshedToken = await refreshOAuth2Token(schemeName, cachedToken.refreshToken);
            if (refreshedToken) {
                return refreshedToken;
            }
            console.error(`Token refresh failed for '${schemeName}', falling back to new token acquisition`);
        }
        // Determine token URL based on flow type
        let tokenUrl = '';
        if (scheme.flows?.clientCredentials?.tokenUrl) {
            tokenUrl = scheme.flows.clientCredentials.tokenUrl;
            console.error(`Using client credentials flow for '${schemeName}'`);
        }
        else if (scheme.flows?.password?.tokenUrl) {
            tokenUrl = scheme.flows.password.tokenUrl;
            console.error(`Using password flow for '${schemeName}'`);
        }
        else if (scheme.flows?.authorizationCode?.tokenUrl) {
            // Try client credentials flow using the authorization code token URL
            tokenUrl = scheme.flows.authorizationCode.tokenUrl;
            console.error(`Attempting client credentials flow using authorization code token URL for '${schemeName}'`);
        }
        else {
            console.error(`No supported OAuth2 flow found for '${schemeName}'`);
            return null;
        }
        // Prepare the token request
        let formData = new URLSearchParams();
        formData.append('grant_type', 'client_credentials');
        // Add scopes if specified
        if (scopes) {
            formData.append('scope', scopes);
        }
        console.error(`Requesting OAuth2 token from ${tokenUrl}`);
        // Make the token request
        const response = await axios({
            method: 'POST',
            url: tokenUrl,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
            },
            data: formData.toString(),
            // For development/testing - ignore SSL certificate issues
            httpsAgent: new https.Agent({
                rejectUnauthorized: false
            })
        });
        // Process the response
        if (response.data?.access_token) {
            const token = response.data.access_token;
            const refreshToken = response.data.refresh_token;
            const expiresIn = response.data.expires_in || 3600; // Default to 1 hour
            // Cache the token
            global.__oauthTokenCache[cacheKey] = {
                token,
                refreshToken,
                expiresAt: now + (expiresIn * 1000) - 60000 // Expire 1 minute early
            };
            console.error(`Successfully acquired OAuth2 token for '${schemeName}' (expires in ${expiresIn} seconds)`);
            if (refreshToken) {
                console.error(`Refresh token acquired for '${schemeName}' - automatic token refresh enabled`);
            }
            return token;
        }
        else {
            console.error(`Failed to acquire OAuth2 token for '${schemeName}': No access_token in response`);
            return null;
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`Error acquiring OAuth2 token for '${schemeName}':`, errorMessage);
        return null;
    }
}
/**
 * Executes an API tool with the provided arguments
 *
 * @param toolName Name of the tool to execute
 * @param definition Tool definition
 * @param toolArgs Arguments provided by the user
 * @param allSecuritySchemes Security schemes from the OpenAPI spec
 * @returns Call tool result
 */
async function executeApiTool(toolName, definition, toolArgs, allSecuritySchemes) {
    try {
        // Validate arguments against the input schema
        let validatedArgs;
        try {
            const zodSchema = getZodSchemaFromJsonSchema(definition.inputSchema, toolName);
            const argsToParse = (typeof toolArgs === 'object' && toolArgs !== null) ? toolArgs : {};
            validatedArgs = zodSchema.parse(argsToParse);
        }
        catch (error) {
            if (error instanceof ZodError) {
                const validationErrorMessage = `Invalid arguments for tool '${toolName}': ${error.errors.map(e => `${e.path.join('.')} (${e.code}): ${e.message}`).join(', ')}`;
                return { content: [{ type: 'text', text: validationErrorMessage }] };
            }
            else {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return { content: [{ type: 'text', text: `Internal error during validation setup: ${errorMessage}` }] };
            }
        }
        // Prepare URL, query parameters, headers, and request body
        let urlPath = definition.pathTemplate;
        const queryParams = {};
        const headers = { 'Accept': 'application/json' };
        let requestBodyData = undefined;
        // Apply parameters to the URL path, query, or headers
        definition.executionParameters.forEach((param) => {
            const value = validatedArgs[param.name];
            if (typeof value !== 'undefined' && value !== null) {
                if (param.in === 'path') {
                    urlPath = urlPath.replace(`{${param.name}}`, encodeURIComponent(String(value)));
                }
                else if (param.in === 'query') {
                    queryParams[param.name] = value;
                }
                else if (param.in === 'header') {
                    headers[param.name.toLowerCase()] = String(value);
                }
            }
        });
        // Ensure all path parameters are resolved
        if (urlPath.includes('{')) {
            throw new Error(`Failed to resolve path parameters: ${urlPath}`);
        }
        // Construct the full URL
        const requestUrl = API_BASE_URL ? `${API_BASE_URL}${urlPath}` : urlPath;
        // Handle request body if needed
        if (definition.requestBodyContentType && typeof validatedArgs['requestBody'] !== 'undefined') {
            requestBodyData = validatedArgs['requestBody'];
            headers['content-type'] = definition.requestBodyContentType;
        }
        // Apply security requirements if available
        // Security requirements use OR between array items and AND within each object
        const appliedSecurity = definition.securityRequirements?.find(req => {
            // Try each security requirement (combined with OR)
            return Object.entries(req).every(([schemeName, scopesArray]) => {
                const scheme = allSecuritySchemes[schemeName];
                if (!scheme)
                    return false;
                // API Key security (header, query, cookie)
                if (scheme.type === 'apiKey') {
                    return !!process.env[`API_KEY_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                }
                // HTTP security (basic, bearer)
                if (scheme.type === 'http') {
                    if (scheme.scheme?.toLowerCase() === 'bearer') {
                        return !!process.env[`BEARER_TOKEN_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                    }
                    else if (scheme.scheme?.toLowerCase() === 'basic') {
                        return !!process.env[`BASIC_USERNAME_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`] &&
                            !!process.env[`BASIC_PASSWORD_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                    }
                }
                // OAuth2 security
                if (scheme.type === 'oauth2') {
                    // Check for pre-existing token
                    if (process.env[`OAUTH_TOKEN_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`]) {
                        return true;
                    }
                    // Check for client credentials for auto-acquisition
                    if (process.env[`OAUTH_CLIENT_ID_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`] &&
                        process.env[`OAUTH_CLIENT_SECRET_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`]) {
                        // Verify we have a supported flow (including authorizationCode for client credentials attempt)
                        if (scheme.flows?.clientCredentials || scheme.flows?.password || scheme.flows?.authorizationCode) {
                            return true;
                        }
                    }
                    return false;
                }
                // OpenID Connect
                if (scheme.type === 'openIdConnect') {
                    return !!process.env[`OPENID_TOKEN_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                }
                return false;
            });
        });
        // If we found matching security scheme(s), apply them
        if (appliedSecurity) {
            // Apply each security scheme from this requirement (combined with AND)
            for (const [schemeName, scopesArray] of Object.entries(appliedSecurity)) {
                const scheme = allSecuritySchemes[schemeName];
                // API Key security
                if (scheme?.type === 'apiKey') {
                    const apiKey = process.env[`API_KEY_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                    if (apiKey) {
                        if (scheme.in === 'header') {
                            headers[scheme.name.toLowerCase()] = apiKey;
                            console.error(`Applied API key '${schemeName}' in header '${scheme.name}'`);
                        }
                        else if (scheme.in === 'query') {
                            queryParams[scheme.name] = apiKey;
                            console.error(`Applied API key '${schemeName}' in query parameter '${scheme.name}'`);
                        }
                        else if (scheme.in === 'cookie') {
                            // Add the cookie, preserving other cookies if they exist
                            headers['cookie'] = `${scheme.name}=${apiKey}${headers['cookie'] ? `; ${headers['cookie']}` : ''}`;
                            console.error(`Applied API key '${schemeName}' in cookie '${scheme.name}'`);
                        }
                    }
                }
                // HTTP security (Bearer or Basic)
                else if (scheme?.type === 'http') {
                    if (scheme.scheme?.toLowerCase() === 'bearer') {
                        const token = process.env[`BEARER_TOKEN_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                        if (token) {
                            headers['authorization'] = `Bearer ${token}`;
                            console.error(`Applied Bearer token for '${schemeName}'`);
                        }
                    }
                    else if (scheme.scheme?.toLowerCase() === 'basic') {
                        const username = process.env[`BASIC_USERNAME_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                        const password = process.env[`BASIC_PASSWORD_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                        if (username && password) {
                            headers['authorization'] = `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`;
                            console.error(`Applied Basic authentication for '${schemeName}'`);
                        }
                    }
                }
                // OAuth2 security
                else if (scheme?.type === 'oauth2') {
                    // Check for simple environment variables first (easier configuration)
                    let token = process.env.STRETY_API_TOKEN ||
                        process.env.STRETY_ACCESS_TOKEN ||
                        process.env.STRETY_BEARER_TOKEN ||
                        process.env[`OAUTH_TOKEN_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                    // If no token but we have client credentials, try to acquire a token
                    if (!token && (scheme.flows?.clientCredentials || scheme.flows?.password || scheme.flows?.authorizationCode)) {
                        console.error(`Attempting to acquire OAuth token for '${schemeName}'`);
                        token = (await acquireOAuth2Token(schemeName, scheme)) ?? '';
                    }
                    // Apply token if available
                    if (token) {
                        headers['authorization'] = `Bearer ${token}`;
                        console.error(`Applied OAuth2 token for '${schemeName}'`);
                        // List the scopes that were requested, if any
                        const scopes = scopesArray;
                        if (scopes && scopes.length > 0) {
                            console.error(`Requested scopes: ${scopes.join(', ')}`);
                        }
                    }
                }
                // OpenID Connect
                else if (scheme?.type === 'openIdConnect') {
                    const token = process.env[`OPENID_TOKEN_${schemeName.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase()}`];
                    if (token) {
                        headers['authorization'] = `Bearer ${token}`;
                        console.error(`Applied OpenID Connect token for '${schemeName}'`);
                        // List the scopes that were requested, if any
                        const scopes = scopesArray;
                        if (scopes && scopes.length > 0) {
                            console.error(`Requested scopes: ${scopes.join(', ')}`);
                        }
                    }
                }
            }
        }
        // Log warning if security is required but not available
        else if (definition.securityRequirements?.length > 0) {
            // First generate a more readable representation of the security requirements
            const securityRequirementsString = definition.securityRequirements
                .map(req => {
                const parts = Object.entries(req)
                    .map(([name, scopesArray]) => {
                    const scopes = scopesArray;
                    if (scopes.length === 0)
                        return name;
                    return `${name} (scopes: ${scopes.join(', ')})`;
                })
                    .join(' AND ');
                return `[${parts}]`;
            })
                .join(' OR ');
            console.warn(`Tool '${toolName}' requires security: ${securityRequirementsString}, but no suitable credentials found.`);
        }
        // Add required Accept header for Strety API
        headers['accept'] = 'application/vnd.api+json';
        // Prepare the axios request configuration
        const config = {
            method: definition.method.toUpperCase(),
            url: requestUrl,
            params: queryParams,
            headers: headers,
            ...(requestBodyData !== undefined && { data: requestBodyData }),
            // For development/testing - ignore SSL certificate issues
            httpsAgent: new https.Agent({
                rejectUnauthorized: false
            })
        };
        // Log request info to stderr (doesn't affect MCP output)
        console.error(`Executing tool "${toolName}": ${config.method} ${config.url}`);
        // Execute the request
        const response = await axios(config);
        // Process and format the response
        let responseText = '';
        const contentType = response.headers['content-type']?.toLowerCase() || '';
        console.error(`Response content-type: ${contentType}`);
        console.error(`Response data type: ${typeof response.data}`);
        // Handle JSON responses (including Strety's application/vnd.api+json)
        if ((contentType.includes('application/json') || contentType.includes('application/vnd.api+json')) &&
            typeof response.data === 'object' && response.data !== null) {
            try {
                responseText = JSON.stringify(response.data, null, 2);
            }
            catch (e) {
                responseText = "[Stringify Error]";
            }
        }
        // Handle string responses
        else if (typeof response.data === 'string') {
            responseText = response.data;
        }
        // Handle other response types - try to stringify objects
        else if (response.data !== undefined && response.data !== null) {
            if (typeof response.data === 'object') {
                try {
                    responseText = JSON.stringify(response.data, null, 2);
                }
                catch (e) {
                    responseText = String(response.data);
                }
            }
            else {
                responseText = String(response.data);
            }
        }
        // Handle empty responses
        else {
            responseText = `(Status: ${response.status} - No body content)`;
        }
        // Return formatted response
        return {
            content: [
                {
                    type: "text",
                    text: `API Response (Status: ${response.status}):\n${responseText}`
                }
            ],
        };
    }
    catch (error) {
        // Handle errors during execution
        let errorMessage;
        // Format Axios errors specially
        if (axios.isAxiosError(error)) {
            errorMessage = formatApiError(error);
        }
        // Handle standard errors
        else if (error instanceof Error) {
            errorMessage = error.message;
        }
        // Handle unexpected error types
        else {
            errorMessage = 'Unexpected error: ' + String(error);
        }
        // Log error to stderr
        console.error(`Error during execution of tool '${toolName}':`, errorMessage);
        // Return error message to client
        return { content: [{ type: "text", text: errorMessage }] };
    }
}
/**
 * Main function to start the server
 */
async function main() {
    // Check command line arguments for transport mode
    const args = process.argv.slice(2);
    const transportArg = args.find(arg => arg.startsWith('--transport='));
    const transport = transportArg ? transportArg.split('=')[1] : 'stdio';
    if (transport === 'web') {
        // Set up Web Server transport
        try {
            await setupWebServer(server, 3000);
        }
        catch (error) {
            console.error("Error setting up web server:", error);
            process.exit(1);
        }
    }
    else {
        // Set up stdio transport (default)
        const transport = new StdioServerTransport();
        await server.connect(transport);
        console.error("MCP server running on stdio transport");
    }
}
/**
 * Cleanup function for graceful shutdown
 */
async function cleanup() {
    console.error("Shutting down MCP server...");
    process.exit(0);
}
// Register signal handlers
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
// Start the server
main().catch((error) => {
    console.error("Fatal error in main execution:", error);
    process.exit(1);
});
/**
 * Formats API errors for better readability
 *
 * @param error Axios error
 * @returns Formatted error message
 */
function formatApiError(error) {
    let message = 'API request failed.';
    if (error.response) {
        message = `API Error: Status ${error.response.status} (${error.response.statusText || 'Status text not available'}). `;
        const responseData = error.response.data;
        const MAX_LEN = 200;
        if (typeof responseData === 'string') {
            message += `Response: ${responseData.substring(0, MAX_LEN)}${responseData.length > MAX_LEN ? '...' : ''}`;
        }
        else if (responseData) {
            try {
                const jsonString = JSON.stringify(responseData);
                message += `Response: ${jsonString.substring(0, MAX_LEN)}${jsonString.length > MAX_LEN ? '...' : ''}`;
            }
            catch {
                message += 'Response: [Could not serialize data]';
            }
        }
        else {
            message += 'No response body received.';
        }
    }
    else if (error.request) {
        message = 'API Network Error: No response received from server.';
        if (error.code)
            message += ` (Code: ${error.code})`;
    }
    else {
        message += `API Request Setup Error: ${error.message}`;
    }
    return message;
}
/**
 * Converts a JSON Schema to a Zod schema for runtime validation
 *
 * @param jsonSchema JSON Schema
 * @param toolName Tool name for error reporting
 * @returns Zod schema
 */
function getZodSchemaFromJsonSchema(jsonSchema, toolName) {
    if (typeof jsonSchema !== 'object' || jsonSchema === null) {
        return z.object({}).passthrough();
    }
    try {
        const zodSchemaString = jsonSchemaToZod(jsonSchema);
        const zodSchema = eval(zodSchemaString);
        if (typeof zodSchema?.parse !== 'function') {
            throw new Error('Eval did not produce a valid Zod schema.');
        }
        return zodSchema;
    }
    catch (err) {
        console.error(`Failed to generate/evaluate Zod schema for '${toolName}':`, err);
        return z.object({}).passthrough();
    }
}
//# sourceMappingURL=index.js.map