{"version": 3, "file": "web-server.js", "sourceRoot": "", "sources": ["../src/web-server.ts"], "names": [], "mappings": "AACA;;EAEE;AACF,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAC1C,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,EAAE,IAAI,IAAI,EAAE,MAAM,MAAM,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,oCAAoC,CAAC;AAW1E,wCAAwC;AACxC,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAEzD;;EAEE;AACF,MAAM,YAAY;IACV,UAAU,CAAS;IACnB,MAAM,CAAkB;IACxB,UAAU,CAAS;IAE3B,OAAO,CAAc;IACrB,OAAO,CAA0B;IACjC,SAAS,CAAqC;IAE9C,YAAY,UAAkB,EAAE,MAAuB;QACrD,IAAI,CAAC,UAAU,GAAG,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,8BAA8B;QAC9B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;YACvB,OAAO,CAAC,KAAK,CAAC,sCAAsC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;YACvE,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,gCAAgC;QAChC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACzB,KAAK,EAAE,UAAU;YACjB,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,cAAc,IAAI,CAAC,UAAU,EAAE;SACxD,CAAC,CAAC;QAEH,4EAA4E;QAC5E,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACzB,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,IAAI,EAAE,YAAY;gBAClB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC;SACH,CAAC,CAAC;QAEH,8BAA8B;QAC9B,MAAM,IAAI,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE;gBACN,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE;oBACV,SAAS,EAAE,IAAI,CAAC,UAAU;oBAC1B,UAAU,EAAE,WAAW;oBACvB,aAAa,EAAE,cAAc;iBAC9B;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,CAAU;QAChC,IAAI,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAEhE,mDAAmD;YACnD,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtC,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBAChD,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,GAAG,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;gBAC3B,OAAO,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;gBACrE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,GAAG,CAAC,CAAC;YACpE,CAAC;YAED,iHAAiH;YACjH,MAAM,SAAS,GAAG,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;YAClD,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,qCAAqC;YAC1E,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC;YAE1C,IAAI,aAA6B,CAAC;YAElC,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;gBACvB,UAAU;gBACV,aAAa,GAAG;oBACd,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB,CAAC;YACJ,CAAC;iBAAM,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,eAAe;gBACf,aAAa,GAAG;oBACd,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,EAAE,CAAC;gBAC1D,qDAAqD;gBACrD,aAAa,GAAG;oBACd,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,+CAA+C;oBAC5D,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;iBAClB,CAAC;YACJ,CAAC;iBAAM,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,QAAQ,EAAE,CAAC;gBAC5C,oEAAoE;gBACpE,aAAa,GAAG;oBACd,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,IAAI;oBACR,KAAK,EAAE,IAAI,CAAC,KAAK;iBAClB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;gBAClD,OAAO,CAAC,CAAC,IAAI,CAAC;oBACZ,KAAK,EAAE,oCAAoC;oBAC3C,OAAO,EAAE,sGAAsG;iBAChH,EAAE,GAAG,CAAC,CAAC;YACV,CAAC;YAED,0CAA0C;YAC1C,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBAEnE,iCAAiC;gBACjC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;oBACjC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;oBAC5C,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,GAAG,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAC;gBACxD,+DAA+D;gBAC/D,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC,SAAS,CAAC,aAAoB,CAAC,CAAC;oBACrC,OAAO,CAAC,CAAC,IAAI,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;gBACvD,CAAC;gBACD,OAAO,CAAC,CAAC,IAAI,CAAC;oBACZ,KAAK,EAAE,2BAA2B;oBAClC,OAAO,EAAE,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;iBAClF,EAAE,GAAG,CAAC,CAAC;YACV,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1E,CAAC;YACD,OAAO,CAAC,CAAC,IAAI,CAAC;gBACZ,KAAK,EAAE,0BAA0B;gBACjC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAuB;QAChC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YACzB,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;SAC9B,CAAC,CAAC;IACL,CAAC;CACA;AAED;;;;;;EAME;AACF,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,MAAc,EAAE,IAAI,GAAG,IAAI;IAChE,kBAAkB;IAClB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IAEvB,cAAc;IACd,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;IAErB,4CAA4C;IAC5C,MAAM,UAAU,GAAwC,EAAE,CAAC;IAE3D,qCAAqC;IACrC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;QACvB,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;IAEH,yCAAyC;IACzC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE;QACpB,OAAO,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;YACnC,uBAAuB;YACvB,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAC5D,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;YAEtC,OAAO,CAAC,KAAK,CAAC,mCAAmC,SAAS,EAAE,CAAC,CAAC;YAE9D,sBAAsB;YACtB,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;YAElC,oCAAoC;YACpC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;gBACvB,OAAO,CAAC,KAAK,CAAC,qCAAqC,SAAS,EAAE,CAAC,CAAC;gBAChE,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,iDAAiD;YACjD,IAAI,CAAC;gBACH,SAAS,CAAC,SAAS,GAAG,KAAK,EAAE,OAAuB,EAAE,EAAE;oBACtD,IAAI,CAAC;wBACH,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAG,OAAe,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC;wBAC9E,kEAAkE;wBAClE,sEAAsE;oBACxE,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;oBACtD,CAAC;gBACH,CAAC,CAAC;gBAEF,4BAA4B;gBAC5B,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/E,CAAC;YAED,qCAAqC;YACrC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACtB,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,4CAA4C;IAC5C,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,GAAG,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QAExC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qDAAqD,EAAE,EAAE,GAAG,CAAC,CAAC;QACvF,CAAC;QAED,OAAO,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,2CAA2C;IAC3C,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;QACxB,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;QACjE,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;YAE9C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEjD,uDAAuD;YACvD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YAClC,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;oBAClB,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAE1C,2CAA2C;oBAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;oBACjD,IAAI,WAAW,GAAG,YAAY,CAAC;oBAE/B,QAAQ,GAAG,EAAE,CAAC;wBACZ,KAAK,OAAO;4BAAE,WAAW,GAAG,WAAW,CAAC;4BAAC,MAAM;wBAC/C,KAAK,MAAM;4BAAE,WAAW,GAAG,UAAU,CAAC;4BAAC,MAAM;wBAC7C,KAAK,KAAK;4BAAE,WAAW,GAAG,iBAAiB,CAAC;4BAAC,MAAM;wBACnD,KAAK,OAAO;4BAAE,WAAW,GAAG,kBAAkB,CAAC;4BAAC,MAAM;wBACtD,KAAK,MAAM;4BAAE,WAAW,GAAG,WAAW,CAAC;4BAAC,MAAM;wBAC9C,KAAK,MAAM;4BAAE,WAAW,GAAG,YAAY,CAAC;4BAAC,MAAM;wBAC/C,KAAK,MAAM;4BAAE,WAAW,GAAG,eAAe,CAAC;4BAAC,MAAM;oBACpD,CAAC;oBAED,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE;wBAC3B,OAAO,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE;qBACzC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,gCAAgC;gBAChC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;YACjD,OAAO,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,mBAAmB;IACnB,KAAK,CAAC;QACJ,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,IAAI;KACL,EAAE,CAAC,IAAI,EAAE,EAAE;QACV,OAAO,CAAC,KAAK,CAAC,8CAA8C,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACzE,OAAO,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;QACnE,OAAO,CAAC,KAAK,CAAC,yCAAyC,IAAI,CAAC,IAAI,yCAAyC,CAAC,CAAC;QAC3G,OAAO,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACX,CAAC"}