#!/usr/bin/env node
/**
 * MCP Server generated from OpenAPI spec for strety-mcp-server v1.0.0
 * Generated on: 2025-08-01T19:09:30.033Z
 */
/**
 * Server configuration
 */
export declare const SERVER_NAME = "strety-mcp-server";
export declare const SERVER_VERSION = "1.0.0";
export declare const API_BASE_URL = "https://2.strety.com";
/**
 * Type definition for cached OAuth tokens
 */
interface TokenCacheEntry {
    token: string;
    refreshToken?: string;
    expiresAt: number;
}
/**
 * Declare global __oauthTokenCache property for TypeScript
 */
declare global {
    var __oauthTokenCache: Record<string, TokenCacheEntry> | undefined;
}
export {};
