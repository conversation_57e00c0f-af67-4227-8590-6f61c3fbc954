# OAuth2 Setup Guide for Strety MCP Server

**✅ WORKING SOLUTION** - This guide provides the tested, working OAuth2 setup process for the Strety MCP server.

## 🎯 Quick Summary

1. **Get OAuth2 credentials** from Strety
2. **Configure redirect URI**: `https://oauth.pstmn.io/v1/callback`
3. **Run setup wizard**: `npm run oauth2:simple`
4. **Authorize in browser** and copy the code
5. **To<PERSON><PERSON> automatically configured** and tested

## 📋 Prerequisites

### Required from Strety Account
- **Client ID** (e.g., `_9kkNXWvkKRpUMnqEStosWbZx72ru52FilDeANulRCo`)
- **Client Secret** (e.g., `FOG7i3rs5CLHENKKWncn5iMXvPtVo-rjSsgIsHhNnuo`)
- **OAuth2 Application** configured in your Strety account

### Required Redirect URI Configuration
In your Strety OAuth2 application settings, add:
```
https://oauth.pstmn.io/v1/callback
```

**⚠️ Important**: Strety requires HTTPS redirect URIs. The Postman OAuth callback service provides a secure HTTPS endpoint that displays the authorization code.

## 🚀 Setup Methods

### Method 1: Automated Setup (✅ WORKING)

**Use this method** - it's tested and working:

```bash
# 1. Install and build
npm install
npm run build

# 2. Add your credentials to .env file
echo "OAUTH_CLIENT_ID_OAUTH2=your_client_id_here" >> .env
echo "OAUTH_CLIENT_SECRET_OAUTH2=your_client_secret_here" >> .env

# 3. Run the working OAuth2 setup wizard
npm run oauth2:simple
```

**What the wizard does:**
1. ✅ Generates authorization URL with correct HTTPS redirect URI
2. ✅ Guides you through browser authorization
3. ✅ Prompts for authorization code from redirect page
4. ✅ Automatically exchanges code for tokens
5. ✅ Tests the tokens with actual API call
6. ✅ Shows you exactly what to add to your configuration

### Method 2: Manual Setup (If Needed)

If the automated setup doesn't work, follow these manual steps:

#### Step 1: Configure Strety OAuth2 App
1. Go to your Strety OAuth2 application settings
2. Add redirect URI: `https://oauth.pstmn.io/v1/callback`
3. Save the configuration

#### Step 2: Get Authorization Code

Open this URL in your browser (replace `YOUR_CLIENT_ID`):

```
https://2.strety.com/api/v1/oauth/authorize?response_type=code&client_id=YOUR_CLIENT_ID&redirect_uri=https%3A%2F%2Foauth.pstmn.io%2Fv1%2Fcallback&scope=read%20write&state=manual-setup
```

#### Step 3: Extract Code from Redirect Page

After authorization, you'll be redirected to a page showing:
```
Authorization code: YOUR_AUTHORIZATION_CODE_HERE
```

Copy the authorization code.

#### Step 4: Exchange Code for Tokens

```bash
curl -X POST https://2.strety.com/api/v1/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=YOUR_AUTHORIZATION_CODE&redirect_uri=https://oauth.pstmn.io/v1/callback&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET"
```

#### Step 5: Save Tokens

Add the tokens to your MCP configuration or `.env` file:

```env
OAUTH_TOKEN_OAUTH2=your_access_token_here
OAUTH_REFRESH_TOKEN_OAUTH2=your_refresh_token_here
```

## 🔄 Token Management

### Automatic Refresh (✅ Working)

The MCP server automatically handles token refresh:
- ✅ **Access tokens**: Valid for 2 hours
- ✅ **Refresh tokens**: Used for automatic renewal
- ✅ **Automatic detection**: Server detects expired tokens
- ✅ **Seamless refresh**: No manual intervention needed

### Manual Token Refresh

When tokens expire, refresh manually with this **working** curl command:

```bash
curl -X POST https://2.strety.com/api/v1/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=refresh_token&refresh_token=YOUR_REFRESH_TOKEN&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET"
```

**Example with real values:**
```bash
curl -X POST https://2.strety.com/api/v1/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=refresh_token&refresh_token=phF-ItL7kVv-hWUBYKK6WsHW9aj8rEAjAbTB6w-B1zU&client_id=_9kkNXWvkKRpUMnqEStosWbZx72ru52FilDeANulRCo&client_secret=FOG7i3rs5CLHENKKWncn5iMXvPtVo-rjSsgIsHhNnuo"
```

## Environment Variables

Your final `.env` file should look like:

```env
# Server configuration
PORT=3000
LOG_LEVEL=info

# OAuth2 client credentials
OAUTH_CLIENT_ID_OAUTH2=your_client_id_here
OAUTH_CLIENT_SECRET_OAUTH2=your_client_secret_here
OAUTH_SCOPES_OAUTH2=read,write

# OAuth2 tokens (from authorization flow)
OAUTH_TOKEN_OAUTH2=your_access_token_here
OAUTH_REFRESH_TOKEN_OAUTH2=your_refresh_token_here
```

## Testing Authentication

Test that authentication works:

```bash
# Build and start the server
npm run build
npm start

# In another terminal, test a tool call
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}' | node build/index.js
```

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**
   - Ensure your OAuth2 app is configured with redirect URI: `http://localhost:3001/oauth/callback`

2. **"Invalid client credentials"**
   - Verify your client ID and secret are correct
   - Check for extra spaces or characters

3. **"Token expired"**
   - The server should automatically refresh tokens
   - If refresh fails, re-run the OAuth2 setup

4. **"Scope insufficient"**
   - Ensure you're requesting `read write` scopes during authorization

### Debug Mode

Enable debug logging by setting:

```env
LOG_LEVEL=debug
```

This will show detailed OAuth2 token acquisition and refresh attempts.

## Security Notes

- **Keep tokens secure**: Never commit `.env` files to version control
- **Refresh token rotation**: Some OAuth2 providers rotate refresh tokens - the server handles this automatically
- **Token expiration**: Access tokens typically expire in 1-2 hours, refresh tokens last much longer
- **Revocation**: If tokens are compromised, revoke them in your Strety OAuth2 app settings

## For Raycast Configuration

Once OAuth2 is set up, configure Raycast MCP with:

```json
{
  "mcpServers": {
    "strety": {
      "command": "node",
      "args": ["build/index.js"],
      "cwd": "/path/to/strety-mcp-server"
    }
  }
}
```

The server will automatically handle authentication using the configured tokens.
