#!/usr/bin/env node

/**
 * Simple OAuth2 Setup for Strety MCP Server
 * 
 * Uses a public redirect URI service to handle HTTPS requirement
 */

import dotenv from 'dotenv';
import axios from 'axios';
import https from 'https';

dotenv.config();

const CLIENT_ID = process.env.OAUTH_CLIENT_ID_OAUTH2;
const CLIENT_SECRET = process.env.OAUTH_CLIENT_SECRET_OAUTH2;

// Use a public OAuth2 redirect service that supports HTTPS
const REDIRECT_URI = 'https://oauth.pstmn.io/v1/callback';

async function simpleOAuth2Setup() {
  console.log('🔐 Strety MCP Server - Simple OAuth2 Setup\n');
  
  if (!CLIENT_ID || !CLIENT_SECRET) {
    console.error('❌ Missing OAuth2 credentials!');
    console.error('Please set OAUTH_CLIENT_ID_OAUTH2 and OAUTH_CLIENT_SECRET_OAUTH2 in your .env file');
    process.exit(1);
  }
  
  console.log('✅ OAuth2 credentials found');
  console.log(`Client ID: ${CLIENT_ID}`);
  console.log(`Client Secret: ${CLIENT_SECRET.substring(0, 10)}...\n`);
  
  // Step 1: Configure Strety OAuth2 App
  console.log('📋 Step 1: Configure Your Strety OAuth2 Application');
  console.log('═'.repeat(60));
  console.log('1. Go to your Strety OAuth2 application settings');
  console.log('2. Add this redirect URI: ' + REDIRECT_URI);
  console.log('3. Save the configuration');
  console.log('');
  
  // Step 2: Authorization URL
  console.log('📋 Step 2: Get Authorization Code');
  console.log('═'.repeat(60));
  
  const authUrl = `https://2.strety.com/api/v1/oauth/authorize?response_type=code&client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&scope=read%20write&state=mcp-setup-${Date.now()}`;
  
  console.log('1. Open this URL in your browser:');
  console.log('\n' + authUrl + '\n');
  
  console.log('2. After authorization, you\'ll be redirected to a page showing:');
  console.log('   "Authorization code: YOUR_CODE_HERE"');
  console.log('');
  console.log('3. Copy the authorization code from that page');
  console.log('');
  
  // Step 3: Interactive token exchange
  console.log('🤖 Interactive Token Exchange');
  console.log('═'.repeat(60));
  console.log('Enter the authorization code from the redirect page:');
  
  process.stdin.setEncoding('utf8');
  process.stdin.on('readable', async () => {
    const chunk = process.stdin.read();
    if (chunk !== null) {
      const authCode = chunk.trim();
      if (authCode && authCode.length > 0) {
        await exchangeCodeForTokens(authCode);
      } else {
        console.log('\n❌ No authorization code provided');
        console.log('Please run the script again and enter the code when prompted');
      }
      process.exit(0);
    }
  });
}

async function exchangeCodeForTokens(authCode) {
  console.log('\n🔄 Exchanging authorization code for tokens...');
  
  try {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'authorization_code');
    formData.append('code', authCode);
    formData.append('redirect_uri', REDIRECT_URI);
    formData.append('client_id', CLIENT_ID);
    formData.append('client_secret', CLIENT_SECRET);
    
    const response = await axios({
      method: 'POST',
      url: 'https://2.strety.com/api/v1/oauth/token',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      data: formData.toString(),
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
    
    const { access_token, refresh_token, expires_in, token_type } = response.data;
    
    if (access_token) {
      console.log('✅ Tokens acquired successfully!\n');
      
      console.log('📋 Add these to your .env file:');
      console.log('═'.repeat(40));
      console.log(`OAUTH_TOKEN_OAUTH2=${access_token}`);
      if (refresh_token) {
        console.log(`OAUTH_REFRESH_TOKEN_OAUTH2=${refresh_token}`);
      }
      console.log('');
      
      console.log('📊 Token Information:');
      console.log(`Token Type: ${token_type || 'Bearer'}`);
      console.log(`Expires In: ${expires_in || 'Unknown'} seconds (${Math.round((expires_in || 7200) / 3600)} hours)`);
      console.log(`Refresh Token: ${refresh_token ? 'Yes' : 'No'}`);
      
      if (refresh_token) {
        console.log('\n✅ Refresh token available - automatic token renewal enabled!');
        console.log('The MCP server will automatically refresh tokens when they expire.');
      } else {
        console.log('\n⚠️  No refresh token - you may need to re-authorize when the token expires.');
      }
      
      console.log('\n🎉 Setup complete! Test with: npm start');
      
      // Test the token immediately
      console.log('\n🧪 Testing token...');
      await testToken(access_token);
      
    } else {
      console.error('❌ No access token in response');
      console.error('Response:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Token exchange failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response:`, error.response.data);
    } else {
      console.error(error.message);
    }
    
    console.log('\n💡 Troubleshooting:');
    console.log('1. Make sure the authorization code is correct and not expired');
    console.log('2. Verify your client ID and secret are correct');
    console.log('3. Check that the redirect URI is configured in Strety: ' + REDIRECT_URI);
    console.log('4. Ensure you completed the authorization step in your browser');
  }
}

async function testToken(accessToken) {
  try {
    const response = await axios({
      method: 'GET',
      url: 'https://2.strety.com/api/v1/people',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      },
      params: {
        'page[size]': 1
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
    
    console.log('✅ Token test successful! API is accessible.');
    console.log(`✅ Found ${response.data.data?.length || 0} people in first page`);
    
  } catch (error) {
    console.error('❌ Token test failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Response:`, error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user');
  process.exit(0);
});

simpleOAuth2Setup().catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
